// Generated by gen_tick.py. DO NOT EDIT.

#[cfg(feature = "tick-hz-1")]
pub const TICK_HZ: u64 = 1;
#[cfg(feature = "tick-hz-2")]
pub const TICK_HZ: u64 = 2;
#[cfg(feature = "tick-hz-4")]
pub const TICK_HZ: u64 = 4;
#[cfg(feature = "tick-hz-8")]
pub const TICK_HZ: u64 = 8;
#[cfg(feature = "tick-hz-10")]
pub const TICK_HZ: u64 = 10;
#[cfg(feature = "tick-hz-16")]
pub const TICK_HZ: u64 = 16;
#[cfg(feature = "tick-hz-32")]
pub const TICK_HZ: u64 = 32;
#[cfg(feature = "tick-hz-64")]
pub const TICK_HZ: u64 = 64;
#[cfg(feature = "tick-hz-100")]
pub const TICK_HZ: u64 = 100;
#[cfg(feature = "tick-hz-128")]
pub const TICK_HZ: u64 = 128;
#[cfg(feature = "tick-hz-256")]
pub const TICK_HZ: u64 = 256;
#[cfg(feature = "tick-hz-512")]
pub const TICK_HZ: u64 = 512;
#[cfg(feature = "tick-hz-1_000")]
pub const TICK_HZ: u64 = 1_000;
#[cfg(feature = "tick-hz-1_024")]
pub const TICK_HZ: u64 = 1_024;
#[cfg(feature = "tick-hz-2_000")]
pub const TICK_HZ: u64 = 2_000;
#[cfg(feature = "tick-hz-2_048")]
pub const TICK_HZ: u64 = 2_048;
#[cfg(feature = "tick-hz-4_000")]
pub const TICK_HZ: u64 = 4_000;
#[cfg(feature = "tick-hz-4_096")]
pub const TICK_HZ: u64 = 4_096;
#[cfg(feature = "tick-hz-8_000")]
pub const TICK_HZ: u64 = 8_000;
#[cfg(feature = "tick-hz-8_192")]
pub const TICK_HZ: u64 = 8_192;
#[cfg(feature = "tick-hz-10_000")]
pub const TICK_HZ: u64 = 10_000;
#[cfg(feature = "tick-hz-16_000")]
pub const TICK_HZ: u64 = 16_000;
#[cfg(feature = "tick-hz-16_384")]
pub const TICK_HZ: u64 = 16_384;
#[cfg(feature = "tick-hz-20_000")]
pub const TICK_HZ: u64 = 20_000;
#[cfg(feature = "tick-hz-32_000")]
pub const TICK_HZ: u64 = 32_000;
#[cfg(feature = "tick-hz-32_768")]
pub const TICK_HZ: u64 = 32_768;
#[cfg(feature = "tick-hz-40_000")]
pub const TICK_HZ: u64 = 40_000;
#[cfg(feature = "tick-hz-64_000")]
pub const TICK_HZ: u64 = 64_000;
#[cfg(feature = "tick-hz-65_536")]
pub const TICK_HZ: u64 = 65_536;
#[cfg(feature = "tick-hz-80_000")]
pub const TICK_HZ: u64 = 80_000;
#[cfg(feature = "tick-hz-100_000")]
pub const TICK_HZ: u64 = 100_000;
#[cfg(feature = "tick-hz-128_000")]
pub const TICK_HZ: u64 = 128_000;
#[cfg(feature = "tick-hz-131_072")]
pub const TICK_HZ: u64 = 131_072;
#[cfg(feature = "tick-hz-160_000")]
pub const TICK_HZ: u64 = 160_000;
#[cfg(feature = "tick-hz-256_000")]
pub const TICK_HZ: u64 = 256_000;
#[cfg(feature = "tick-hz-262_144")]
pub const TICK_HZ: u64 = 262_144;
#[cfg(feature = "tick-hz-320_000")]
pub const TICK_HZ: u64 = 320_000;
#[cfg(feature = "tick-hz-512_000")]
pub const TICK_HZ: u64 = 512_000;
#[cfg(feature = "tick-hz-524_288")]
pub const TICK_HZ: u64 = 524_288;
#[cfg(feature = "tick-hz-640_000")]
pub const TICK_HZ: u64 = 640_000;
#[cfg(feature = "tick-hz-1_000_000")]
pub const TICK_HZ: u64 = 1_000_000;
#[cfg(feature = "tick-hz-1_024_000")]
pub const TICK_HZ: u64 = 1_024_000;
#[cfg(feature = "tick-hz-1_048_576")]
pub const TICK_HZ: u64 = 1_048_576;
#[cfg(feature = "tick-hz-1_280_000")]
pub const TICK_HZ: u64 = 1_280_000;
#[cfg(feature = "tick-hz-2_000_000")]
pub const TICK_HZ: u64 = 2_000_000;
#[cfg(feature = "tick-hz-2_048_000")]
pub const TICK_HZ: u64 = 2_048_000;
#[cfg(feature = "tick-hz-2_097_152")]
pub const TICK_HZ: u64 = 2_097_152;
#[cfg(feature = "tick-hz-2_560_000")]
pub const TICK_HZ: u64 = 2_560_000;
#[cfg(feature = "tick-hz-3_000_000")]
pub const TICK_HZ: u64 = 3_000_000;
#[cfg(feature = "tick-hz-4_000_000")]
pub const TICK_HZ: u64 = 4_000_000;
#[cfg(feature = "tick-hz-4_096_000")]
pub const TICK_HZ: u64 = 4_096_000;
#[cfg(feature = "tick-hz-4_194_304")]
pub const TICK_HZ: u64 = 4_194_304;
#[cfg(feature = "tick-hz-5_120_000")]
pub const TICK_HZ: u64 = 5_120_000;
#[cfg(feature = "tick-hz-6_000_000")]
pub const TICK_HZ: u64 = 6_000_000;
#[cfg(feature = "tick-hz-8_000_000")]
pub const TICK_HZ: u64 = 8_000_000;
#[cfg(feature = "tick-hz-8_192_000")]
pub const TICK_HZ: u64 = 8_192_000;
#[cfg(feature = "tick-hz-8_388_608")]
pub const TICK_HZ: u64 = 8_388_608;
#[cfg(feature = "tick-hz-9_000_000")]
pub const TICK_HZ: u64 = 9_000_000;
#[cfg(feature = "tick-hz-10_000_000")]
pub const TICK_HZ: u64 = 10_000_000;
#[cfg(feature = "tick-hz-10_240_000")]
pub const TICK_HZ: u64 = 10_240_000;
#[cfg(feature = "tick-hz-12_000_000")]
pub const TICK_HZ: u64 = 12_000_000;
#[cfg(feature = "tick-hz-16_000_000")]
pub const TICK_HZ: u64 = 16_000_000;
#[cfg(feature = "tick-hz-16_384_000")]
pub const TICK_HZ: u64 = 16_384_000;
#[cfg(feature = "tick-hz-16_777_216")]
pub const TICK_HZ: u64 = 16_777_216;
#[cfg(feature = "tick-hz-18_000_000")]
pub const TICK_HZ: u64 = 18_000_000;
#[cfg(feature = "tick-hz-20_000_000")]
pub const TICK_HZ: u64 = 20_000_000;
#[cfg(feature = "tick-hz-20_480_000")]
pub const TICK_HZ: u64 = 20_480_000;
#[cfg(feature = "tick-hz-24_000_000")]
pub const TICK_HZ: u64 = 24_000_000;
#[cfg(feature = "tick-hz-30_000_000")]
pub const TICK_HZ: u64 = 30_000_000;
#[cfg(feature = "tick-hz-32_000_000")]
pub const TICK_HZ: u64 = 32_000_000;
#[cfg(feature = "tick-hz-32_768_000")]
pub const TICK_HZ: u64 = 32_768_000;
#[cfg(feature = "tick-hz-36_000_000")]
pub const TICK_HZ: u64 = 36_000_000;
#[cfg(feature = "tick-hz-40_000_000")]
pub const TICK_HZ: u64 = 40_000_000;
#[cfg(feature = "tick-hz-40_960_000")]
pub const TICK_HZ: u64 = 40_960_000;
#[cfg(feature = "tick-hz-48_000_000")]
pub const TICK_HZ: u64 = 48_000_000;
#[cfg(feature = "tick-hz-50_000_000")]
pub const TICK_HZ: u64 = 50_000_000;
#[cfg(feature = "tick-hz-60_000_000")]
pub const TICK_HZ: u64 = 60_000_000;
#[cfg(feature = "tick-hz-64_000_000")]
pub const TICK_HZ: u64 = 64_000_000;
#[cfg(feature = "tick-hz-65_536_000")]
pub const TICK_HZ: u64 = 65_536_000;
#[cfg(feature = "tick-hz-70_000_000")]
pub const TICK_HZ: u64 = 70_000_000;
#[cfg(feature = "tick-hz-72_000_000")]
pub const TICK_HZ: u64 = 72_000_000;
#[cfg(feature = "tick-hz-80_000_000")]
pub const TICK_HZ: u64 = 80_000_000;
#[cfg(feature = "tick-hz-81_920_000")]
pub const TICK_HZ: u64 = 81_920_000;
#[cfg(feature = "tick-hz-90_000_000")]
pub const TICK_HZ: u64 = 90_000_000;
#[cfg(feature = "tick-hz-96_000_000")]
pub const TICK_HZ: u64 = 96_000_000;
#[cfg(feature = "tick-hz-100_000_000")]
pub const TICK_HZ: u64 = 100_000_000;
#[cfg(feature = "tick-hz-110_000_000")]
pub const TICK_HZ: u64 = 110_000_000;
#[cfg(feature = "tick-hz-120_000_000")]
pub const TICK_HZ: u64 = 120_000_000;
#[cfg(feature = "tick-hz-128_000_000")]
pub const TICK_HZ: u64 = 128_000_000;
#[cfg(feature = "tick-hz-130_000_000")]
pub const TICK_HZ: u64 = 130_000_000;
#[cfg(feature = "tick-hz-131_072_000")]
pub const TICK_HZ: u64 = 131_072_000;
#[cfg(feature = "tick-hz-133_000_000")]
pub const TICK_HZ: u64 = 133_000_000;
#[cfg(feature = "tick-hz-140_000_000")]
pub const TICK_HZ: u64 = 140_000_000;
#[cfg(feature = "tick-hz-144_000_000")]
pub const TICK_HZ: u64 = 144_000_000;
#[cfg(feature = "tick-hz-150_000_000")]
pub const TICK_HZ: u64 = 150_000_000;
#[cfg(feature = "tick-hz-160_000_000")]
pub const TICK_HZ: u64 = 160_000_000;
#[cfg(feature = "tick-hz-163_840_000")]
pub const TICK_HZ: u64 = 163_840_000;
#[cfg(feature = "tick-hz-170_000_000")]
pub const TICK_HZ: u64 = 170_000_000;
#[cfg(feature = "tick-hz-180_000_000")]
pub const TICK_HZ: u64 = 180_000_000;
#[cfg(feature = "tick-hz-190_000_000")]
pub const TICK_HZ: u64 = 190_000_000;
#[cfg(feature = "tick-hz-192_000_000")]
pub const TICK_HZ: u64 = 192_000_000;
#[cfg(feature = "tick-hz-200_000_000")]
pub const TICK_HZ: u64 = 200_000_000;
#[cfg(feature = "tick-hz-210_000_000")]
pub const TICK_HZ: u64 = 210_000_000;
#[cfg(feature = "tick-hz-220_000_000")]
pub const TICK_HZ: u64 = 220_000_000;
#[cfg(feature = "tick-hz-230_000_000")]
pub const TICK_HZ: u64 = 230_000_000;
#[cfg(feature = "tick-hz-240_000_000")]
pub const TICK_HZ: u64 = 240_000_000;
#[cfg(feature = "tick-hz-250_000_000")]
pub const TICK_HZ: u64 = 250_000_000;
#[cfg(feature = "tick-hz-256_000_000")]
pub const TICK_HZ: u64 = 256_000_000;
#[cfg(feature = "tick-hz-260_000_000")]
pub const TICK_HZ: u64 = 260_000_000;
#[cfg(feature = "tick-hz-262_144_000")]
pub const TICK_HZ: u64 = 262_144_000;
#[cfg(feature = "tick-hz-270_000_000")]
pub const TICK_HZ: u64 = 270_000_000;
#[cfg(feature = "tick-hz-280_000_000")]
pub const TICK_HZ: u64 = 280_000_000;
#[cfg(feature = "tick-hz-288_000_000")]
pub const TICK_HZ: u64 = 288_000_000;
#[cfg(feature = "tick-hz-290_000_000")]
pub const TICK_HZ: u64 = 290_000_000;
#[cfg(feature = "tick-hz-300_000_000")]
pub const TICK_HZ: u64 = 300_000_000;
#[cfg(feature = "tick-hz-320_000_000")]
pub const TICK_HZ: u64 = 320_000_000;
#[cfg(feature = "tick-hz-327_680_000")]
pub const TICK_HZ: u64 = 327_680_000;
#[cfg(feature = "tick-hz-340_000_000")]
pub const TICK_HZ: u64 = 340_000_000;
#[cfg(feature = "tick-hz-360_000_000")]
pub const TICK_HZ: u64 = 360_000_000;
#[cfg(feature = "tick-hz-380_000_000")]
pub const TICK_HZ: u64 = 380_000_000;
#[cfg(feature = "tick-hz-384_000_000")]
pub const TICK_HZ: u64 = 384_000_000;
#[cfg(feature = "tick-hz-400_000_000")]
pub const TICK_HZ: u64 = 400_000_000;
#[cfg(feature = "tick-hz-420_000_000")]
pub const TICK_HZ: u64 = 420_000_000;
#[cfg(feature = "tick-hz-440_000_000")]
pub const TICK_HZ: u64 = 440_000_000;
#[cfg(feature = "tick-hz-460_000_000")]
pub const TICK_HZ: u64 = 460_000_000;
#[cfg(feature = "tick-hz-480_000_000")]
pub const TICK_HZ: u64 = 480_000_000;
#[cfg(feature = "tick-hz-500_000_000")]
pub const TICK_HZ: u64 = 500_000_000;
#[cfg(feature = "tick-hz-512_000_000")]
pub const TICK_HZ: u64 = 512_000_000;
#[cfg(feature = "tick-hz-520_000_000")]
pub const TICK_HZ: u64 = 520_000_000;
#[cfg(feature = "tick-hz-524_288_000")]
pub const TICK_HZ: u64 = 524_288_000;
#[cfg(feature = "tick-hz-540_000_000")]
pub const TICK_HZ: u64 = 540_000_000;
#[cfg(feature = "tick-hz-560_000_000")]
pub const TICK_HZ: u64 = 560_000_000;
#[cfg(feature = "tick-hz-576_000_000")]
pub const TICK_HZ: u64 = 576_000_000;
#[cfg(feature = "tick-hz-580_000_000")]
pub const TICK_HZ: u64 = 580_000_000;
#[cfg(feature = "tick-hz-600_000_000")]
pub const TICK_HZ: u64 = 600_000_000;
#[cfg(feature = "tick-hz-620_000_000")]
pub const TICK_HZ: u64 = 620_000_000;
#[cfg(feature = "tick-hz-640_000_000")]
pub const TICK_HZ: u64 = 640_000_000;
#[cfg(feature = "tick-hz-655_360_000")]
pub const TICK_HZ: u64 = 655_360_000;
#[cfg(feature = "tick-hz-660_000_000")]
pub const TICK_HZ: u64 = 660_000_000;
#[cfg(feature = "tick-hz-680_000_000")]
pub const TICK_HZ: u64 = 680_000_000;
#[cfg(feature = "tick-hz-700_000_000")]
pub const TICK_HZ: u64 = 700_000_000;
#[cfg(feature = "tick-hz-720_000_000")]
pub const TICK_HZ: u64 = 720_000_000;
#[cfg(feature = "tick-hz-740_000_000")]
pub const TICK_HZ: u64 = 740_000_000;
#[cfg(feature = "tick-hz-760_000_000")]
pub const TICK_HZ: u64 = 760_000_000;
#[cfg(feature = "tick-hz-768_000_000")]
pub const TICK_HZ: u64 = 768_000_000;
#[cfg(feature = "tick-hz-780_000_000")]
pub const TICK_HZ: u64 = 780_000_000;
#[cfg(feature = "tick-hz-800_000_000")]
pub const TICK_HZ: u64 = 800_000_000;
#[cfg(feature = "tick-hz-820_000_000")]
pub const TICK_HZ: u64 = 820_000_000;
#[cfg(feature = "tick-hz-840_000_000")]
pub const TICK_HZ: u64 = 840_000_000;
#[cfg(feature = "tick-hz-860_000_000")]
pub const TICK_HZ: u64 = 860_000_000;
#[cfg(feature = "tick-hz-880_000_000")]
pub const TICK_HZ: u64 = 880_000_000;
#[cfg(feature = "tick-hz-900_000_000")]
pub const TICK_HZ: u64 = 900_000_000;
#[cfg(feature = "tick-hz-920_000_000")]
pub const TICK_HZ: u64 = 920_000_000;
#[cfg(feature = "tick-hz-940_000_000")]
pub const TICK_HZ: u64 = 940_000_000;
#[cfg(feature = "tick-hz-960_000_000")]
pub const TICK_HZ: u64 = 960_000_000;
#[cfg(feature = "tick-hz-980_000_000")]
pub const TICK_HZ: u64 = 980_000_000;
#[cfg(feature = "tick-hz-1_000_000_000")]
pub const TICK_HZ: u64 = 1_000_000_000;
#[cfg(feature = "tick-hz-1_310_720_000")]
pub const TICK_HZ: u64 = 1_310_720_000;
#[cfg(feature = "tick-hz-2_621_440_000")]
pub const TICK_HZ: u64 = 2_621_440_000;
#[cfg(feature = "tick-hz-5_242_880_000")]
pub const TICK_HZ: u64 = 5_242_880_000;
#[cfg(not(any(
    feature = "tick-hz-1",
    feature = "tick-hz-2",
    feature = "tick-hz-4",
    feature = "tick-hz-8",
    feature = "tick-hz-10",
    feature = "tick-hz-16",
    feature = "tick-hz-32",
    feature = "tick-hz-64",
    feature = "tick-hz-100",
    feature = "tick-hz-128",
    feature = "tick-hz-256",
    feature = "tick-hz-512",
    feature = "tick-hz-1_000",
    feature = "tick-hz-1_024",
    feature = "tick-hz-2_000",
    feature = "tick-hz-2_048",
    feature = "tick-hz-4_000",
    feature = "tick-hz-4_096",
    feature = "tick-hz-8_000",
    feature = "tick-hz-8_192",
    feature = "tick-hz-10_000",
    feature = "tick-hz-16_000",
    feature = "tick-hz-16_384",
    feature = "tick-hz-20_000",
    feature = "tick-hz-32_000",
    feature = "tick-hz-32_768",
    feature = "tick-hz-40_000",
    feature = "tick-hz-64_000",
    feature = "tick-hz-65_536",
    feature = "tick-hz-80_000",
    feature = "tick-hz-100_000",
    feature = "tick-hz-128_000",
    feature = "tick-hz-131_072",
    feature = "tick-hz-160_000",
    feature = "tick-hz-256_000",
    feature = "tick-hz-262_144",
    feature = "tick-hz-320_000",
    feature = "tick-hz-512_000",
    feature = "tick-hz-524_288",
    feature = "tick-hz-640_000",
    feature = "tick-hz-1_000_000",
    feature = "tick-hz-1_024_000",
    feature = "tick-hz-1_048_576",
    feature = "tick-hz-1_280_000",
    feature = "tick-hz-2_000_000",
    feature = "tick-hz-2_048_000",
    feature = "tick-hz-2_097_152",
    feature = "tick-hz-2_560_000",
    feature = "tick-hz-3_000_000",
    feature = "tick-hz-4_000_000",
    feature = "tick-hz-4_096_000",
    feature = "tick-hz-4_194_304",
    feature = "tick-hz-5_120_000",
    feature = "tick-hz-6_000_000",
    feature = "tick-hz-8_000_000",
    feature = "tick-hz-8_192_000",
    feature = "tick-hz-8_388_608",
    feature = "tick-hz-9_000_000",
    feature = "tick-hz-10_000_000",
    feature = "tick-hz-10_240_000",
    feature = "tick-hz-12_000_000",
    feature = "tick-hz-16_000_000",
    feature = "tick-hz-16_384_000",
    feature = "tick-hz-16_777_216",
    feature = "tick-hz-18_000_000",
    feature = "tick-hz-20_000_000",
    feature = "tick-hz-20_480_000",
    feature = "tick-hz-24_000_000",
    feature = "tick-hz-30_000_000",
    feature = "tick-hz-32_000_000",
    feature = "tick-hz-32_768_000",
    feature = "tick-hz-36_000_000",
    feature = "tick-hz-40_000_000",
    feature = "tick-hz-40_960_000",
    feature = "tick-hz-48_000_000",
    feature = "tick-hz-50_000_000",
    feature = "tick-hz-60_000_000",
    feature = "tick-hz-64_000_000",
    feature = "tick-hz-65_536_000",
    feature = "tick-hz-70_000_000",
    feature = "tick-hz-72_000_000",
    feature = "tick-hz-80_000_000",
    feature = "tick-hz-81_920_000",
    feature = "tick-hz-90_000_000",
    feature = "tick-hz-96_000_000",
    feature = "tick-hz-100_000_000",
    feature = "tick-hz-110_000_000",
    feature = "tick-hz-120_000_000",
    feature = "tick-hz-128_000_000",
    feature = "tick-hz-130_000_000",
    feature = "tick-hz-131_072_000",
    feature = "tick-hz-133_000_000",
    feature = "tick-hz-140_000_000",
    feature = "tick-hz-144_000_000",
    feature = "tick-hz-150_000_000",
    feature = "tick-hz-160_000_000",
    feature = "tick-hz-163_840_000",
    feature = "tick-hz-170_000_000",
    feature = "tick-hz-180_000_000",
    feature = "tick-hz-190_000_000",
    feature = "tick-hz-192_000_000",
    feature = "tick-hz-200_000_000",
    feature = "tick-hz-210_000_000",
    feature = "tick-hz-220_000_000",
    feature = "tick-hz-230_000_000",
    feature = "tick-hz-240_000_000",
    feature = "tick-hz-250_000_000",
    feature = "tick-hz-256_000_000",
    feature = "tick-hz-260_000_000",
    feature = "tick-hz-262_144_000",
    feature = "tick-hz-270_000_000",
    feature = "tick-hz-280_000_000",
    feature = "tick-hz-288_000_000",
    feature = "tick-hz-290_000_000",
    feature = "tick-hz-300_000_000",
    feature = "tick-hz-320_000_000",
    feature = "tick-hz-327_680_000",
    feature = "tick-hz-340_000_000",
    feature = "tick-hz-360_000_000",
    feature = "tick-hz-380_000_000",
    feature = "tick-hz-384_000_000",
    feature = "tick-hz-400_000_000",
    feature = "tick-hz-420_000_000",
    feature = "tick-hz-440_000_000",
    feature = "tick-hz-460_000_000",
    feature = "tick-hz-480_000_000",
    feature = "tick-hz-500_000_000",
    feature = "tick-hz-512_000_000",
    feature = "tick-hz-520_000_000",
    feature = "tick-hz-524_288_000",
    feature = "tick-hz-540_000_000",
    feature = "tick-hz-560_000_000",
    feature = "tick-hz-576_000_000",
    feature = "tick-hz-580_000_000",
    feature = "tick-hz-600_000_000",
    feature = "tick-hz-620_000_000",
    feature = "tick-hz-640_000_000",
    feature = "tick-hz-655_360_000",
    feature = "tick-hz-660_000_000",
    feature = "tick-hz-680_000_000",
    feature = "tick-hz-700_000_000",
    feature = "tick-hz-720_000_000",
    feature = "tick-hz-740_000_000",
    feature = "tick-hz-760_000_000",
    feature = "tick-hz-768_000_000",
    feature = "tick-hz-780_000_000",
    feature = "tick-hz-800_000_000",
    feature = "tick-hz-820_000_000",
    feature = "tick-hz-840_000_000",
    feature = "tick-hz-860_000_000",
    feature = "tick-hz-880_000_000",
    feature = "tick-hz-900_000_000",
    feature = "tick-hz-920_000_000",
    feature = "tick-hz-940_000_000",
    feature = "tick-hz-960_000_000",
    feature = "tick-hz-980_000_000",
    feature = "tick-hz-1_000_000_000",
    feature = "tick-hz-1_310_720_000",
    feature = "tick-hz-2_621_440_000",
    feature = "tick-hz-5_242_880_000",
)))]
pub const TICK_HZ: u64 = 1_000_000;
