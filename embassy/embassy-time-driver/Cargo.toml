[package]
name = "embassy-time-driver"
version = "0.2.1"
edition = "2021"
description = "Driver trait for embassy-time"
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-time-driver"
readme = "README.md"
license = "MIT OR Apache-2.0"
categories = [
    "embedded",
    "no-std",
    "concurrency",
    "asynchronous",
]

# Prevent multiple copies of this crate in the same binary.
# Needed because different copies might get different tick rates, causing
# wrong delays if the time driver is using one copy and user code is using another.
# This is especially common when mixing crates from crates.io and git.
links = "embassy-time"

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-time-driver-v$VERSION/embassy-time-driver/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-time-driver/src/"
target = "x86_64-unknown-linux-gnu"

[features]
#! ### Tick Rate
#!
#! At most 1 `tick-*` feature can be enabled. If none is enabled, a default of 1MHz is used.
#! 
#! If the time driver in use supports using arbitrary tick rates, you can enable one `tick-*`
#! feature from your binary crate to set the tick rate. The driver will use configured tick rate.
#! If the time driver supports a fixed tick rate, it will enable one feature itself, so you should
#! not enable one. Check the time driver documentation for details.
#!
#! When using embassy-time from libraries, you should *not* enable any `tick-*` feature, to allow the
#! end user or the driver to pick.
#! <details>
#!   <summary>Available tick rates:</summary>
#! <!-- Next line must be left empty for the features to render correctly! -->
#! 

# BEGIN TICKS
# Generated by gen_tick.py. DO NOT EDIT.
## 1Hz Tick Rate
tick-hz-1 = []
## 2Hz Tick Rate
tick-hz-2 = []
## 4Hz Tick Rate
tick-hz-4 = []
## 8Hz Tick Rate
tick-hz-8 = []
## 10Hz Tick Rate
tick-hz-10 = []
## 16Hz Tick Rate
tick-hz-16 = []
## 32Hz Tick Rate
tick-hz-32 = []
## 64Hz Tick Rate
tick-hz-64 = []
## 100Hz Tick Rate
tick-hz-100 = []
## 128Hz Tick Rate
tick-hz-128 = []
## 256Hz Tick Rate
tick-hz-256 = []
## 512Hz Tick Rate
tick-hz-512 = []
## 1.0kHz Tick Rate
tick-hz-1_000 = []
## 1.024kHz Tick Rate
tick-hz-1_024 = []
## 2.0kHz Tick Rate
tick-hz-2_000 = []
## 2.048kHz Tick Rate
tick-hz-2_048 = []
## 4.0kHz Tick Rate
tick-hz-4_000 = []
## 4.096kHz Tick Rate
tick-hz-4_096 = []
## 8.0kHz Tick Rate
tick-hz-8_000 = []
## 8.192kHz Tick Rate
tick-hz-8_192 = []
## 10.0kHz Tick Rate
tick-hz-10_000 = []
## 16.0kHz Tick Rate
tick-hz-16_000 = []
## 16.384kHz Tick Rate
tick-hz-16_384 = []
## 20.0kHz Tick Rate
tick-hz-20_000 = []
## 32.0kHz Tick Rate
tick-hz-32_000 = []
## 32.768kHz Tick Rate
tick-hz-32_768 = []
## 40.0kHz Tick Rate
tick-hz-40_000 = []
## 64.0kHz Tick Rate
tick-hz-64_000 = []
## 65.536kHz Tick Rate
tick-hz-65_536 = []
## 80.0kHz Tick Rate
tick-hz-80_000 = []
## 100.0kHz Tick Rate
tick-hz-100_000 = []
## 128.0kHz Tick Rate
tick-hz-128_000 = []
## 131.072kHz Tick Rate
tick-hz-131_072 = []
## 160.0kHz Tick Rate
tick-hz-160_000 = []
## 256.0kHz Tick Rate
tick-hz-256_000 = []
## 262.144kHz Tick Rate
tick-hz-262_144 = []
## 320.0kHz Tick Rate
tick-hz-320_000 = []
## 512.0kHz Tick Rate
tick-hz-512_000 = []
## 524.288kHz Tick Rate
tick-hz-524_288 = []
## 640.0kHz Tick Rate
tick-hz-640_000 = []
## 1.0MHz Tick Rate
tick-hz-1_000_000 = []
## 1.024MHz Tick Rate
tick-hz-1_024_000 = []
## 1.048576MHz Tick Rate
tick-hz-1_048_576 = []
## 1.28MHz Tick Rate
tick-hz-1_280_000 = []
## 2.0MHz Tick Rate
tick-hz-2_000_000 = []
## 2.048MHz Tick Rate
tick-hz-2_048_000 = []
## 2.097152MHz Tick Rate
tick-hz-2_097_152 = []
## 2.56MHz Tick Rate
tick-hz-2_560_000 = []
## 3.0MHz Tick Rate
tick-hz-3_000_000 = []
## 4.0MHz Tick Rate
tick-hz-4_000_000 = []
## 4.096MHz Tick Rate
tick-hz-4_096_000 = []
## 4.194304MHz Tick Rate
tick-hz-4_194_304 = []
## 5.12MHz Tick Rate
tick-hz-5_120_000 = []
## 6.0MHz Tick Rate
tick-hz-6_000_000 = []
## 8.0MHz Tick Rate
tick-hz-8_000_000 = []
## 8.192MHz Tick Rate
tick-hz-8_192_000 = []
## 8.388608MHz Tick Rate
tick-hz-8_388_608 = []
## 9.0MHz Tick Rate
tick-hz-9_000_000 = []
## 10.0MHz Tick Rate
tick-hz-10_000_000 = []
## 10.24MHz Tick Rate
tick-hz-10_240_000 = []
## 12.0MHz Tick Rate
tick-hz-12_000_000 = []
## 16.0MHz Tick Rate
tick-hz-16_000_000 = []
## 16.384MHz Tick Rate
tick-hz-16_384_000 = []
## 16.777216MHz Tick Rate
tick-hz-16_777_216 = []
## 18.0MHz Tick Rate
tick-hz-18_000_000 = []
## 20.0MHz Tick Rate
tick-hz-20_000_000 = []
## 20.48MHz Tick Rate
tick-hz-20_480_000 = []
## 24.0MHz Tick Rate
tick-hz-24_000_000 = []
## 30.0MHz Tick Rate
tick-hz-30_000_000 = []
## 32.0MHz Tick Rate
tick-hz-32_000_000 = []
## 32.768MHz Tick Rate
tick-hz-32_768_000 = []
## 36.0MHz Tick Rate
tick-hz-36_000_000 = []
## 40.0MHz Tick Rate
tick-hz-40_000_000 = []
## 40.96MHz Tick Rate
tick-hz-40_960_000 = []
## 48.0MHz Tick Rate
tick-hz-48_000_000 = []
## 50.0MHz Tick Rate
tick-hz-50_000_000 = []
## 60.0MHz Tick Rate
tick-hz-60_000_000 = []
## 64.0MHz Tick Rate
tick-hz-64_000_000 = []
## 65.536MHz Tick Rate
tick-hz-65_536_000 = []
## 70.0MHz Tick Rate
tick-hz-70_000_000 = []
## 72.0MHz Tick Rate
tick-hz-72_000_000 = []
## 80.0MHz Tick Rate
tick-hz-80_000_000 = []
## 81.92MHz Tick Rate
tick-hz-81_920_000 = []
## 90.0MHz Tick Rate
tick-hz-90_000_000 = []
## 96.0MHz Tick Rate
tick-hz-96_000_000 = []
## 100.0MHz Tick Rate
tick-hz-100_000_000 = []
## 110.0MHz Tick Rate
tick-hz-110_000_000 = []
## 120.0MHz Tick Rate
tick-hz-120_000_000 = []
## 128.0MHz Tick Rate
tick-hz-128_000_000 = []
## 130.0MHz Tick Rate
tick-hz-130_000_000 = []
## 131.072MHz Tick Rate
tick-hz-131_072_000 = []
## 133.0MHz Tick Rate
tick-hz-133_000_000 = []
## 140.0MHz Tick Rate
tick-hz-140_000_000 = []
## 144.0MHz Tick Rate
tick-hz-144_000_000 = []
## 150.0MHz Tick Rate
tick-hz-150_000_000 = []
## 160.0MHz Tick Rate
tick-hz-160_000_000 = []
## 163.84MHz Tick Rate
tick-hz-163_840_000 = []
## 170.0MHz Tick Rate
tick-hz-170_000_000 = []
## 180.0MHz Tick Rate
tick-hz-180_000_000 = []
## 190.0MHz Tick Rate
tick-hz-190_000_000 = []
## 192.0MHz Tick Rate
tick-hz-192_000_000 = []
## 200.0MHz Tick Rate
tick-hz-200_000_000 = []
## 210.0MHz Tick Rate
tick-hz-210_000_000 = []
## 220.0MHz Tick Rate
tick-hz-220_000_000 = []
## 230.0MHz Tick Rate
tick-hz-230_000_000 = []
## 240.0MHz Tick Rate
tick-hz-240_000_000 = []
## 250.0MHz Tick Rate
tick-hz-250_000_000 = []
## 256.0MHz Tick Rate
tick-hz-256_000_000 = []
## 260.0MHz Tick Rate
tick-hz-260_000_000 = []
## 262.144MHz Tick Rate
tick-hz-262_144_000 = []
## 270.0MHz Tick Rate
tick-hz-270_000_000 = []
## 280.0MHz Tick Rate
tick-hz-280_000_000 = []
## 288.0MHz Tick Rate
tick-hz-288_000_000 = []
## 290.0MHz Tick Rate
tick-hz-290_000_000 = []
## 300.0MHz Tick Rate
tick-hz-300_000_000 = []
## 320.0MHz Tick Rate
tick-hz-320_000_000 = []
## 327.68MHz Tick Rate
tick-hz-327_680_000 = []
## 340.0MHz Tick Rate
tick-hz-340_000_000 = []
## 360.0MHz Tick Rate
tick-hz-360_000_000 = []
## 380.0MHz Tick Rate
tick-hz-380_000_000 = []
## 384.0MHz Tick Rate
tick-hz-384_000_000 = []
## 400.0MHz Tick Rate
tick-hz-400_000_000 = []
## 420.0MHz Tick Rate
tick-hz-420_000_000 = []
## 440.0MHz Tick Rate
tick-hz-440_000_000 = []
## 460.0MHz Tick Rate
tick-hz-460_000_000 = []
## 480.0MHz Tick Rate
tick-hz-480_000_000 = []
## 500.0MHz Tick Rate
tick-hz-500_000_000 = []
## 512.0MHz Tick Rate
tick-hz-512_000_000 = []
## 520.0MHz Tick Rate
tick-hz-520_000_000 = []
## 524.288MHz Tick Rate
tick-hz-524_288_000 = []
## 540.0MHz Tick Rate
tick-hz-540_000_000 = []
## 560.0MHz Tick Rate
tick-hz-560_000_000 = []
## 576.0MHz Tick Rate
tick-hz-576_000_000 = []
## 580.0MHz Tick Rate
tick-hz-580_000_000 = []
## 600.0MHz Tick Rate
tick-hz-600_000_000 = []
## 620.0MHz Tick Rate
tick-hz-620_000_000 = []
## 640.0MHz Tick Rate
tick-hz-640_000_000 = []
## 655.36MHz Tick Rate
tick-hz-655_360_000 = []
## 660.0MHz Tick Rate
tick-hz-660_000_000 = []
## 680.0MHz Tick Rate
tick-hz-680_000_000 = []
## 700.0MHz Tick Rate
tick-hz-700_000_000 = []
## 720.0MHz Tick Rate
tick-hz-720_000_000 = []
## 740.0MHz Tick Rate
tick-hz-740_000_000 = []
## 760.0MHz Tick Rate
tick-hz-760_000_000 = []
## 768.0MHz Tick Rate
tick-hz-768_000_000 = []
## 780.0MHz Tick Rate
tick-hz-780_000_000 = []
## 800.0MHz Tick Rate
tick-hz-800_000_000 = []
## 820.0MHz Tick Rate
tick-hz-820_000_000 = []
## 840.0MHz Tick Rate
tick-hz-840_000_000 = []
## 860.0MHz Tick Rate
tick-hz-860_000_000 = []
## 880.0MHz Tick Rate
tick-hz-880_000_000 = []
## 900.0MHz Tick Rate
tick-hz-900_000_000 = []
## 920.0MHz Tick Rate
tick-hz-920_000_000 = []
## 940.0MHz Tick Rate
tick-hz-940_000_000 = []
## 960.0MHz Tick Rate
tick-hz-960_000_000 = []
## 980.0MHz Tick Rate
tick-hz-980_000_000 = []
## 1.0GHz Tick Rate
tick-hz-1_000_000_000 = []
## 1.31072GHz Tick Rate
tick-hz-1_310_720_000 = []
## 2.62144GHz Tick Rate
tick-hz-2_621_440_000 = []
## 5.24288GHz Tick Rate
tick-hz-5_242_880_000 = []
# END TICKS

#! </details>

[dependencies]
document-features = "0.2.7"

[dev-dependencies]
critical-section = "1"
embassy-time-queue-utils = { path = "../embassy-time-queue-utils" }
