[package]
name = "embassy-time"
version = "0.5.0"
edition = "2021"
description = "Instant and Duration for embedded no-std systems, with async timer support"
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-time"
readme = "README.md"
license = "MIT OR Apache-2.0"
categories = [
    "embedded",
    "no-std",
    "concurrency",
    "asynchronous",
]

[package.metadata.embassy]
build = [
    {target = "thumbv6m-none-eabi", features = ["defmt", "defmt-timestamp-uptime", "mock-driver"]},
    # Xtensa builds
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32s2-none-elf", features = ["defmt", "defmt-timestamp-uptime", "mock-driver"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-time-v$VERSION/embassy-time/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-time/src/"
features = ["defmt", "std"]
target = "x86_64-unknown-linux-gnu"

[package.metadata.docs.rs]
features = ["defmt", "std"]

[features]
## Display the time since startup next to defmt log messages.
## At most 1 `defmt-timestamp-uptime-*` feature can be used.
## `defmt-timestamp-uptime` is provided for backwards compatibility (provides the same format as `uptime-us`).
## To use this you must have a time driver provided.
defmt-timestamp-uptime = ["defmt"]
defmt-timestamp-uptime-s = ["defmt"]
defmt-timestamp-uptime-ms = ["defmt"]
defmt-timestamp-uptime-us = ["defmt"]
defmt-timestamp-uptime-ts = ["defmt"]
defmt-timestamp-uptime-tms = ["defmt"]
defmt-timestamp-uptime-tus = ["defmt"]

#! ### Time Drivers

#! Usually, time drivers are defined by a HAL, or a companion crate to the HAL. For `std` and WASM
#! environments, as well as for testing purposes, `embassy-time` provides some default time drivers
#! that may be suitable for your use case. You can enable one of the following features to use them.

## Create a `MockDriver` that can be manually advanced for testing purposes.
mock-driver = ["tick-hz-1_000_000", "dep:embassy-time-queue-utils"]
## Create a time driver for `std` environments.
std = ["tick-hz-1_000_000", "dep:embassy-time-queue-utils"]
## Create a time driver for WASM.
wasm = ["dep:wasm-bindgen", "dep:js-sys", "dep:wasm-timer", "tick-hz-1_000_000", "dep:embassy-time-queue-utils"]

#! ### Generic Queue

#! By default embassy-time uses a timer queue implementation that is faster but depends on `embassy-executor`.
#! It will panic if you try to await any timer when using another executor.
#! 
#! Alternatively, you can choose to use a "generic" timer queue implementation that works on any executor.
#! To enable it, enable any of the features below.
#! 
#! The features also set how many timers are used for the generic queue. At most one
#! `generic-queue-*` feature can be enabled. If none is enabled, `queue_integrated` is used.
#!
#! When using embassy-time from libraries, you should *not* enable any `generic-queue-*` feature, to allow the
#! end user to pick.

## Generic Queue with 8 timers
generic-queue-8 = ["embassy-time-queue-utils/generic-queue-8"]
## Generic Queue with 16 timers
generic-queue-16 = ["embassy-time-queue-utils/generic-queue-16"]
## Generic Queue with 32 timers
generic-queue-32 = ["embassy-time-queue-utils/generic-queue-32"]
## Generic Queue with 64 timers
generic-queue-64 = ["embassy-time-queue-utils/generic-queue-64"]
## Generic Queue with 128 timers
generic-queue-128 = ["embassy-time-queue-utils/generic-queue-128"]

#! ### Tick Rate
#!
#! At most 1 `tick-*` feature can be enabled. If none is enabled, a default of 1MHz is used.
#!
#! If the time driver in use supports using arbitrary tick rates, you can enable one `tick-*`
#! feature from your binary crate to set the tick rate. The driver will use configured tick rate.
#! If the time driver supports a fixed tick rate, it will enable one feature itself, so you should
#! not enable one. Check the time driver documentation for details.
#!
#! When using embassy-time from libraries, you should *not* enable any `tick-*` feature, to allow the
#! end user or the driver to pick.
#! <details>
#!   <summary>Available tick rates:</summary>
#! <!-- Next line must be left empty for the features to render correctly! -->
#!

# BEGIN TICKS
# Generated by gen_tick.py. DO NOT EDIT.
## 1Hz Tick Rate
tick-hz-1 = ["embassy-time-driver/tick-hz-1"]
## 2Hz Tick Rate
tick-hz-2 = ["embassy-time-driver/tick-hz-2"]
## 4Hz Tick Rate
tick-hz-4 = ["embassy-time-driver/tick-hz-4"]
## 8Hz Tick Rate
tick-hz-8 = ["embassy-time-driver/tick-hz-8"]
## 10Hz Tick Rate
tick-hz-10 = ["embassy-time-driver/tick-hz-10"]
## 16Hz Tick Rate
tick-hz-16 = ["embassy-time-driver/tick-hz-16"]
## 32Hz Tick Rate
tick-hz-32 = ["embassy-time-driver/tick-hz-32"]
## 64Hz Tick Rate
tick-hz-64 = ["embassy-time-driver/tick-hz-64"]
## 100Hz Tick Rate
tick-hz-100 = ["embassy-time-driver/tick-hz-100"]
## 128Hz Tick Rate
tick-hz-128 = ["embassy-time-driver/tick-hz-128"]
## 256Hz Tick Rate
tick-hz-256 = ["embassy-time-driver/tick-hz-256"]
## 512Hz Tick Rate
tick-hz-512 = ["embassy-time-driver/tick-hz-512"]
## 1.0kHz Tick Rate
tick-hz-1_000 = ["embassy-time-driver/tick-hz-1_000"]
## 1.024kHz Tick Rate
tick-hz-1_024 = ["embassy-time-driver/tick-hz-1_024"]
## 2.0kHz Tick Rate
tick-hz-2_000 = ["embassy-time-driver/tick-hz-2_000"]
## 2.048kHz Tick Rate
tick-hz-2_048 = ["embassy-time-driver/tick-hz-2_048"]
## 4.0kHz Tick Rate
tick-hz-4_000 = ["embassy-time-driver/tick-hz-4_000"]
## 4.096kHz Tick Rate
tick-hz-4_096 = ["embassy-time-driver/tick-hz-4_096"]
## 8.0kHz Tick Rate
tick-hz-8_000 = ["embassy-time-driver/tick-hz-8_000"]
## 8.192kHz Tick Rate
tick-hz-8_192 = ["embassy-time-driver/tick-hz-8_192"]
## 10.0kHz Tick Rate
tick-hz-10_000 = ["embassy-time-driver/tick-hz-10_000"]
## 16.0kHz Tick Rate
tick-hz-16_000 = ["embassy-time-driver/tick-hz-16_000"]
## 16.384kHz Tick Rate
tick-hz-16_384 = ["embassy-time-driver/tick-hz-16_384"]
## 20.0kHz Tick Rate
tick-hz-20_000 = ["embassy-time-driver/tick-hz-20_000"]
## 32.0kHz Tick Rate
tick-hz-32_000 = ["embassy-time-driver/tick-hz-32_000"]
## 32.768kHz Tick Rate
tick-hz-32_768 = ["embassy-time-driver/tick-hz-32_768"]
## 40.0kHz Tick Rate
tick-hz-40_000 = ["embassy-time-driver/tick-hz-40_000"]
## 64.0kHz Tick Rate
tick-hz-64_000 = ["embassy-time-driver/tick-hz-64_000"]
## 65.536kHz Tick Rate
tick-hz-65_536 = ["embassy-time-driver/tick-hz-65_536"]
## 80.0kHz Tick Rate
tick-hz-80_000 = ["embassy-time-driver/tick-hz-80_000"]
## 100.0kHz Tick Rate
tick-hz-100_000 = ["embassy-time-driver/tick-hz-100_000"]
## 128.0kHz Tick Rate
tick-hz-128_000 = ["embassy-time-driver/tick-hz-128_000"]
## 131.072kHz Tick Rate
tick-hz-131_072 = ["embassy-time-driver/tick-hz-131_072"]
## 160.0kHz Tick Rate
tick-hz-160_000 = ["embassy-time-driver/tick-hz-160_000"]
## 256.0kHz Tick Rate
tick-hz-256_000 = ["embassy-time-driver/tick-hz-256_000"]
## 262.144kHz Tick Rate
tick-hz-262_144 = ["embassy-time-driver/tick-hz-262_144"]
## 320.0kHz Tick Rate
tick-hz-320_000 = ["embassy-time-driver/tick-hz-320_000"]
## 512.0kHz Tick Rate
tick-hz-512_000 = ["embassy-time-driver/tick-hz-512_000"]
## 524.288kHz Tick Rate
tick-hz-524_288 = ["embassy-time-driver/tick-hz-524_288"]
## 640.0kHz Tick Rate
tick-hz-640_000 = ["embassy-time-driver/tick-hz-640_000"]
## 1.0MHz Tick Rate
tick-hz-1_000_000 = ["embassy-time-driver/tick-hz-1_000_000"]
## 1.024MHz Tick Rate
tick-hz-1_024_000 = ["embassy-time-driver/tick-hz-1_024_000"]
## 1.048576MHz Tick Rate
tick-hz-1_048_576 = ["embassy-time-driver/tick-hz-1_048_576"]
## 1.28MHz Tick Rate
tick-hz-1_280_000 = ["embassy-time-driver/tick-hz-1_280_000"]
## 2.0MHz Tick Rate
tick-hz-2_000_000 = ["embassy-time-driver/tick-hz-2_000_000"]
## 2.048MHz Tick Rate
tick-hz-2_048_000 = ["embassy-time-driver/tick-hz-2_048_000"]
## 2.097152MHz Tick Rate
tick-hz-2_097_152 = ["embassy-time-driver/tick-hz-2_097_152"]
## 2.56MHz Tick Rate
tick-hz-2_560_000 = ["embassy-time-driver/tick-hz-2_560_000"]
## 3.0MHz Tick Rate
tick-hz-3_000_000 = ["embassy-time-driver/tick-hz-3_000_000"]
## 4.0MHz Tick Rate
tick-hz-4_000_000 = ["embassy-time-driver/tick-hz-4_000_000"]
## 4.096MHz Tick Rate
tick-hz-4_096_000 = ["embassy-time-driver/tick-hz-4_096_000"]
## 4.194304MHz Tick Rate
tick-hz-4_194_304 = ["embassy-time-driver/tick-hz-4_194_304"]
## 5.12MHz Tick Rate
tick-hz-5_120_000 = ["embassy-time-driver/tick-hz-5_120_000"]
## 6.0MHz Tick Rate
tick-hz-6_000_000 = ["embassy-time-driver/tick-hz-6_000_000"]
## 8.0MHz Tick Rate
tick-hz-8_000_000 = ["embassy-time-driver/tick-hz-8_000_000"]
## 8.192MHz Tick Rate
tick-hz-8_192_000 = ["embassy-time-driver/tick-hz-8_192_000"]
## 8.388608MHz Tick Rate
tick-hz-8_388_608 = ["embassy-time-driver/tick-hz-8_388_608"]
## 9.0MHz Tick Rate
tick-hz-9_000_000 = ["embassy-time-driver/tick-hz-9_000_000"]
## 10.0MHz Tick Rate
tick-hz-10_000_000 = ["embassy-time-driver/tick-hz-10_000_000"]
## 10.24MHz Tick Rate
tick-hz-10_240_000 = ["embassy-time-driver/tick-hz-10_240_000"]
## 12.0MHz Tick Rate
tick-hz-12_000_000 = ["embassy-time-driver/tick-hz-12_000_000"]
## 16.0MHz Tick Rate
tick-hz-16_000_000 = ["embassy-time-driver/tick-hz-16_000_000"]
## 16.384MHz Tick Rate
tick-hz-16_384_000 = ["embassy-time-driver/tick-hz-16_384_000"]
## 16.777216MHz Tick Rate
tick-hz-16_777_216 = ["embassy-time-driver/tick-hz-16_777_216"]
## 18.0MHz Tick Rate
tick-hz-18_000_000 = ["embassy-time-driver/tick-hz-18_000_000"]
## 20.0MHz Tick Rate
tick-hz-20_000_000 = ["embassy-time-driver/tick-hz-20_000_000"]
## 20.48MHz Tick Rate
tick-hz-20_480_000 = ["embassy-time-driver/tick-hz-20_480_000"]
## 24.0MHz Tick Rate
tick-hz-24_000_000 = ["embassy-time-driver/tick-hz-24_000_000"]
## 30.0MHz Tick Rate
tick-hz-30_000_000 = ["embassy-time-driver/tick-hz-30_000_000"]
## 32.0MHz Tick Rate
tick-hz-32_000_000 = ["embassy-time-driver/tick-hz-32_000_000"]
## 32.768MHz Tick Rate
tick-hz-32_768_000 = ["embassy-time-driver/tick-hz-32_768_000"]
## 36.0MHz Tick Rate
tick-hz-36_000_000 = ["embassy-time-driver/tick-hz-36_000_000"]
## 40.0MHz Tick Rate
tick-hz-40_000_000 = ["embassy-time-driver/tick-hz-40_000_000"]
## 40.96MHz Tick Rate
tick-hz-40_960_000 = ["embassy-time-driver/tick-hz-40_960_000"]
## 48.0MHz Tick Rate
tick-hz-48_000_000 = ["embassy-time-driver/tick-hz-48_000_000"]
## 50.0MHz Tick Rate
tick-hz-50_000_000 = ["embassy-time-driver/tick-hz-50_000_000"]
## 60.0MHz Tick Rate
tick-hz-60_000_000 = ["embassy-time-driver/tick-hz-60_000_000"]
## 64.0MHz Tick Rate
tick-hz-64_000_000 = ["embassy-time-driver/tick-hz-64_000_000"]
## 65.536MHz Tick Rate
tick-hz-65_536_000 = ["embassy-time-driver/tick-hz-65_536_000"]
## 70.0MHz Tick Rate
tick-hz-70_000_000 = ["embassy-time-driver/tick-hz-70_000_000"]
## 72.0MHz Tick Rate
tick-hz-72_000_000 = ["embassy-time-driver/tick-hz-72_000_000"]
## 80.0MHz Tick Rate
tick-hz-80_000_000 = ["embassy-time-driver/tick-hz-80_000_000"]
## 81.92MHz Tick Rate
tick-hz-81_920_000 = ["embassy-time-driver/tick-hz-81_920_000"]
## 90.0MHz Tick Rate
tick-hz-90_000_000 = ["embassy-time-driver/tick-hz-90_000_000"]
## 96.0MHz Tick Rate
tick-hz-96_000_000 = ["embassy-time-driver/tick-hz-96_000_000"]
## 100.0MHz Tick Rate
tick-hz-100_000_000 = ["embassy-time-driver/tick-hz-100_000_000"]
## 110.0MHz Tick Rate
tick-hz-110_000_000 = ["embassy-time-driver/tick-hz-110_000_000"]
## 120.0MHz Tick Rate
tick-hz-120_000_000 = ["embassy-time-driver/tick-hz-120_000_000"]
## 128.0MHz Tick Rate
tick-hz-128_000_000 = ["embassy-time-driver/tick-hz-128_000_000"]
## 130.0MHz Tick Rate
tick-hz-130_000_000 = ["embassy-time-driver/tick-hz-130_000_000"]
## 131.072MHz Tick Rate
tick-hz-131_072_000 = ["embassy-time-driver/tick-hz-131_072_000"]
## 133.0MHz Tick Rate
tick-hz-133_000_000 = ["embassy-time-driver/tick-hz-133_000_000"]
## 140.0MHz Tick Rate
tick-hz-140_000_000 = ["embassy-time-driver/tick-hz-140_000_000"]
## 144.0MHz Tick Rate
tick-hz-144_000_000 = ["embassy-time-driver/tick-hz-144_000_000"]
## 150.0MHz Tick Rate
tick-hz-150_000_000 = ["embassy-time-driver/tick-hz-150_000_000"]
## 160.0MHz Tick Rate
tick-hz-160_000_000 = ["embassy-time-driver/tick-hz-160_000_000"]
## 163.84MHz Tick Rate
tick-hz-163_840_000 = ["embassy-time-driver/tick-hz-163_840_000"]
## 170.0MHz Tick Rate
tick-hz-170_000_000 = ["embassy-time-driver/tick-hz-170_000_000"]
## 180.0MHz Tick Rate
tick-hz-180_000_000 = ["embassy-time-driver/tick-hz-180_000_000"]
## 190.0MHz Tick Rate
tick-hz-190_000_000 = ["embassy-time-driver/tick-hz-190_000_000"]
## 192.0MHz Tick Rate
tick-hz-192_000_000 = ["embassy-time-driver/tick-hz-192_000_000"]
## 200.0MHz Tick Rate
tick-hz-200_000_000 = ["embassy-time-driver/tick-hz-200_000_000"]
## 210.0MHz Tick Rate
tick-hz-210_000_000 = ["embassy-time-driver/tick-hz-210_000_000"]
## 220.0MHz Tick Rate
tick-hz-220_000_000 = ["embassy-time-driver/tick-hz-220_000_000"]
## 230.0MHz Tick Rate
tick-hz-230_000_000 = ["embassy-time-driver/tick-hz-230_000_000"]
## 240.0MHz Tick Rate
tick-hz-240_000_000 = ["embassy-time-driver/tick-hz-240_000_000"]
## 250.0MHz Tick Rate
tick-hz-250_000_000 = ["embassy-time-driver/tick-hz-250_000_000"]
## 256.0MHz Tick Rate
tick-hz-256_000_000 = ["embassy-time-driver/tick-hz-256_000_000"]
## 260.0MHz Tick Rate
tick-hz-260_000_000 = ["embassy-time-driver/tick-hz-260_000_000"]
## 262.144MHz Tick Rate
tick-hz-262_144_000 = ["embassy-time-driver/tick-hz-262_144_000"]
## 270.0MHz Tick Rate
tick-hz-270_000_000 = ["embassy-time-driver/tick-hz-270_000_000"]
## 280.0MHz Tick Rate
tick-hz-280_000_000 = ["embassy-time-driver/tick-hz-280_000_000"]
## 288.0MHz Tick Rate
tick-hz-288_000_000 = ["embassy-time-driver/tick-hz-288_000_000"]
## 290.0MHz Tick Rate
tick-hz-290_000_000 = ["embassy-time-driver/tick-hz-290_000_000"]
## 300.0MHz Tick Rate
tick-hz-300_000_000 = ["embassy-time-driver/tick-hz-300_000_000"]
## 320.0MHz Tick Rate
tick-hz-320_000_000 = ["embassy-time-driver/tick-hz-320_000_000"]
## 327.68MHz Tick Rate
tick-hz-327_680_000 = ["embassy-time-driver/tick-hz-327_680_000"]
## 340.0MHz Tick Rate
tick-hz-340_000_000 = ["embassy-time-driver/tick-hz-340_000_000"]
## 360.0MHz Tick Rate
tick-hz-360_000_000 = ["embassy-time-driver/tick-hz-360_000_000"]
## 380.0MHz Tick Rate
tick-hz-380_000_000 = ["embassy-time-driver/tick-hz-380_000_000"]
## 384.0MHz Tick Rate
tick-hz-384_000_000 = ["embassy-time-driver/tick-hz-384_000_000"]
## 400.0MHz Tick Rate
tick-hz-400_000_000 = ["embassy-time-driver/tick-hz-400_000_000"]
## 420.0MHz Tick Rate
tick-hz-420_000_000 = ["embassy-time-driver/tick-hz-420_000_000"]
## 440.0MHz Tick Rate
tick-hz-440_000_000 = ["embassy-time-driver/tick-hz-440_000_000"]
## 460.0MHz Tick Rate
tick-hz-460_000_000 = ["embassy-time-driver/tick-hz-460_000_000"]
## 480.0MHz Tick Rate
tick-hz-480_000_000 = ["embassy-time-driver/tick-hz-480_000_000"]
## 500.0MHz Tick Rate
tick-hz-500_000_000 = ["embassy-time-driver/tick-hz-500_000_000"]
## 512.0MHz Tick Rate
tick-hz-512_000_000 = ["embassy-time-driver/tick-hz-512_000_000"]
## 520.0MHz Tick Rate
tick-hz-520_000_000 = ["embassy-time-driver/tick-hz-520_000_000"]
## 524.288MHz Tick Rate
tick-hz-524_288_000 = ["embassy-time-driver/tick-hz-524_288_000"]
## 540.0MHz Tick Rate
tick-hz-540_000_000 = ["embassy-time-driver/tick-hz-540_000_000"]
## 560.0MHz Tick Rate
tick-hz-560_000_000 = ["embassy-time-driver/tick-hz-560_000_000"]
## 576.0MHz Tick Rate
tick-hz-576_000_000 = ["embassy-time-driver/tick-hz-576_000_000"]
## 580.0MHz Tick Rate
tick-hz-580_000_000 = ["embassy-time-driver/tick-hz-580_000_000"]
## 600.0MHz Tick Rate
tick-hz-600_000_000 = ["embassy-time-driver/tick-hz-600_000_000"]
## 620.0MHz Tick Rate
tick-hz-620_000_000 = ["embassy-time-driver/tick-hz-620_000_000"]
## 640.0MHz Tick Rate
tick-hz-640_000_000 = ["embassy-time-driver/tick-hz-640_000_000"]
## 655.36MHz Tick Rate
tick-hz-655_360_000 = ["embassy-time-driver/tick-hz-655_360_000"]
## 660.0MHz Tick Rate
tick-hz-660_000_000 = ["embassy-time-driver/tick-hz-660_000_000"]
## 680.0MHz Tick Rate
tick-hz-680_000_000 = ["embassy-time-driver/tick-hz-680_000_000"]
## 700.0MHz Tick Rate
tick-hz-700_000_000 = ["embassy-time-driver/tick-hz-700_000_000"]
## 720.0MHz Tick Rate
tick-hz-720_000_000 = ["embassy-time-driver/tick-hz-720_000_000"]
## 740.0MHz Tick Rate
tick-hz-740_000_000 = ["embassy-time-driver/tick-hz-740_000_000"]
## 760.0MHz Tick Rate
tick-hz-760_000_000 = ["embassy-time-driver/tick-hz-760_000_000"]
## 768.0MHz Tick Rate
tick-hz-768_000_000 = ["embassy-time-driver/tick-hz-768_000_000"]
## 780.0MHz Tick Rate
tick-hz-780_000_000 = ["embassy-time-driver/tick-hz-780_000_000"]
## 800.0MHz Tick Rate
tick-hz-800_000_000 = ["embassy-time-driver/tick-hz-800_000_000"]
## 820.0MHz Tick Rate
tick-hz-820_000_000 = ["embassy-time-driver/tick-hz-820_000_000"]
## 840.0MHz Tick Rate
tick-hz-840_000_000 = ["embassy-time-driver/tick-hz-840_000_000"]
## 860.0MHz Tick Rate
tick-hz-860_000_000 = ["embassy-time-driver/tick-hz-860_000_000"]
## 880.0MHz Tick Rate
tick-hz-880_000_000 = ["embassy-time-driver/tick-hz-880_000_000"]
## 900.0MHz Tick Rate
tick-hz-900_000_000 = ["embassy-time-driver/tick-hz-900_000_000"]
## 920.0MHz Tick Rate
tick-hz-920_000_000 = ["embassy-time-driver/tick-hz-920_000_000"]
## 940.0MHz Tick Rate
tick-hz-940_000_000 = ["embassy-time-driver/tick-hz-940_000_000"]
## 960.0MHz Tick Rate
tick-hz-960_000_000 = ["embassy-time-driver/tick-hz-960_000_000"]
## 980.0MHz Tick Rate
tick-hz-980_000_000 = ["embassy-time-driver/tick-hz-980_000_000"]
## 1.0GHz Tick Rate
tick-hz-1_000_000_000 = ["embassy-time-driver/tick-hz-1_000_000_000"]
## 1.31072GHz Tick Rate
tick-hz-1_310_720_000 = ["embassy-time-driver/tick-hz-1_310_720_000"]
## 2.62144GHz Tick Rate
tick-hz-2_621_440_000 = ["embassy-time-driver/tick-hz-2_621_440_000"]
## 5.24288GHz Tick Rate
tick-hz-5_242_880_000 = ["embassy-time-driver/tick-hz-5_242_880_000"]
# END TICKS

#! </details>

[dependencies]
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver" }
embassy-time-queue-utils = { version = "0.3.0", path = "../embassy-time-queue-utils", optional = true}

defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }

embedded-hal-02 = { package = "embedded-hal", version = "0.2.6" }
embedded-hal-1 = { package = "embedded-hal", version = "1.0" }
embedded-hal-async = { version = "1.0" }

futures-core = { version = "0.3.31", default-features = false }
critical-section = "1.1"
cfg-if = "1.0.0"

document-features = "0.2.7"

# WASM dependencies
wasm-bindgen = { version = "0.2.81", optional = true }
js-sys = { version = "0.3", optional = true }
wasm-timer = { version = "0.2.5", optional = true }

[dev-dependencies]
serial_test = "0.9"
critical-section = { version = "1.1", features = ["std"] }
embassy-executor = { version = "0.9.0", path = "../embassy-executor" }
