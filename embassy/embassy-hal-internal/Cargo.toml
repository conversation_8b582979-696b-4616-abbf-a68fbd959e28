[package]
name = "embassy-hal-internal"
version = "0.3.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Internal implementation details for Embassy HALs. DO NOT USE DIRECTLY."
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-hal-internal"
categories = [
    "embedded",
    "no-std",
    "asynchronous",
]

[features]

# Define the number of NVIC priority bits.
prio-bits-0 = []
prio-bits-1 = []
prio-bits-2 = []
prio-bits-3 = []
prio-bits-4 = []
prio-bits-5 = []
prio-bits-6 = []
prio-bits-7 = []
prio-bits-8 = []

cortex-m = ["dep:cortex-m", "dep:critical-section"]

[dependencies]
defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }

num-traits = { version = "0.2.14", default-features = false }

cortex-m = { version = "0.7.6", optional = true }
critical-section = { version = "1", optional = true }
