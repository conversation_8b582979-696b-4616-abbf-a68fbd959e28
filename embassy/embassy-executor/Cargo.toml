[package]
name = "embassy-executor"
version = "0.9.1"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "async/await executor designed for embedded usage"
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-executor"
categories = [
    "embedded",
    "no-std",
    "asynchronous",
]

[package.metadata.embassy]
build = [
    {target = "thumbv7em-none-eabi", features = []},
    {target = "thumbv7em-none-eabi", features = ["log"]},
    {target = "thumbv7em-none-eabi", features = ["defmt"]},
    {target = "thumbv6m-none-eabi", features = ["defmt"]},
    {target = "thumbv6m-none-eabi", features = ["arch-cortex-m", "defmt", "executor-interrupt", "executor-thread"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "rtos-trace"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-thread"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread", "embassy-time-driver"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread", "embassy-time-driver", "scheduler-priority"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread", "embassy-time-driver", "scheduler-priority", "scheduler-deadline"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread", "embassy-time-driver", "scheduler-deadline"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread", "scheduler-priority", "scheduler-deadline"]},
    {target = "thumbv7em-none-eabi", features = ["arch-cortex-m", "executor-interrupt", "executor-thread", "scheduler-deadline"]},
    {target = "armv7a-none-eabi", features = ["arch-cortex-ar", "executor-thread"]},
    {target = "armv7r-none-eabi", features = ["arch-cortex-ar", "executor-thread"]},
    {target = "armv7r-none-eabihf", features = ["arch-cortex-ar", "executor-thread"]},
    {target = "riscv32imac-unknown-none-elf", features = ["arch-riscv32"]},
    {target = "riscv32imac-unknown-none-elf", features = ["arch-riscv32", "executor-thread"]},
    # Nightly builds
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly"]},
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly", "log"]},
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly", "defmt"]},
    {group = "nightly", target = "thumbv6m-none-eabi", features = ["nightly", "defmt"]},
    {group = "nightly", target = "thumbv6m-none-eabi", features = ["nightly", "defmt", "arch-cortex-m", "executor-thread", "executor-interrupt"]},
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly", "arch-cortex-m"]},
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly", "arch-cortex-m", "executor-thread"]},
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly", "arch-cortex-m", "executor-interrupt"]},
    {group = "nightly", target = "thumbv7em-none-eabi", features = ["nightly", "arch-cortex-m", "executor-thread", "executor-interrupt"]},
    {group = "nightly", target = "riscv32imac-unknown-none-elf", features = ["nightly", "arch-riscv32"]},
    {group = "nightly", target = "riscv32imac-unknown-none-elf", features = ["nightly", "arch-riscv32", "executor-thread"]},
    {group = "nightly", target = "armv7a-none-eabi", features = ["nightly", "arch-cortex-ar", "executor-thread"]},
    {group = "nightly", target = "avr-none", features = ["nightly", "arch-avr", "avr-device/atmega328p"], build-std = ["core", "alloc"], env = { RUSTFLAGS = "-Ctarget-cpu=atmega328p" }},
    # Xtensa builds
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = []},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = ["log"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = ["defmt"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32s2-none-elf", features = ["defmt"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = ["defmt", "arch-spin", "executor-thread"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32s2-none-elf", features = ["defmt", "arch-spin", "executor-thread"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32s3-none-elf", features = ["defmt", "arch-spin", "executor-thread"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = ["arch-spin"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = ["arch-spin", "rtos-trace"]},
    {group = "xtensa", build-std = ["core", "alloc"],  target = "xtensa-esp32-none-elf", features = ["arch-spin", "executor-thread"]},
]


[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-executor-v$VERSION/embassy-executor/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-executor/src/"
features = ["defmt", "scheduler-deadline", "scheduler-priority"]
flavors = [
    { name = "std",             target = "x86_64-unknown-linux-gnu",     features = ["arch-std", "executor-thread"] },
    { name = "wasm",            target = "wasm32-unknown-unknown",       features = ["arch-wasm", "executor-thread"] },
    { name = "cortex-m",        target = "thumbv7em-none-eabi",          features = ["arch-cortex-m", "executor-thread", "executor-interrupt"] },
    { name = "riscv32",         target = "riscv32imac-unknown-none-elf", features = ["arch-riscv32", "executor-thread"] },
]

[package.metadata.docs.rs]
default-target = "thumbv7em-none-eabi"
targets = ["thumbv7em-none-eabi"]
features = ["defmt", "arch-cortex-m", "executor-thread", "executor-interrupt", "scheduler-deadline", "scheduler-priority", "embassy-time-driver"]

[dependencies]
defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }
rtos-trace = { version = "0.2", optional = true }

embassy-executor-macros = { version = "0.7.0", path = "../embassy-executor-macros" }
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver", optional = true }
embassy-executor-timer-queue = { version = "0.1", path = "../embassy-executor-timer-queue" }
critical-section = "1.1"

document-features = "0.2.7"

# needed for AVR
portable-atomic = { version = "1.5", optional = true }

# arch-cortex-m dependencies
cortex-m = { version = "0.7.6", optional = true }

# arch-cortex-ar dependencies
cortex-ar = { version = "0.2", optional = true }

# arch-wasm dependencies
wasm-bindgen = { version = "0.2.82", optional = true }
js-sys = { version = "0.3", optional = true }

# arch-avr dependencies
avr-device = { version = "0.7.0", optional = true }


[dependencies.cordyceps]
version = "0.3.4"
features = ["no-cache-pad"]

[dev-dependencies]
critical-section = { version = "1.1", features = ["std"] }
trybuild = "1.0"
embassy-sync = { path = "../embassy-sync" }
rustversion = "1.0.21"

[features]

## Enable nightly-only features
nightly = ["embassy-executor-macros/nightly"]

# Enables turbo wakers, which requires patching core. Not surfaced in the docs by default due to
# being an complicated advanced and undocumented feature.
# See: https://github.com/embassy-rs/embassy/pull/1263
turbowakers = []

#! ### Architecture
_arch = [] # some arch was picked
## std
arch-std = ["_arch"]
## Cortex-M
arch-cortex-m = ["_arch", "dep:cortex-m"]
## Cortex-A/R
arch-cortex-ar = ["_arch", "dep:cortex-ar"]
## RISC-V 32
arch-riscv32 = ["_arch"]
## WASM
arch-wasm = ["_arch", "dep:wasm-bindgen", "dep:js-sys"]
## AVR
arch-avr = ["_arch", "dep:portable-atomic", "dep:avr-device"]
## spin (architecture agnostic; never sleeps)
arch-spin = ["_arch"]

#! ### Metadata

## Enable the `name` field in task metadata.
metadata-name = ["embassy-executor-macros/metadata-name"]

#! ### Executor

## Enable the thread-mode executor (using WFE/SEV in Cortex-M, WFI in other embedded archs)
executor-thread = []
## Enable the interrupt-mode executor (available in Cortex-M only)
executor-interrupt = []
## Enable tracing hooks
trace = ["_any_trace"]
## Enable support for rtos-trace framework
rtos-trace = ["_any_trace", "metadata-name", "dep:rtos-trace", "embassy-time-driver"]
_any_trace = []

## Enable "Earliest Deadline First" Scheduler, using soft-realtime "deadlines" to prioritize
## tasks based on the remaining time before their deadline. Adds some overhead.
scheduler-deadline = []

## Enable "Highest Priority First" Scheduler. Adds some overhead.
scheduler-priority = []

## Enable the embassy_time_driver dependency.
## This can unlock extra APIs, for example for the `sheduler-deadline`
embassy-time-driver = ["dep:embassy-time-driver"]
