import os

abspath = os.path.abspath(__file__)
dname = os.path.dirname(abspath)
os.chdir(dname)

features = []


def feature(name, default, min=None, max=None, pow2=None, vals=None, factors=[]):
    if vals is None:
        assert min is not None
        assert max is not None

        vals = set()
        val = min
        while val <= max:
            vals.add(val)
            for f in factors:
                if val * f <= max:
                    vals.add(val * f)
            if (pow2 == True or (isinstance(pow2, int) and val >= pow2)) and val > 0:
                val *= 2
            else:
                val += 1
        vals.add(default)
        vals = sorted(list(vals))

    features.append(
        {
            "name": name,
            "default": default,
            "vals": vals,
        }
    )


feature(
    "task_arena_size", default=4096, min=64, max=1024 * 1024, pow2=True, factors=[3, 5]
)

# ========= Update Cargo.toml

things = ""
for f in features:
    name = f["name"].replace("_", "-")
    for val in f["vals"]:
        things += f"## {val}"
        if val == f["default"]:
            things += " (default)\n"
        else:
            things += "\n"
            
        things += f"{name}-{val} = []"
        if val == f["default"]:
            things += " # Default"
        things += "\n"
    things += "\n"

SEPARATOR_START = "# BEGIN AUTOGENERATED CONFIG FEATURES\n"
SEPARATOR_END = "# END AUTOGENERATED CONFIG FEATURES\n"
HELP = "# Generated by gen_config.py. DO NOT EDIT.\n"
with open("Cargo.toml", "r") as f:
    data = f.read()
before, data = data.split(SEPARATOR_START, maxsplit=1)
_, after = data.split(SEPARATOR_END, maxsplit=1)
data = before + SEPARATOR_START + HELP + things + SEPARATOR_END + after
with open("Cargo.toml", "w") as f:
    f.write(data)


# ========= Update build.rs

things = ""
for f in features:
    name = f["name"].upper()
    things += f'    ("{name}", {f["default"]}),\n'

SEPARATOR_START = "// BEGIN AUTOGENERATED CONFIG FEATURES\n"
SEPARATOR_END = "// END AUTOGENERATED CONFIG FEATURES\n"
HELP = "    // Generated by gen_config.py. DO NOT EDIT.\n"
with open("build.rs", "r") as f:
    data = f.read()
before, data = data.split(SEPARATOR_START, maxsplit=1)
_, after = data.split(SEPARATOR_END, maxsplit=1)
data = before + SEPARATOR_START + HELP + things + "    " + SEPARATOR_END + after
with open("build.rs", "w") as f:
    f.write(data)
