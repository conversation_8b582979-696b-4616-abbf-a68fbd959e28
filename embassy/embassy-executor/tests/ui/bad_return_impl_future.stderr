error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:5:4
  |
4 | #[embassy_executor::task]
  | ------------------------- required by a bound introduced by this call
5 | fn task() -> impl Future<Output = u32> {
  |    ^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `task_pool_size`
 --> src/lib.rs
  |
  |     pub const fn task_pool_size<F, Args, Fut, const POOL_SIZE: usize>(_: F) -> usize
  |                  -------------- required by a bound in this function
  |     where
  |         F: TaskFn<Args, Fut = Fut>,
  |                         ^^^^^^^^^ required by this bound in `task_pool_size`

error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:4:1
  |
4 | #[embassy_executor::task]
  | ^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `task_pool_size`
 --> src/lib.rs
  |
  |     pub const fn task_pool_size<F, Args, Fut, const POOL_SIZE: usize>(_: F) -> usize
  |                  -------------- required by a bound in this function
  |     where
  |         F: TaskFn<Args, Fut = Fut>,
  |            ^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `task_pool_size`
  = note: this error originates in the attribute macro `embassy_executor::task` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:5:4
  |
4 | #[embassy_executor::task]
  | ------------------------- required by a bound introduced by this call
5 | fn task() -> impl Future<Output = u32> {
  |    ^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `task_pool_align`
 --> src/lib.rs
  |
  |     pub const fn task_pool_align<F, Args, Fut, const POOL_SIZE: usize>(_: F) -> usize
  |                  --------------- required by a bound in this function
  |     where
  |         F: TaskFn<Args, Fut = Fut>,
  |                         ^^^^^^^^^ required by this bound in `task_pool_align`

error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:4:1
  |
4 | #[embassy_executor::task]
  | ^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `task_pool_align`
 --> src/lib.rs
  |
  |     pub const fn task_pool_align<F, Args, Fut, const POOL_SIZE: usize>(_: F) -> usize
  |                  --------------- required by a bound in this function
  |     where
  |         F: TaskFn<Args, Fut = Fut>,
  |            ^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `task_pool_align`
  = note: this error originates in the attribute macro `embassy_executor::task` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:5:4
  |
4 | #[embassy_executor::task]
  | ------------------------- required by a bound introduced by this call
5 | fn task() -> impl Future<Output = u32> {
  |    ^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `__task_pool_get`
 --> tests/ui/bad_return_impl_future.rs:4:1
  |
4 | #[embassy_executor::task]
  | ^^^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `__task_pool_get`
  = note: this error originates in the attribute macro `embassy_executor::task` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:5:4
  |
4 | #[embassy_executor::task]
  | ------------------------- required by a bound introduced by this call
5 | fn task() -> impl Future<Output = u32> {
  |    ^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `task_pool_new`
 --> src/lib.rs
  |
  |     pub const fn task_pool_new<F, Args, Fut, const POOL_SIZE: usize>(_: F) -> TaskPool<Fut, POOL_SIZE>
  |                  ------------- required by a bound in this function
  |     where
  |         F: TaskFn<Args, Fut = Fut>,
  |                         ^^^^^^^^^ required by this bound in `task_pool_new`

error[E0277]: task futures must resolve to `()` or `!`
 --> tests/ui/bad_return_impl_future.rs:4:1
  |
4 | #[embassy_executor::task]
  | ^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `TaskFn<_>` is not implemented for fn item `fn() -> impl Future<Output = u32> {__task_task}`
  |
  = note: use `async fn` or change the return type to `impl Future<Output = ()>`
note: required by a bound in `task_pool_new`
 --> src/lib.rs
  |
  |     pub const fn task_pool_new<F, Args, Fut, const POOL_SIZE: usize>(_: F) -> TaskPool<Fut, POOL_SIZE>
  |                  ------------- required by a bound in this function
  |     where
  |         F: TaskFn<Args, Fut = Fut>,
  |            ^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `task_pool_new`
  = note: this error originates in the attribute macro `embassy_executor::task` (in Nightly builds, run with -Z macro-backtrace for more info)
