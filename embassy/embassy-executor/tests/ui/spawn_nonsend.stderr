warning: unused import: `core::future::Future`
 --> tests/ui/spawn_nonsend.rs:3:5
  |
3 | use core::future::Future;
  |     ^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

error[E0277]: `*mut ()` cannot be sent between threads safely
  --> tests/ui/spawn_nonsend.rs:13:13
   |
7  | #[embassy_executor::task]
   | ------------------------- within this `impl Sized`
...
13 |     s.spawn(task(core::ptr::null_mut()).unwrap());
   |       ----- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `*mut ()` cannot be sent between threads safely
   |       |
   |       required by a bound introduced by this call
   |
   = help: within `impl Sized`, the trait `Send` is not implemented for `*mut ()`
note: required because it's used within this closure
  --> tests/ui/spawn_nonsend.rs:7:1
   |
7  | #[embassy_executor::task]
   | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: required because it appears within the type `impl Sized`
  --> src/raw/mod.rs
   |
   |     pub unsafe fn _spawn_async_fn<FutFn>(&'static self, future: FutFn) -> Result<SpawnToken<impl Sized>, SpawnError>
   |                                                                                             ^^^^^^^^^^
note: required because it appears within the type `impl Sized`
  --> tests/ui/spawn_nonsend.rs:7:1
   |
7  | #[embassy_executor::task]
   | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: required by a bound in `SendSpawner::spawn`
  --> src/spawner.rs
   |
   |     pub fn spawn<S: Send>(&self, token: SpawnToken<S>) {
   |                     ^^^^ required by this bound in `SendSpawner::spawn`
   = note: this error originates in the attribute macro `embassy_executor::task` (in Nightly builds, run with -Z macro-backtrace for more info)
