error[E0133]: call to unsafe function `std::ptr::const_ptr::<impl *const T>::read` is unsafe and requires unsafe block
 --> tests/ui/unsafe_op_in_unsafe_task.rs:7:5
  |
7 |     (&x as *const i32).read();
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^ call to unsafe function
  |
  = note: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/unsafe-op-in-unsafe-fn.html>
  = note: consult the function's documentation for information on how to avoid undefined behavior
note: an unsafe function restricts its caller, but its body is safe by default
 --> tests/ui/unsafe_op_in_unsafe_task.rs:5:1
  |
5 | async unsafe fn task() {
  | ^^^^^^^^^^^^^^^^^^^^^^
note: the lint level is defined here
 --> tests/ui/unsafe_op_in_unsafe_task.rs:2:9
  |
2 | #![deny(unsafe_op_in_unsafe_fn)]
  |         ^^^^^^^^^^^^^^^^^^^^^^
