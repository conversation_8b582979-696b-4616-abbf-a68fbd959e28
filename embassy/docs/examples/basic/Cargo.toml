[package]
authors = ["<PERSON><PERSON> <<EMAIL>>"]
edition = "2018"
name = "embassy-basic-example"
version = "0.1.0"
license = "MIT OR Apache-2.0"

publish = false
[dependencies]
embassy-executor = { version = "0.9.0", path = "../../../embassy-executor", features = ["defmt", "arch-cortex-m", "executor-thread"] }
embassy-time = { version = "0.5.0", path = "../../../embassy-time", features = ["defmt"] }
embassy-nrf = { version = "0.7.0", path = "../../../embassy-nrf", features = ["defmt", "nrf52840", "time-driver-rtc1", "gpiote"] }

defmt = "1.0.1"
defmt-rtt = "1.0.0"

cortex-m = { version = "0.7.6", features = ["critical-section-single-core"] }
cortex-m-rt = "0.7.0"
panic-probe = { version = "1.0.0", features = ["print-defmt"] }

[package.metadata.embassy]
build = [
  { target = "thumbv7em-none-eabi" }
]
