= From bare metal to async Rust

If you're new to Embassy, it can be overwhelming to grasp all the terminology and concepts. This guide aims to clarify the different layers in Embassy, which problem each layer solves for the application writer.

This guide uses the STM32 IOT01A board, but should be easy to translate to any STM32 chip. For nRF, the PAC itself is not maintained within the Embassy project, but the concepts and the layers are similar.

The application we'll write is a simple 'push button, blink led' application, which is great for illustrating input and output handling for each of the examples we'll go through. We'll start at the Peripheral Access Crate (PAC) example and end at the async example.

== PAC version

The PAC is the lowest API for accessing peripherals and registers, if you don't count reading/writing directly to memory addresses. It provides distinct types to make accessing peripheral registers easier, but it does little to prevent you from configuring or coordinating those registers incorrectly.

Writing an application using the PAC directly is therefore not recommended, but if the functionality you want to use is not exposed in the upper layers, that's what you need to use.

The blinky app using PAC is shown below:

[source,rust]
----
include::../examples/layer-by-layer/blinky-pac/src/main.rs[]
----

As you can see, a lot of code is needed to enable the peripheral clocks and to configure the input pins and the output pins of the application.

Another downside of this application is that it is busy-looping while polling the button state. This prevents the microcontroller from utilizing any sleep mode to save power.

== HAL version

To simplify our application, we can use the HAL instead. The HAL exposes higher level APIs that handle details such as:

* Automatically enabling the peripheral clock when you're using the peripheral
* Deriving and applying register configuration from higher level types
* Implementing the embedded-hal traits to make peripherals useful in third party drivers

The HAL example is shown below:

[source,rust]
----
include::../examples/layer-by-layer/blinky-hal/src/main.rs[]
----

As you can see, the application becomes a lot simpler, even without using any async code. The `Input` and `Output` types hide all the details of accessing the GPIO registers and allow you to use a much simpler API for querying the state of the button and toggling the LED output.

The same downside from the PAC example still applies though: the application is busy looping and consuming more power than necessary.

== Interrupt driven

To save power, we need to configure the application so that it can be notified when the button is pressed using an interrupt.

Once the interrupt is configured, the application can instruct the microcontroller to enter a sleep mode, consuming very little power.

Given Embassy focus on async Rust (which we'll come back to after this example), the example application must use a combination of the HAL and PAC in order to use interrupts. For this reason, the application also contains some helper functions to access the PAC (not shown below).

[source,rust]
----
include::../examples/layer-by-layer/blinky-irq/src/main.rs[lines="1..57"]
----

The simple application is now more complex again, primarily because of the need to keep the button and LED states in the global scope where it is accessible by the main application loop, as well as the interrupt handler.

To do that, the types must be guarded by a mutex, and interrupts must be disabled whenever we are accessing this global state to gain access to the peripherals.

Luckily, there is an elegant solution to this problem when using Embassy.

== Async version

It's time to use the Embassy capabilities to its fullest. At the core, Embassy has an async executor, or a runtime for async tasks if you will. The executor polls a set of tasks (defined at compile time), and whenever a task `blocks`, the executor will run another task, or put the microcontroller to sleep.

[source,rust]
----
include::../examples/layer-by-layer/blinky-async/src/main.rs[]
----

The async version looks very similar to the HAL version, apart from a few minor details:

* The main entry point is annotated with a different macro and has an async type signature. This macro creates and starts an Embassy runtime instance and launches the main application task. Using the `Spawner` instance, the application may spawn other tasks.
* The peripheral initialization is done by the main macro, and is handed to the main task.
* Before checking the button state, the application is awaiting a transition in the pin state (low -> high or high -> low).

When `button.wait_for_any_edge().await` is called, the executor will pause the main task and put the microcontroller in sleep mode, unless there are other tasks that can run. Internally, the Embassy HAL has configured the interrupt handler for the button (in `ExtiInput`), so that whenever an interrupt is raised, the task awaiting the button will be woken up.

The minimal overhead of the executor and the ability to run multiple tasks "concurrently" combined with the enormous simplification of the application, makes `async` a great fit for embedded.

== Summary

We have seen how the same application can be written at the different abstraction levels in Embassy. First starting out at the PAC level, then using the HAL, then using interrupts, and then using interrupts indirectly using async Rust.
