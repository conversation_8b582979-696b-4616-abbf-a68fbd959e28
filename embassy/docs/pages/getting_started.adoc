= Getting started

So you want to try Embassy, great! To get started, there are a few tools you need to install:

* link:https://rustup.rs/[rustup] - the Rust toolchain is needed to compile Rust code.
* link:https://probe.rs/[probe-rs] - to flash the firmware on your device. If you already have other tools like `OpenOCD` setup, you can use that as well.

If you don't have any supported board, don't worry: you can also run embassy on your PC using the `std` examples.

== Getting a board with examples

Embassy supports many microcontroller families, but the quickest way to get started is by using a board which Embassy has existing example code for.

This list is non-exhaustive. If your board isn’t included here, check the link:https://github.com/embassy-rs/embassy/tree/main/examples[examples folder] to see if example code has been written for it.

=== nRF kits

* link:https://www.nordicsemi.com/Products/Development-hardware/nrf52-dk[nRF52 DK]
* link:https://www.nordicsemi.com/Products/Development-hardware/nRF9160-DK[nRF9160 DK]

=== STM32 kits

* link:https://www.st.com/en/evaluation-tools/nucleo-h743zi.html[STM32 Nucleo-144 development board with STM32H743ZI MCU]
* link:https://www.st.com/en/evaluation-tools/nucleo-f429zi.html[STM32 Nucleo-144 development board with STM32F429ZI MCU]
* link:https://www.st.com/en/evaluation-tools/b-l4s5i-iot01a.html[STM32L4+ Discovery kit IoT node, low-power wireless, BLE, NFC, WiFi]
* link:https://www.st.com/en/evaluation-tools/b-l072z-lrwan1.html[STM32L0 Discovery kit LoRa, Sigfox, low-power wireless]
* link:https://www.st.com/en/evaluation-tools/nucleo-wl55jc.html[STM32 Nucleo-64 development board with STM32WL55JCI MCU]
* link:https://www.st.com/en/evaluation-tools/b-u585i-iot02a.html[Discovery kit for IoT node with STM32U5 series]


=== RP2040 kits

* link:https://www.raspberrypi.com/products/raspberry-pi-pico/[Raspberry Pi Pico]

=== ESP32

* link:https://github.com/esp-rs/esp-rust-board[ESP32C3]

== Running an example

First you need to clone the link:https://github.com/embassy-rs/embassy[github repository];

[source, bash]
----
git clone https://github.com/embassy-rs/embassy.git
cd embassy
----

Once you have a copy of the repository, find examples folder for your board and, and build an example program. `blinky` is a good choice as all it does is blink an LED – the embedded world’s equivalent of “Hello World”.

[source, bash]
----
cd examples/nrf52840
cargo build --bin blinky --release
----

Once you’ve confirmed you can build the example, connect your computer to your board with a debug probe and run it on hardware:

[source, bash]
----
cargo run --bin blinky --release
----

If everything worked correctly, you should see a blinking LED on your board, and debug output similar to this on your computer:

[source]
----
    Finished dev [unoptimized + debuginfo] target(s) in 1m 56s
     Running `probe-rs run --chip STM32F407VGTx target/thumbv7em-none-eabi/debug/blinky`
(HOST) INFO  flashing program (71.36 KiB)
(HOST) INFO  success!
────────────────────────────────────────────────────────────────────────────────
0 INFO  Hello World!
└─ blinky::__embassy_main::task::{generator#0} @ src/bin/blinky.rs:18
1 INFO  high
└─ blinky::__embassy_main::task::{generator#0} @ src/bin/blinky.rs:23
2 INFO  low
└─ blinky::__embassy_main::task::{generator#0} @ src/bin/blinky.rs:27
3 INFO  high
└─ blinky::__embassy_main::task::{generator#0} @ src/bin/blinky.rs:23
4 INFO  low
└─ blinky::__embassy_main::task::{generator#0} @ src/bin/blinky.rs:27
----

NOTE: How does the `+cargo run+` command know how to connect to our board and program it? In each `examples` folder, there’s a `.cargo/config.toml` file which tells cargo to use link:https://probe.rs/[probe-rs] as the runner for ARM binaries in that folder. probe-rs handles communication with the debug probe and MCU. In order for this to work, probe-rs needs to know which chip it’s programming, so you’ll have to edit this file if you want to run examples on other chips.

=== It didn’t work!

If you are having issues when running `+cargo run --release+`, please check the following:

* You are specifying the correct `+--chip+` on the command line, OR
* You have set `+.cargo/config.toml+`’s run line to the correct chip, AND
* You have changed `+examples/Cargo.toml+`’s HAL (e.g. embassy-stm32) dependency's feature to use the correct chip (replace the existing stm32xxxx feature)

At this point the project should run. If you do not see a blinky LED for blinky, for example, be sure to check the code is toggling your board's LED pin.

If you are trying to run an example with `+cargo run --release+` and you see the following output:
[source]
----
0.000000 INFO Hello World!
└─ <invalid location: defmt frame-index: 14>
0.000000 DEBUG rcc: Clocks { sys: Hertz(80000000), apb1: Hertz(80000000), apb1_tim: Hertz(80000000), apb2: Hertz(80000000), apb2_tim: Hertz(80000000), ahb1: Hertz(80000000), ahb2: Hertz(80000000), ahb3: Hertz(80000000) }
└─ <invalid location: defmt frame-index: 124>
0.000061 TRACE allocating type=Interrupt mps=8 interval_ms=255, dir=In
└─ <invalid location: defmt frame-index: 68>
0.000091 TRACE   index=1
└─ <invalid location: defmt frame-index: 72>
----

To get rid of the frame-index error add the following to your `Cargo.toml`:

[source,toml]
----
[profile.release]
debug = 2
----

If you’re getting an extremely long error message containing something like the following:

[source]
----
error[E0463]: can't find crate for `std`
  |
  = note: the `thumbv6m-none-eabi` target may not support the standard library
  = note: `std` is required by `stable_deref_trait` because it does not declare `#![no_std]`
----

Make sure that you didn’t accidentally run `+cargo add probe-rs+` (which adds it as a dependency) instead of link:https://probe.rs/docs/getting-started/installation/[correctly installing probe-rs].

If you’re using a raspberry pi pico-w, make sure you’re running `+cargo run --bin wifi_blinky --release+` rather than the regular blinky. The pico-w’s on-board LED is connected to the WiFi chip, which needs to be initialized before the LED can be blinked.

If you’re using an rp2040 debug probe (e.g. the pico probe) and are having issues after running `probe-rs info`, unplug and reconnect the probe, letting it power cycle. Running `probe-rs info` is link:https://github.com/probe-rs/probe-rs/issues/1849[known to put the pico probe into an unusable state].

:embassy-dev-faq-link-with-hash: https://embassy.dev/book/#_frequently_asked_questions
:embassy-matrix-channel: https://matrix.to/#/#embassy-rs:matrix.org

If you’re still having problems, check the {embassy-dev-faq-link-with-hash}[FAQ], or ask for help in the {embassy-matrix-channel}[Embassy Chat Room].

== What's next?

Congratulations, you have your first Embassy application running! Here are some suggestions for where to go from here:

* Read more about the xref:_embassy_executor[executor].
* Read more about the xref:_hardware_abstraction_layer_hal[HAL].
* Start xref:_a_basic_embassy_application[writing your application].
* Learn how to xref:_starting_a_new_project[start a new embassy project by adapting an example].
