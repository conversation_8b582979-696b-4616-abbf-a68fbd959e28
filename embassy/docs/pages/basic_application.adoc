= A basic Embassy application

So you've got one of the examples running, but what now? Let's go through a simple Embassy application for the nRF52 DK to understand it better.

== Main

The full example can be found link:https://github.com/embassy-rs/embassy/tree/main/docs/examples/basic[here].

NOTE: If you’re using VS Code and rust-analyzer to view and edit the examples, you may need to make some changes to `.vscode/settings.json` to tell it which project we’re working on. Follow the instructions commented in that file to get rust-analyzer working correctly.

=== Bare metal

The first thing you’ll notice are two attributes at the top of the file. These tells the compiler that program has no access to std, and that there is no main function (because it is not run by an OS).

[source,rust]
----
include::../examples/basic/src/main.rs[lines="1..2"]
----

=== Dealing with errors

Then, what follows are some declarations on how to deal with panics and faults. During development, a good practice is to rely on `defmt-rtt` and `panic-probe` to print diagnostics to the terminal:

[source,rust]
----
include::../examples/basic/src/main.rs[lines="8"]
----

=== Task declaration

After a bit of import declaration, the tasks run by the application should be declared:

[source,rust]
----
include::../examples/basic/src/main.rs[lines="10..18"]
----

An embassy task must be declared `async`, and may NOT take generic arguments. In this case, we are handed the LED that should be blinked and the interval of the blinking.

NOTE: Notice that there is no busy waiting going on in this task. It is using the Embassy timer to yield execution, allowing the microcontroller to sleep in between the blinking.

=== Main

The main entry point of an Embassy application is defined using the `#[embassy_executor::main]` macro. The entry point is passed a `Spawner`, which it can use to spawn other tasks.

We then initialize the HAL with a default config, which gives us a `Peripherals` struct we can use to access the MCU’s various peripherals. In this case, we want to configure one of the pins as a GPIO output driving the LED:

[source,rust]
----
include::../examples/basic/src/main.rs[lines="20..-1"]
----

What happens when the `blinker` task has been spawned and main returns? Well, the main entry point is actually just like any other task, except that you can only have one and it takes some specific type arguments. The magic lies within the `#[embassy_executor::main]` macro. The macro does the following:

. Creates an Embassy Executor
. Defines a main task for the entry point
. Runs the executor spawning the main task

There is also a way to run the executor without using the macro, in which case you have to create the `Executor` instance yourself.

== The Cargo.toml

The project definition needs to contain the embassy dependencies:

[source,toml]
----
include::../examples/basic/Cargo.toml[lines="9..11"]
----

Depending on your microcontroller, you may need to replace `embassy-nrf` with something else (`embassy-stm32` for STM32. Remember to update feature flags as well).

In this particular case, the nrf52840 chip is selected, and the RTC1 peripheral is used as the time driver.
