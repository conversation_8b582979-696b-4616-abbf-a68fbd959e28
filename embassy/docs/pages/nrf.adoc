= Embassy nRF HAL

The link:https://github.com/embassy-rs/embassy/tree/main/embassy-nrf[Embassy nRF HAL] is based on the PACs (Peripheral Access Crate) from link:https://github.com/nrf-rs/[nrf-rs].

== Timer driver

The nRF timer driver operates at 32768 Hz by default.

== Peripherals

The following peripherals have a HAL implementation at present

* PWM
* SPIM
* QSPI
* NVMC
* GPIOTE
* RNG
* TIMER
* WDT
* TEMP
* PPI
* UARTE
* TWIM
* SAADC

== Bluetooth

For bluetooth, you can use the link:https://github.com/embassy-rs/nrf-softdevice[nrf-softdevice] crate.
