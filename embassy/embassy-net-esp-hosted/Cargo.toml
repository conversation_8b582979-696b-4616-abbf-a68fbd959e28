[package]
name = "embassy-net-esp-hosted"
version = "0.2.1"
edition = "2021"
description = "embassy-net driver for ESP-Hosted"
keywords = ["embedded", "esp-hosted", "embassy-net", "embedded-hal-async", "wifi"]
categories = ["embedded", "hardware-support", "no-std", "network-programming", "asynchronous"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-net-esp-hosted"

[features]
defmt = ["dep:defmt", "heapless/defmt-03"]
log = ["dep:log"]

[dependencies]
defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }

embassy-time = { version = "0.5.0", path = "../embassy-time" }
embassy-sync = { version = "0.7.2", path = "../embassy-sync"}
embassy-futures = { version = "0.1.2", path = "../embassy-futures"}
embassy-net-driver-channel = { version = "0.3.2", path = "../embassy-net-driver-channel"}

embedded-hal = { version = "1.0" }
embedded-hal-async = { version = "1.0" }

noproto = "0.1.0"
heapless = "0.8"

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-net-esp-hosted-v$VERSION/embassy-net-esp-hosted/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-net-esp-hosted/src/"
target = "thumbv7em-none-eabi"
features = ["defmt"]

[package.metadata.docs.rs]
features = ["defmt"]
