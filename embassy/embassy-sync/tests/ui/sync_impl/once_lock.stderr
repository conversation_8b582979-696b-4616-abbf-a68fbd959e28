error[E0277]: `*mut u8` cannot be shared between threads safely
 --> tests/ui/sync_impl/once_lock.rs:4:16
  |
4 | static GLOBAL: OnceLock<*mut u8> = OnceLock::new();
  |                ^^^^^^^^^^^^^^^^^ `*mut u8` cannot be shared between threads safely
  |
  = help: the trait `Sync` is not implemented for `*mut u8`
  = note: required for `embassy_sync::once_lock::OnceLock<*mut u8>` to implement `Sync`
  = note: shared static variables must have a type that implements `Sync`
