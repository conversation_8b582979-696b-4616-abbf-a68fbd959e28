[package]
name = "embassy-net-driver-channel"
version = "0.3.2"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "High-level channel-based driver for the `embassy-net` async TCP/IP network stack."
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-net-driver-channel"
categories = [
    "embedded",
    "no-std",
    "asynchronous",
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-net-driver-channel-v$VERSION/embassy-net-driver-channel/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-net-driver-channel/src/"
features = ["defmt"]
target = "thumbv7em-none-eabi"

[package.metadata.docs.rs]
features = ["defmt"]

[dependencies]
defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }

embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
embassy-net-driver = { version = "0.2.0", path = "../embassy-net-driver" }
