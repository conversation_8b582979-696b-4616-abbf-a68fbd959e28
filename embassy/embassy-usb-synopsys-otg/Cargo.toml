[package]
name = "embassy-usb-synopsys-otg"
version = "0.3.1"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "`embassy-usb-driver` implementation for Synopsys OTG USB controllers"
keywords = ["embedded", "async", "usb", "hal", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-usb-synopsys-otg"

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-usb-synopsys-otg-v$VERSION/embassy-usb-synopsys-otg/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-usb-synopsys-otg/src/"
features = ["defmt"]
target = "thumbv7em-none-eabi"

[dependencies]
critical-section = "1.1"

embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-usb-driver = { version = "0.2.0", path = "../embassy-usb-driver" }

defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }
