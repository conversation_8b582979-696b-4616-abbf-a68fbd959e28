import os

abspath = os.path.abspath(__file__)
dname = os.path.dirname(abspath)
os.chdir(dname)

features = []


def feature(name, default, min, max, pow2=None):
    vals = set()
    val = min
    while val <= max:
        vals.add(val)
        if pow2 == True or (isinstance(pow2, int) and val >= pow2):
            val *= 2
        else:
            val += 1
    vals.add(default)

    features.append(
        {
            "name": name,
            "default": default,
            "vals": sorted(list(vals)),
        }
    )


feature("max_interface_count", default=4, min=1, max=8)
feature("max_handler_count", default=4, min=1, max=8)

# ========= Update Cargo.toml

things = ""
for f in features:
    name = f["name"].replace("_", "-")
    for val in f["vals"]:
        things += f"{name}-{val} = []"
        if val == f["default"]:
            things += " # Default"
        things += "\n"
    things += "\n"

SEPARATOR_START = "# BEGIN AUTOGENERATED CONFIG FEATURES\n"
SEPARATOR_END = "# END AUTOGENERATED CONFIG FEATURES\n"
HELP = "# Generated by gen_config.py. DO NOT EDIT.\n"
with open("Cargo.toml", "r") as f:
    data = f.read()
before, data = data.split(SEPARATOR_START, maxsplit=1)
_, after = data.split(SEPARATOR_END, maxsplit=1)
data = before + SEPARATOR_START + HELP + things + SEPARATOR_END + after
with open("Cargo.toml", "w") as f:
    f.write(data)


# ========= Update build.rs

things = ""
for f in features:
    name = f["name"].upper()
    things += f'    ("{name}", {f["default"]}),\n'

SEPARATOR_START = "// BEGIN AUTOGENERATED CONFIG FEATURES\n"
SEPARATOR_END = "// END AUTOGENERATED CONFIG FEATURES\n"
HELP = "    // Generated by gen_config.py. DO NOT EDIT.\n"
with open("build.rs", "r") as f:
    data = f.read()
before, data = data.split(SEPARATOR_START, maxsplit=1)
_, after = data.split(SEPARATOR_END, maxsplit=1)
data = before + SEPARATOR_START + HELP + \
    things + "    " + SEPARATOR_END + after
with open("build.rs", "w") as f:
    f.write(data)
