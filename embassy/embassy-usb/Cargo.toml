[package]
name = "embassy-usb"
version = "0.5.1"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Async USB device stack for embedded devices in Rust."
keywords = ["embedded", "async", "usb", "hal", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-usb"

[package.metadata.embassy]
build = [
    {target = "thumbv6m-none-eabi", features = []},
    {target = "thumbv6m-none-eabi", features = ["log"]},
    {target = "thumbv6m-none-eabi", features = ["defmt"]},
    {target = "thumbv6m-none-eabi", features = ["usbd-hid"]},
    {target = "thumbv6m-none-eabi", features = ["max-interface-count-1"]},
    {target = "thumbv6m-none-eabi", features = ["max-interface-count-8"]},
    {target = "thumbv6m-none-eabi", features = ["max-handler-count-8"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-usb-v$VERSION/embassy-usb/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-usb/src/"
features = ["defmt", "usbd-hid"]
target = "thumbv7em-none-eabi"

[package.metadata.docs.rs]
features = ["defmt", "usbd-hid"]

[features]
defmt = ["dep:defmt", "embassy-usb-driver/defmt"]
usbd-hid = ["dep:usbd-hid", "dep:ssmarshal"]
default = ["usbd-hid"]

# BEGIN AUTOGENERATED CONFIG FEATURES
# Generated by gen_config.py. DO NOT EDIT.
max-interface-count-1 = []
max-interface-count-2 = []
max-interface-count-3 = []
max-interface-count-4 = [] # Default
max-interface-count-5 = []
max-interface-count-6 = []
max-interface-count-7 = []
max-interface-count-8 = []

max-handler-count-1 = []
max-handler-count-2 = []
max-handler-count-3 = []
max-handler-count-4 = [] # Default
max-handler-count-5 = []
max-handler-count-6 = []
max-handler-count-7 = []
max-handler-count-8 = []

# END AUTOGENERATED CONFIG FEATURES

[dependencies]
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
embassy-usb-driver = { version = "0.2.0", path = "../embassy-usb-driver" }
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-net-driver-channel = { version = "0.3.2", path = "../embassy-net-driver-channel" }

defmt = { version = "1", optional = true }
log = { version = "0.4.14", optional = true }
heapless = "0.8"
embedded-io-async = "0.6.1"

# for HID
usbd-hid = { version = "0.8.1", optional = true }
ssmarshal = { version = "1.0", default-features = false, optional = true }
