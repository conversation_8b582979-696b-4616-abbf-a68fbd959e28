[package]
name = "embassy-usb-logger"
version = "0.5.1"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "`log` implementation for USB serial using `embassy-usb`."
keywords = ["embedded", "log", "usb", "hal", "serial"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-usb-logger"

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-usb-logger-v$VERSION/embassy-usb/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-usb-logger/src/"
target = "thumbv7em-none-eabi"

[dependencies]
embassy-usb = { version = "0.5.1", path = "../embassy-usb" }
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
log = "0.4"
