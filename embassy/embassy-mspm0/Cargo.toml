[package]
name = "embassy-mspm0"
version = "0.1.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Embassy Hardware Abstraction Layer (HAL) for Texas Instruments MSPM0 series microcontrollers"
keywords = ["embedded", "async", "mspm0", "hal", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-mspm0"

# TODO: Remove to publish initial version
publish = false

[package.metadata.embassy]
build = [
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0c1104dgs20", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0g3507pm", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0g3519pz", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0l1306rhb", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0l2228pn", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0l1345dgs28", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0l1106dgs28", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0l1228pm", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0g1107ycj", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0g3105rhb", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0g1505pt", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "mspm0g1519rhb", "time-driver-any"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-mspm0-v$VERSION/embassy-mspm0/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-mspm0/src/"

features = ["defmt", "unstable-pac", "time-driver-any"]
flavors = [
    { regex_feature = "mspm0c.*", target = "thumbv6m-none-eabi" },
    { regex_feature = "mspm0l.*", target = "thumbv6m-none-eabi" },
    { regex_feature = "mspm0g.*", target = "thumbv6m-none-eabi" },
]

[package.metadata.docs.rs]
features = ["defmt", "unstable-pac", "time-driver-any", "time", "mspm0g3507"]
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-time = { version = "0.5.0", path = "../embassy-time", optional = true }
# TODO: Support other tick rates
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver", optional = true, features = ["tick-hz-32_768"] }
embassy-time-queue-utils = { version = "0.3.0", path = "../embassy-time-queue-utils", optional = true }
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
embassy-hal-internal = { version = "0.3.0", path = "../embassy-hal-internal", features = ["cortex-m", "prio-bits-2"] }
embassy-embedded-hal = { version = "0.5.0", path = "../embassy-embedded-hal", default-features = false }
embassy-executor = { version = "0.9.0", path = "../embassy-executor", optional = true }

embedded-hal-02 = { package = "embedded-hal", version = "0.2.6", features = ["unproven"] }
embedded-hal = { version = "1.0" }
embedded-hal-nb = { version = "1.0" }
embedded-hal-async = { version = "1.0" }
embedded-io = "0.6.1"
embedded-io-async = "0.6.1"

defmt = { version = "1.0.1", optional = true }
fixed = "1.29"
log = { version = "0.4.14", optional = true }
cortex-m-rt = ">=0.6.15,<0.8"
cortex-m = "0.7.6"
critical-section = "1.2.0"

# mspm0-metapac = { version = "" }
mspm0-metapac = { git = "https://github.com/mspm0-rs/mspm0-data-generated/", tag = "mspm0-data-d7bf3d01ac0780e716a45b0474234d39443dc5cf" }

[build-dependencies]
proc-macro2 = "1.0.94"
quote = "1.0.40"
cfg_aliases = "0.2.1"

# mspm0-metapac = { version = "", default-features = false, features = ["metadata"] }
mspm0-metapac = { git = "https://github.com/mspm0-rs/mspm0-data-generated/", tag = "mspm0-data-d7bf3d01ac0780e716a45b0474234d39443dc5cf", default-features = false, features = ["metadata"] }

[features]
default = ["rt"]

## Enable `mspm0-metapac`'s `rt` feature
rt = ["mspm0-metapac/rt"]

## Use [`defmt`](https://docs.rs/defmt/latest/defmt/) for logging
defmt = [
    "dep:defmt",
    "embassy-sync/defmt",
    "embassy-embedded-hal/defmt",
    "embassy-hal-internal/defmt",
    "embassy-time?/defmt",
]

## Re-export mspm0-metapac at `mspm0::pac`.
## This is unstable because semver-minor (non-breaking) releases of embassy-mspm0 may major-bump (breaking) the mspm0-metapac version.
## If this is an issue for you, you're encouraged to directly depend on a fixed version of the PAC.
## There are no plans to make this stable.
unstable-pac = []

#! ## Time

# Features starting with `_` are for internal use only. They're not intended
# to be enabled by other crates, and are not covered by semver guarantees.
_time-driver = ["dep:embassy-time-driver", "dep:embassy-time-queue-utils"]

# Use any time driver
time-driver-any = ["_time-driver"]
## Use TIMG0 as time driver
time-driver-timg0 = ["_time-driver"]
## Use TIMG1 as time driver
time-driver-timg1 = ["_time-driver"]
## Use TIMG2 as time driver
time-driver-timg2 = ["_time-driver"]
## Use TIMG3 as time driver
time-driver-timg3 = ["_time-driver"]
## Use TIMG4 as time driver
time-driver-timg4 = ["_time-driver"]
## Use TIMG5 as time driver
time-driver-timg5 = ["_time-driver"]
## Use TIMG6 as time driver
time-driver-timg6 = ["_time-driver"]
## Use TIMG7 as time driver
time-driver-timg7 = ["_time-driver"]
## Use TIMG8 as time driver
time-driver-timg8 = ["_time-driver"]
## Use TIMG9 as time driver
time-driver-timg9 = ["_time-driver"]
## Use TIMG10 as time driver
time-driver-timg10 = ["_time-driver"]
## Use TIMG11 as time driver
time-driver-timg11 = ["_time-driver"]
## Use TIMG12 as time driver
time-driver-timg12 = ["_time-driver"]
## Use TIMG13 as time driver
time-driver-timg13 = ["_time-driver"]
## Use TIMG14 as time driver
time-driver-timg14 = ["_time-driver"]
## Use TIMA0 as time driver
time-driver-tima0 = ["_time-driver"]
## Use TIMA1 as time driver
time-driver-tima1 = ["_time-driver"]

#! ## Chip-selection features
#! Select your chip by specifying the model as a feature, e.g. `mspm0g3507pm`.
#! Check the `Cargo.toml` for the latest list of supported chips.
#!
#! **Important:** Do not forget to adapt the target chip in your toolchain,
#! e.g. in `.cargo/config.toml`.

mspm0c1103dgs20 = ["mspm0-metapac/mspm0c1103dgs20"]
mspm0c1103dsg = ["mspm0-metapac/mspm0c1103dsg"]
mspm0c1103dyy = ["mspm0-metapac/mspm0c1103dyy"]
mspm0c1103ruk = ["mspm0-metapac/mspm0c1103ruk"]
mspm0c1104dgs20 = ["mspm0-metapac/mspm0c1104dgs20"]
mspm0c1104dsg = ["mspm0-metapac/mspm0c1104dsg"]
mspm0c1104dyy = ["mspm0-metapac/mspm0c1104dyy"]
mspm0c1104ruk = ["mspm0-metapac/mspm0c1104ruk"]
mspm0c1104ycj = ["mspm0-metapac/mspm0c1104ycj"]
mspm0c1105pt = ["mspm0-metapac/mspm0c1105pt"]
mspm0c1105rgz = ["mspm0-metapac/mspm0c1105rgz"]
mspm0c1105rhb = ["mspm0-metapac/mspm0c1105rhb"]
mspm0c1105dgs32 = ["mspm0-metapac/mspm0c1105dgs32"]
mspm0c1105dgs28 = ["mspm0-metapac/mspm0c1105dgs28"]
mspm0c1105rge = ["mspm0-metapac/mspm0c1105rge"]
mspm0c1105dgs20 = ["mspm0-metapac/mspm0c1105dgs20"]
mspm0c1105ruk = ["mspm0-metapac/mspm0c1105ruk"]
mspm0c1105zcm = ["mspm0-metapac/mspm0c1105zcm"]
mspm0c1106pt = ["mspm0-metapac/mspm0c1106pt"]
mspm0c1106rgz = ["mspm0-metapac/mspm0c1106rgz"]
mspm0c1106rhb = ["mspm0-metapac/mspm0c1106rhb"]
mspm0c1106dgs32 = ["mspm0-metapac/mspm0c1106dgs32"]
mspm0c1106dgs28 = ["mspm0-metapac/mspm0c1106dgs28"]
mspm0c1106rge = ["mspm0-metapac/mspm0c1106rge"]
mspm0c1106dgs20 = ["mspm0-metapac/mspm0c1106dgs20"]
mspm0c1106ruk = ["mspm0-metapac/mspm0c1106ruk"]
mspm0c1106zcm = ["mspm0-metapac/mspm0c1106zcm"]
mspm0g1105dgs28 = ["mspm0-metapac/mspm0g1105dgs28"]
mspm0g1105pm = ["mspm0-metapac/mspm0g1105pm"]
mspm0g1105pt = ["mspm0-metapac/mspm0g1105pt"]
mspm0g1105rge = ["mspm0-metapac/mspm0g1105rge"]
mspm0g1105rgz = ["mspm0-metapac/mspm0g1105rgz"]
mspm0g1105rhb = ["mspm0-metapac/mspm0g1105rhb"]
mspm0g1106dgs28 = ["mspm0-metapac/mspm0g1106dgs28"]
mspm0g1106pm = ["mspm0-metapac/mspm0g1106pm"]
mspm0g1106pt = ["mspm0-metapac/mspm0g1106pt"]
mspm0g1106rge = ["mspm0-metapac/mspm0g1106rge"]
mspm0g1106rgz = ["mspm0-metapac/mspm0g1106rgz"]
mspm0g1106rhb = ["mspm0-metapac/mspm0g1106rhb"]
mspm0g1107dgs28 = ["mspm0-metapac/mspm0g1107dgs28"]
mspm0g1107pm = ["mspm0-metapac/mspm0g1107pm"]
mspm0g1107pt = ["mspm0-metapac/mspm0g1107pt"]
mspm0g1107rge = ["mspm0-metapac/mspm0g1107rge"]
mspm0g1107rgz = ["mspm0-metapac/mspm0g1107rgz"]
mspm0g1107rhb = ["mspm0-metapac/mspm0g1107rhb"]
mspm0g1107ycj = ["mspm0-metapac/mspm0g1107ycj"]
mspm0g1505pm = ["mspm0-metapac/mspm0g1505pm"]
mspm0g1505pt = ["mspm0-metapac/mspm0g1505pt"]
mspm0g1505rge = ["mspm0-metapac/mspm0g1505rge"]
mspm0g1505rgz = ["mspm0-metapac/mspm0g1505rgz"]
mspm0g1505rhb = ["mspm0-metapac/mspm0g1505rhb"]
mspm0g1506pm = ["mspm0-metapac/mspm0g1506pm"]
mspm0g1506pt = ["mspm0-metapac/mspm0g1506pt"]
mspm0g1506rge = ["mspm0-metapac/mspm0g1506rge"]
mspm0g1506rgz = ["mspm0-metapac/mspm0g1506rgz"]
mspm0g1506rhb = ["mspm0-metapac/mspm0g1506rhb"]
mspm0g1507pm = ["mspm0-metapac/mspm0g1507pm"]
mspm0g1507pt = ["mspm0-metapac/mspm0g1507pt"]
mspm0g1507rge = ["mspm0-metapac/mspm0g1507rge"]
mspm0g1507rgz = ["mspm0-metapac/mspm0g1507rgz"]
mspm0g1507rhb = ["mspm0-metapac/mspm0g1507rhb"]
mspm0g1507ycj = ["mspm0-metapac/mspm0g1507ycj"]
mspm0g1519rgz = ["mspm0-metapac/mspm0g1519rgz"]
mspm0g1519rhb = ["mspm0-metapac/mspm0g1519rhb"]
mspm0g3105dgs20 = ["mspm0-metapac/mspm0g3105dgs20"]
mspm0g3105dgs28 = ["mspm0-metapac/mspm0g3105dgs28"]
mspm0g3105rhb = ["mspm0-metapac/mspm0g3105rhb"]
mspm0g3106dgs20 = ["mspm0-metapac/mspm0g3106dgs20"]
mspm0g3106dgs28 = ["mspm0-metapac/mspm0g3106dgs28"]
mspm0g3106rhb = ["mspm0-metapac/mspm0g3106rhb"]
mspm0g3107dgs20 = ["mspm0-metapac/mspm0g3107dgs20"]
mspm0g3107dgs28 = ["mspm0-metapac/mspm0g3107dgs28"]
mspm0g3107rhb = ["mspm0-metapac/mspm0g3107rhb"]
mspm0g3505dgs28 = ["mspm0-metapac/mspm0g3505dgs28"]
mspm0g3505pm = ["mspm0-metapac/mspm0g3505pm"]
mspm0g3505pt = ["mspm0-metapac/mspm0g3505pt"]
mspm0g3505rgz = ["mspm0-metapac/mspm0g3505rgz"]
mspm0g3505rhb = ["mspm0-metapac/mspm0g3505rhb"]
mspm0g3506dgs28 = ["mspm0-metapac/mspm0g3506dgs28"]
mspm0g3506pm = ["mspm0-metapac/mspm0g3506pm"]
mspm0g3506pt = ["mspm0-metapac/mspm0g3506pt"]
mspm0g3506rgz = ["mspm0-metapac/mspm0g3506rgz"]
mspm0g3506rhb = ["mspm0-metapac/mspm0g3506rhb"]
mspm0g3507dgs28 = ["mspm0-metapac/mspm0g3507dgs28"]
mspm0g3507pm = ["mspm0-metapac/mspm0g3507pm"]
mspm0g3507pt = ["mspm0-metapac/mspm0g3507pt"]
mspm0g3507rgz = ["mspm0-metapac/mspm0g3507rgz"]
mspm0g3507rhb = ["mspm0-metapac/mspm0g3507rhb"]
mspm0g3519pm = ["mspm0-metapac/mspm0g3519pm"]
mspm0g3519pn = ["mspm0-metapac/mspm0g3519pn"]
mspm0g3519pz = ["mspm0-metapac/mspm0g3519pz"]
mspm0g3519rgz = ["mspm0-metapac/mspm0g3519rgz"]
mspm0g3519rhb = ["mspm0-metapac/mspm0g3519rhb"]
mspm0l1105dgs20 = ["mspm0-metapac/mspm0l1105dgs20"]
mspm0l1105dgs28 = ["mspm0-metapac/mspm0l1105dgs28"]
mspm0l1105dyy = ["mspm0-metapac/mspm0l1105dyy"]
mspm0l1105rge = ["mspm0-metapac/mspm0l1105rge"]
mspm0l1105rtr = ["mspm0-metapac/mspm0l1105rtr"]
mspm0l1106dgs20 = ["mspm0-metapac/mspm0l1106dgs20"]
mspm0l1106dgs28 = ["mspm0-metapac/mspm0l1106dgs28"]
mspm0l1106dyy = ["mspm0-metapac/mspm0l1106dyy"]
mspm0l1106rge = ["mspm0-metapac/mspm0l1106rge"]
mspm0l1106rhb = ["mspm0-metapac/mspm0l1106rhb"]
mspm0l1106rtr = ["mspm0-metapac/mspm0l1106rtr"]
mspm0l1227pm = ["mspm0-metapac/mspm0l1227pm"]
mspm0l1227pn = ["mspm0-metapac/mspm0l1227pn"]
mspm0l1227pt = ["mspm0-metapac/mspm0l1227pt"]
mspm0l1227rge = ["mspm0-metapac/mspm0l1227rge"]
mspm0l1227rgz = ["mspm0-metapac/mspm0l1227rgz"]
mspm0l1227rhb = ["mspm0-metapac/mspm0l1227rhb"]
mspm0l1228pm = ["mspm0-metapac/mspm0l1228pm"]
mspm0l1228pn = ["mspm0-metapac/mspm0l1228pn"]
mspm0l1228pt = ["mspm0-metapac/mspm0l1228pt"]
mspm0l1228rge = ["mspm0-metapac/mspm0l1228rge"]
mspm0l1228rgz = ["mspm0-metapac/mspm0l1228rgz"]
mspm0l1228rhb = ["mspm0-metapac/mspm0l1228rhb"]
mspm0l1303rge = ["mspm0-metapac/mspm0l1303rge"]
mspm0l1304dgs20 = ["mspm0-metapac/mspm0l1304dgs20"]
mspm0l1304dgs28 = ["mspm0-metapac/mspm0l1304dgs28"]
mspm0l1304dyy = ["mspm0-metapac/mspm0l1304dyy"]
mspm0l1304rge = ["mspm0-metapac/mspm0l1304rge"]
mspm0l1304rhb = ["mspm0-metapac/mspm0l1304rhb"]
mspm0l1304rtr = ["mspm0-metapac/mspm0l1304rtr"]
mspm0l1305dgs20 = ["mspm0-metapac/mspm0l1305dgs20"]
mspm0l1305dgs28 = ["mspm0-metapac/mspm0l1305dgs28"]
mspm0l1305dyy = ["mspm0-metapac/mspm0l1305dyy"]
mspm0l1305rge = ["mspm0-metapac/mspm0l1305rge"]
mspm0l1305rtr = ["mspm0-metapac/mspm0l1305rtr"]
mspm0l1306dgs20 = ["mspm0-metapac/mspm0l1306dgs20"]
mspm0l1306dgs28 = ["mspm0-metapac/mspm0l1306dgs28"]
mspm0l1306dyy = ["mspm0-metapac/mspm0l1306dyy"]
mspm0l1306rge = ["mspm0-metapac/mspm0l1306rge"]
mspm0l1306rhb = ["mspm0-metapac/mspm0l1306rhb"]
mspm0l1343dgs20 = ["mspm0-metapac/mspm0l1343dgs20"]
mspm0l1344dgs20 = ["mspm0-metapac/mspm0l1344dgs20"]
mspm0l1345dgs28 = ["mspm0-metapac/mspm0l1345dgs28"]
mspm0l1346dgs28 = ["mspm0-metapac/mspm0l1346dgs28"]
mspm0l2227pm = ["mspm0-metapac/mspm0l2227pm"]
mspm0l2227pn = ["mspm0-metapac/mspm0l2227pn"]
mspm0l2227pt = ["mspm0-metapac/mspm0l2227pt"]
mspm0l2227rgz = ["mspm0-metapac/mspm0l2227rgz"]
mspm0l2228pm = ["mspm0-metapac/mspm0l2228pm"]
mspm0l2228pn = ["mspm0-metapac/mspm0l2228pn"]
mspm0l2228pt = ["mspm0-metapac/mspm0l2228pt"]
mspm0l2228rgz = ["mspm0-metapac/mspm0l2228rgz"]
msps003f3pw20 = ["mspm0-metapac/msps003f3pw20"]
msps003f4pw20 = ["mspm0-metapac/msps003f4pw20"]
