{
  "[toml]": {
    "editor.formatOnSave": false
  },
  "[markdown]": {
    "editor.formatOnSave": false
  },
  "rust-analyzer.check.allTargets": false,
  "rust-analyzer.check.noDefaultFeatures": true,
  "rust-analyzer.cargo.noDefaultFeatures": true,
  "rust-analyzer.showUnlinkedFileNotification": false,
  // Uncomment the target of your chip.
  //"rust-analyzer.cargo.target": "thumbv6m-none-eabi",
  //"rust-analyzer.cargo.target": "thumbv7m-none-eabi",
  "rust-analyzer.cargo.target": "thumbv7em-none-eabi",
  //"rust-analyzer.cargo.target": "thumbv7em-none-eabihf",
  //"rust-analyzer.cargo.target": "thumbv8m.main-none-eabihf",
  "rust-analyzer.cargo.features": [
    // Comment out these features when working on the examples. Most example crates do not have any cargo features.
    "stm32f107rb",
    "time-driver-any",
    "unstable-pac",
    "exti",
    "rt",
  ],
  "rust-analyzer.linkedProjects": [
    "embassy-stm32/Cargo.toml",
    // To work on the examples, comment the line above and all of the cargo.features lines,
    // then uncomment ONE line below to select the chip you want to work on.
    // This makes rust-analyzer work on the example crate and all its dependencies.
    // "examples/mspm0c1104/Cargo.toml",
    // "examples/mspm0g3507/Cargo.toml",
    // "examples/mspm0g3519/Cargo.toml",
    // "examples/mspm0l1306/Cargo.toml",
    // "examples/mspm0l2228/Cargo.toml",
    // "examples/nrf52840-rtic/Cargo.toml",
    // "examples/nrf5340/Cargo.toml",
    // "examples/nrf-rtos-trace/Cargo.toml",
    // "examples/mimxrt1011/Cargo.toml",
    // "examples/mimxrt1062-evk/Cargo.toml",
    // "examples/rp/Cargo.toml",
    // "examples/std/Cargo.toml",
    // "examples/stm32c0/Cargo.toml",
    // "examples/stm32f0/Cargo.toml",
    // "examples/stm32f1/Cargo.toml",
    // "examples/stm32f2/Cargo.toml",
    // "examples/stm32f3/Cargo.toml",
    // "examples/stm32f334/Cargo.toml",
    // "examples/stm32f4/Cargo.toml",
    // "examples/stm32f7/Cargo.toml",
    // "examples/stm32g0/Cargo.toml",
    // "examples/stm32g4/Cargo.toml",
    // "examples/stm32h5/Cargo.toml",
    // "examples/stm32h7/Cargo.toml",
    // "examples/stm32l0/Cargo.toml",
    // "examples/stm32l1/Cargo.toml",
    // "examples/stm32l4/Cargo.toml",
    // "examples/stm32l5/Cargo.toml",
    // "examples/stm32u5/Cargo.toml",
    // "examples/stm32wb/Cargo.toml",
    // "examples/stm32wl/Cargo.toml",
    // "examples/wasm/Cargo.toml",
  ],
}
