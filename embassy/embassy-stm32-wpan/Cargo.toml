[package]
name = "embassy-stm32-wpan"
version = "0.1.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Async STM32 WPAN stack for embedded devices in Rust."
keywords = ["embedded", "async", "stm32", "ble", "wpan"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-stm32-wpan"

publish = false

[package.metadata.embassy]
build = [
    {target = "thumbv7em-none-eabi", features = ["stm32wb55rg"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-stm32-wpan-v$VERSION/embassy-stm32-wpan/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-stm32-wpan/src/"
target = "thumbv7em-none-eabihf"
features = ["stm32wb55rg", "ble", "mac"]

[package.metadata.docs.rs]
features = ["stm32wb55rg", "ble", "mac"]

[dependencies]
embassy-stm32 = { version = "0.4.0", path = "../embassy-stm32" }
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-time = { version = "0.5.0", path = "../embassy-time", optional = true }
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
embassy-hal-internal = { version = "0.3.0", path = "../embassy-hal-internal" }
embassy-embedded-hal = { version = "0.5.0", path = "../embassy-embedded-hal" }
embassy-net-driver = { version = "0.2.0", path = "../embassy-net-driver", optional=true }

defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.17", optional = true }

cortex-m = "0.7.6"
heapless = "0.8"
aligned = "0.4.1"

bit_field = "0.10.2"
stm32-device-signature = { version = "0.3.3", features = ["stm32wb5x"] }
stm32wb-hci = { version = "0.17.0", optional = true }
futures-util = { version = "0.3.30", default-features = false }
bitflags = { version = "2.3.3", optional = true }

[features]
defmt = ["dep:defmt", "embassy-sync/defmt", "embassy-embedded-hal/defmt", "embassy-hal-internal/defmt", "stm32wb-hci?/defmt"]

ble = ["dep:stm32wb-hci"]
mac = ["dep:bitflags", "dep:embassy-net-driver" ]

extended = []

stm32wb10cc = [ "embassy-stm32/stm32wb10cc" ]
stm32wb15cc = [ "embassy-stm32/stm32wb15cc" ]
stm32wb30ce = [ "embassy-stm32/stm32wb30ce" ]
stm32wb35cc = [ "embassy-stm32/stm32wb35cc" ]
stm32wb35ce = [ "embassy-stm32/stm32wb35ce" ]
stm32wb50cg = [ "embassy-stm32/stm32wb50cg" ]
stm32wb55cc = [ "embassy-stm32/stm32wb55cc" ]
stm32wb55ce = [ "embassy-stm32/stm32wb55ce" ]
stm32wb55cg = [ "embassy-stm32/stm32wb55cg" ]
stm32wb55rc = [ "embassy-stm32/stm32wb55rc" ]
stm32wb55re = [ "embassy-stm32/stm32wb55re" ]
stm32wb55rg = [ "embassy-stm32/stm32wb55rg" ]
stm32wb55vc = [ "embassy-stm32/stm32wb55vc" ]
stm32wb55ve = [ "embassy-stm32/stm32wb55ve" ]
stm32wb55vg = [ "embassy-stm32/stm32wb55vg" ]
stm32wb55vy = [ "embassy-stm32/stm32wb55vy" ]
