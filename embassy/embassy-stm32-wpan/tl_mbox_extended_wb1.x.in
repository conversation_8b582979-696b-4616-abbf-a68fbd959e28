MEMORY 
{
    RAM_SHARED (xrw)           : ORIGIN = 0x20030000, LENGTH = 4K
    RAMB_SHARED (xrw)          : ORIGIN = 0x20030028, LENGTH = 4K
}

/*
 * Scatter the mailbox interface memory sections in shared memory
 */
SECTIONS
{
    TL_REF_TABLE                     (NOLOAD) : { *(TL_REF_TABLE) } >RAM_SHARED

    MB_MEM1 (NOLOAD)                          : { *(MB_MEM1) } >RAMB_SHARED
    MB_MEM2 (NOLOAD)                          : { _sMB_MEM2 = . ; *(MB_MEM2) ; _eMB_MEM2 = . ; } >RAMB_SHARED
}
