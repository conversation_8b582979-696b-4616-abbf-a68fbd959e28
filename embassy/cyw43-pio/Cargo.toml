[package]
name = "cyw43-pio"
version = "0.8.0"
edition = "2021"
description = "RP2040 PIO SPI implementation for cyw43"
keywords = ["embedded", "cyw43", "embassy-net", "embedded-hal-async", "wifi"]
categories = ["embedded", "hardware-support", "no-std", "network-programming", "asynchronous"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/cyw43-pio"

[dependencies]
cyw43 = { version = "0.5.0", path = "../cyw43" }
embassy-rp = { version = "0.8.0", path = "../embassy-rp" }
fixed = "1.23.1"
defmt = { version = "1.0.1", optional = true }

[package.metadata.embassy]
build = [
    {target = "thumbv6m-none-eabi", features = ["embassy-rp/rp2040"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/cyw43-pio-v$VERSION/cyw43-pio/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/cyw43-pio/src/"
target = "thumbv6m-none-eabi"
features = ["embassy-rp/rp2040"]
