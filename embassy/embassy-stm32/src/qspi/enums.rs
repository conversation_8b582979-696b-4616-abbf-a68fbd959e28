//! Enums used in QSPI configuration.

#[allow(dead_code)]
#[derive(Co<PERSON>, <PERSON>lone)]
pub(crate) enum QspiMode {
    IndirectWrite,
    IndirectRead,
    AutoPolling,
    MemoryMapped,
}

impl From<QspiMode> for u8 {
    fn from(val: QspiMode) -> Self {
        match val {
            QspiMode::IndirectWrite => 0b00,
            QspiMode::IndirectRead => 0b01,
            QspiMode::AutoPolling => 0b10,
            QspiMode::MemoryMapped => 0b11,
        }
    }
}

/// QSPI lane width
#[allow(dead_code)]
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum QspiWidth {
    /// None
    NONE,
    /// Single lane
    SING,
    /// Dual lanes
    DUAL,
    /// Quad lanes
    QUAD,
}

impl From<QspiWidth> for u8 {
    fn from(val: QspiWidth) -> Self {
        match val {
            QspiWidth::NONE => 0b00,
            QspiWidth::SING => 0b01,
            QspiWidth::DUAL => 0b10,
            QspiWidth::QUAD => 0b11,
        }
    }
}

/// Flash bank selection
#[allow(dead_code)]
#[derive(Copy, Clone)]
pub enum FlashSelection {
    /// Bank 1
    Flash1,
    /// Bank 2
    Flash2,
}

impl From<FlashSelection> for bool {
    fn from(val: FlashSelection) -> Self {
        match val {
            FlashSelection::Flash1 => false,
            FlashSelection::Flash2 => true,
        }
    }
}

/// QSPI memory size.
#[allow(missing_docs)]
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum MemorySize {
    _1KiB,
    _2KiB,
    _4KiB,
    _8KiB,
    _16KiB,
    _32KiB,
    _64KiB,
    _128KiB,
    _256KiB,
    _512KiB,
    _1MiB,
    _2MiB,
    _4MiB,
    _8MiB,
    _16MiB,
    _32MiB,
    _64MiB,
    _128MiB,
    _256MiB,
    _512MiB,
    _1GiB,
    _2GiB,
    _4GiB,
    Other(u8),
}

impl From<MemorySize> for u8 {
    fn from(val: MemorySize) -> Self {
        match val {
            MemorySize::_1KiB => 9,
            MemorySize::_2KiB => 10,
            MemorySize::_4KiB => 11,
            MemorySize::_8KiB => 12,
            MemorySize::_16KiB => 13,
            MemorySize::_32KiB => 14,
            MemorySize::_64KiB => 15,
            MemorySize::_128KiB => 16,
            MemorySize::_256KiB => 17,
            MemorySize::_512KiB => 18,
            MemorySize::_1MiB => 19,
            MemorySize::_2MiB => 20,
            MemorySize::_4MiB => 21,
            MemorySize::_8MiB => 22,
            MemorySize::_16MiB => 23,
            MemorySize::_32MiB => 24,
            MemorySize::_64MiB => 25,
            MemorySize::_128MiB => 26,
            MemorySize::_256MiB => 27,
            MemorySize::_512MiB => 28,
            MemorySize::_1GiB => 29,
            MemorySize::_2GiB => 30,
            MemorySize::_4GiB => 31,
            MemorySize::Other(val) => val,
        }
    }
}

/// QSPI Address size
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum AddressSize {
    /// 8-bit address
    _8Bit,
    /// 16-bit address
    _16Bit,
    /// 24-bit address
    _24bit,
    /// 32-bit address
    _32bit,
}

impl From<AddressSize> for u8 {
    fn from(val: AddressSize) -> Self {
        match val {
            AddressSize::_8Bit => 0b00,
            AddressSize::_16Bit => 0b01,
            AddressSize::_24bit => 0b10,
            AddressSize::_32bit => 0b11,
        }
    }
}

/// Time the Chip Select line stays high.
#[allow(missing_docs)]
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum ChipSelectHighTime {
    _1Cycle,
    _2Cycle,
    _3Cycle,
    _4Cycle,
    _5Cycle,
    _6Cycle,
    _7Cycle,
    _8Cycle,
}

impl From<ChipSelectHighTime> for u8 {
    fn from(val: ChipSelectHighTime) -> Self {
        match val {
            ChipSelectHighTime::_1Cycle => 0,
            ChipSelectHighTime::_2Cycle => 1,
            ChipSelectHighTime::_3Cycle => 2,
            ChipSelectHighTime::_4Cycle => 3,
            ChipSelectHighTime::_5Cycle => 4,
            ChipSelectHighTime::_6Cycle => 5,
            ChipSelectHighTime::_7Cycle => 6,
            ChipSelectHighTime::_8Cycle => 7,
        }
    }
}

/// FIFO threshold.
#[allow(missing_docs)]
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum FIFOThresholdLevel {
    _1Bytes,
    _2Bytes,
    _3Bytes,
    _4Bytes,
    _5Bytes,
    _6Bytes,
    _7Bytes,
    _8Bytes,
    _9Bytes,
    _10Bytes,
    _11Bytes,
    _12Bytes,
    _13Bytes,
    _14Bytes,
    _15Bytes,
    _16Bytes,
    _17Bytes,
    _18Bytes,
    _19Bytes,
    _20Bytes,
    _21Bytes,
    _22Bytes,
    _23Bytes,
    _24Bytes,
    _25Bytes,
    _26Bytes,
    _27Bytes,
    _28Bytes,
    _29Bytes,
    _30Bytes,
    _31Bytes,
    _32Bytes,
}

impl From<FIFOThresholdLevel> for u8 {
    fn from(val: FIFOThresholdLevel) -> Self {
        match val {
            FIFOThresholdLevel::_1Bytes => 0,
            FIFOThresholdLevel::_2Bytes => 1,
            FIFOThresholdLevel::_3Bytes => 2,
            FIFOThresholdLevel::_4Bytes => 3,
            FIFOThresholdLevel::_5Bytes => 4,
            FIFOThresholdLevel::_6Bytes => 5,
            FIFOThresholdLevel::_7Bytes => 6,
            FIFOThresholdLevel::_8Bytes => 7,
            FIFOThresholdLevel::_9Bytes => 8,
            FIFOThresholdLevel::_10Bytes => 9,
            FIFOThresholdLevel::_11Bytes => 10,
            FIFOThresholdLevel::_12Bytes => 11,
            FIFOThresholdLevel::_13Bytes => 12,
            FIFOThresholdLevel::_14Bytes => 13,
            FIFOThresholdLevel::_15Bytes => 14,
            FIFOThresholdLevel::_16Bytes => 15,
            FIFOThresholdLevel::_17Bytes => 16,
            FIFOThresholdLevel::_18Bytes => 17,
            FIFOThresholdLevel::_19Bytes => 18,
            FIFOThresholdLevel::_20Bytes => 19,
            FIFOThresholdLevel::_21Bytes => 20,
            FIFOThresholdLevel::_22Bytes => 21,
            FIFOThresholdLevel::_23Bytes => 22,
            FIFOThresholdLevel::_24Bytes => 23,
            FIFOThresholdLevel::_25Bytes => 24,
            FIFOThresholdLevel::_26Bytes => 25,
            FIFOThresholdLevel::_27Bytes => 26,
            FIFOThresholdLevel::_28Bytes => 27,
            FIFOThresholdLevel::_29Bytes => 28,
            FIFOThresholdLevel::_30Bytes => 29,
            FIFOThresholdLevel::_31Bytes => 30,
            FIFOThresholdLevel::_32Bytes => 31,
        }
    }
}

/// Dummy cycle count
#[allow(missing_docs)]
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum DummyCycles {
    _0,
    _1,
    _2,
    _3,
    _4,
    _5,
    _6,
    _7,
    _8,
    _9,
    _10,
    _11,
    _12,
    _13,
    _14,
    _15,
    _16,
    _17,
    _18,
    _19,
    _20,
    _21,
    _22,
    _23,
    _24,
    _25,
    _26,
    _27,
    _28,
    _29,
    _30,
    _31,
}

impl From<DummyCycles> for u8 {
    fn from(val: DummyCycles) -> Self {
        match val {
            DummyCycles::_0 => 0,
            DummyCycles::_1 => 1,
            DummyCycles::_2 => 2,
            DummyCycles::_3 => 3,
            DummyCycles::_4 => 4,
            DummyCycles::_5 => 5,
            DummyCycles::_6 => 6,
            DummyCycles::_7 => 7,
            DummyCycles::_8 => 8,
            DummyCycles::_9 => 9,
            DummyCycles::_10 => 10,
            DummyCycles::_11 => 11,
            DummyCycles::_12 => 12,
            DummyCycles::_13 => 13,
            DummyCycles::_14 => 14,
            DummyCycles::_15 => 15,
            DummyCycles::_16 => 16,
            DummyCycles::_17 => 17,
            DummyCycles::_18 => 18,
            DummyCycles::_19 => 19,
            DummyCycles::_20 => 20,
            DummyCycles::_21 => 21,
            DummyCycles::_22 => 22,
            DummyCycles::_23 => 23,
            DummyCycles::_24 => 24,
            DummyCycles::_25 => 25,
            DummyCycles::_26 => 26,
            DummyCycles::_27 => 27,
            DummyCycles::_28 => 28,
            DummyCycles::_29 => 29,
            DummyCycles::_30 => 30,
            DummyCycles::_31 => 31,
        }
    }
}

#[allow(missing_docs)]
#[derive(Copy, Clone)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum SampleShifting {
    None,
    HalfCycle,
}

impl From<SampleShifting> for bool {
    fn from(value: SampleShifting) -> Self {
        match value {
            SampleShifting::None => false,
            SampleShifting::HalfCycle => true,
        }
    }
}
