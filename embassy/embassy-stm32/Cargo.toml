[package]
name = "embassy-stm32"
version = "0.4.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Embassy Hardware Abstraction Layer (HAL) for ST STM32 series microcontrollers"
keywords = ["embedded", "async", "stm32", "hal", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-stm32"

[package.metadata.embassy]
build = [
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "exti", "stm32l552ze", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "stm32l552ze", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "exti", "stm32l552ze", "time"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "stm32l552ze", "time"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "exti", "stm32l552ze"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "stm32l552ze"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "single-bank", "stm32l552ze"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32c071rb", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32c051f6", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32c091gb", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32c092rc", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f038f6", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f030c6", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f058t8", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f030r8", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f031k6", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f030rc", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f070f6", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f078vb", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f042g4", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32f072c8", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f401ve", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f405zg", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f407zg", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f401ve", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f405zg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f407zg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f410tb", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f411ce", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f412zg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f413vh", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f415zg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f417zg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f423zh", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f427zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["exti", "log", "stm32f429zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["exti", "log", "stm32f437zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f439zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f446ze", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f469zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f479zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f730i8", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h753zi", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h735zg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "split-pc2", "split-pc3", "stm32h755zi-cm7", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h725re", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h7b3ai", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h7b3ai", "time", "time-driver-tim1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h7r3z8", "time", "time-driver-tim1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h7r7a8", "time", "time-driver-tim1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h7s3a8", "time", "time-driver-tim1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32h7s7z8", "time", "time-driver-tim1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32l431cb", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32l476vg", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32l422cb", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32wb15cc", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32l072cz", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32l041f6", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32l051k8", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "low-power", "stm32l073cz", "time", "time-driver-any"]},
    {target = "thumbv7m-none-eabi", features = ["defmt", "exti", "stm32l151cb-a", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f303c8", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f398ve", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32f378cc", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32g0b0ce", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32g0c1ve", "time", "time-driver-any"]},
    {target = "thumbv7m-none-eabi", features = ["defmt", "exti", "stm32f217zg", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "dual-bank", "exti", "low-power", "stm32l552ze", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32wl54jc-cm0p", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32wle5jb", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32g431kb", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "dual-bank", "exti", "stm32g474pe", "time", "time-driver-any"]},
    {target = "thumbv7m-none-eabi", features = ["defmt", "exti", "stm32f107vc", "time", "time-driver-any"]},
    {target = "thumbv7m-none-eabi", features = ["defmt", "exti", "stm32f103re", "time", "time-driver-any"]},
    {target = "thumbv7m-none-eabi", features = ["defmt", "exti", "stm32f100c4", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32h503rb", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32h523cc", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32h562ag", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32wba50ke", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32wba55ug", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32wba62cg", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "low-power", "stm32wba65ri", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32u5f9zj", "time", "time-driver-any"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "exti", "stm32u5g9nj", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "stm32wb35ce", "time", "time-driver-any"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "exti", "low-power", "stm32wb55rg", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32u031r8", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32u073mb", "time", "time-driver-any"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "exti", "stm32u083rc", "time", "time-driver-any"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-stm32-v$VERSION/embassy-stm32/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-stm32/src/"

features = ["defmt", "unstable-pac", "exti", "time-driver-any", "time"]
flavors = [
    { regex_feature = "stm32f0.*", target = "thumbv6m-none-eabi" },
    { regex_feature = "stm32f1.*", target = "thumbv7m-none-eabi" },
    { regex_feature = "stm32f2.*", target = "thumbv7m-none-eabi" },
    { regex_feature = "stm32f3.*", target = "thumbv7em-none-eabi" },
    { regex_feature = "stm32f4[2367]..[ig]", target = "thumbv7em-none-eabi", features = ["low-power", "dual-bank"] },
    { regex_feature = "stm32f4.*", target = "thumbv7em-none-eabi", features = ["low-power", "single-bank"] },
    { regex_feature = "stm32f7[67]..[ig]", target = "thumbv7em-none-eabi", features = ["dual-bank"] },
    { regex_feature = "stm32f7.*", target = "thumbv7em-none-eabi" },
    { regex_feature = "stm32c0.*", target = "thumbv6m-none-eabi" },
    { regex_feature = "stm32g0...c", target = "thumbv6m-none-eabi", features = ["dual-bank"] },
    { regex_feature = "stm32g0.*", target = "thumbv6m-none-eabi" },
    { regex_feature = "stm32g4[78].*", target = "thumbv7em-none-eabi", features = ["low-power", "dual-bank"] },
    { regex_feature = "stm32g4.*", target = "thumbv7em-none-eabi", features = ["low-power"] },
    { regex_feature = "stm32h5.*", target = "thumbv8m.main-none-eabihf", features = ["low-power"] },
    { regex_feature = "stm32h7.*", target = "thumbv7em-none-eabi" },
    { regex_feature = "stm32l0.*", target = "thumbv6m-none-eabi", features = ["low-power"] },
    { regex_feature = "stm32l1.*", target = "thumbv7m-none-eabi" },
    { regex_feature = "stm32l4[pqrs].*", target = "thumbv7em-none-eabi", features = ["dual-bank"] },
    { regex_feature = "stm32l4.*", target = "thumbv7em-none-eabi" },
    { regex_feature = "stm32l5...e", target = "thumbv8m.main-none-eabihf", features = ["low-power", "dual-bank"] },
    { regex_feature = "stm32l5.*", target = "thumbv8m.main-none-eabihf", features = ["low-power"] },
    { regex_feature = "stm32u0.*", target = "thumbv6m-none-eabi" },
    { regex_feature = "stm32u5.*", target = "thumbv8m.main-none-eabihf" },
    { regex_feature = "stm32wb.*", target = "thumbv7em-none-eabi" },
    { regex_feature = "stm32wba.*", target = "thumbv8m.main-none-eabihf" },
    { regex_feature = "stm32wl.*", target = "thumbv7em-none-eabi" },
]

[package.metadata.docs.rs]
features = ["defmt", "unstable-pac", "exti", "time-driver-any", "time", "stm32h755zi-cm7", "dual-bank"]
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-time = { version = "0.5.0", path = "../embassy-time", optional = true }
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver", optional = true }
embassy-time-queue-utils = { version = "0.3.0", path = "../embassy-time-queue-utils", optional = true }
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
embassy-hal-internal = { version = "0.3.0", path = "../embassy-hal-internal", features = ["cortex-m", "prio-bits-4"] }
embassy-embedded-hal = { version = "0.5.0", path = "../embassy-embedded-hal", default-features = false }
embassy-net-driver = { version = "0.2.0", path = "../embassy-net-driver" }
embassy-usb-driver = { version = "0.2.0", path = "../embassy-usb-driver" }
embassy-usb-synopsys-otg = { version = "0.3.1", path = "../embassy-usb-synopsys-otg" }
embassy-executor = { version = "0.9.0", path = "../embassy-executor", optional = true }

embedded-hal-02 = { package = "embedded-hal", version = "0.2.6", features = ["unproven"] }
embedded-hal-1 = { package = "embedded-hal", version = "1.0" }
embedded-hal-async = { version = "1.0" }
embedded-hal-nb = { version = "1.0" }
embedded-can = "0.4"

embedded-storage = "0.3.1"
embedded-storage-async = { version = "0.4.1" }

rand-core-06 = { package = "rand_core", version = "0.6" }
rand-core-09 = { package = "rand_core", version = "0.9" }


defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }
cortex-m-rt = ">=0.6.15,<0.8"
cortex-m = "0.7.6"
futures-util = { version = "0.3.30", default-features = false }
sdio-host = "0.9.0"
critical-section = "1.1"
#stm32-metapac = { version = "18" }
stm32-metapac = { git = "https://github.com/embassy-rs/stm32-data-generated", tag = "stm32-data-3cf72eac610259fd78ef16f1c63be69a144d75f7" }

vcell = "0.1.3"
nb = "1.0.0"
stm32-fmc = "0.3.0"
cfg-if = "1.0.0"
embedded-io = { version = "0.6.0" }
embedded-io-async = { version = "0.6.1" }
chrono = { version = "^0.4", default-features = false, optional = true }
bit_field = "0.10.2"
document-features = "0.2.7"

static_assertions = { version = "1.1" }
volatile-register = { version = "0.2.1" }
bitflags = "2.4.2"

block-device-driver = { version = "0.2" }
aligned = "0.4.1"
heapless = "0.9.1"

[dev-dependencies]
critical-section = { version = "1.1", features = ["std"] }
proptest = "1.5.0"
proptest-state-machine = "0.3.0"

[build-dependencies]
proc-macro2 = "1.0.36"
quote = "1.0.15"

#stm32-metapac = { version = "18", default-features = false, features = ["metadata"]}
stm32-metapac = { git = "https://github.com/embassy-rs/stm32-data-generated", tag = "stm32-data-3cf72eac610259fd78ef16f1c63be69a144d75f7", default-features = false, features = ["metadata"] }

[features]
default = ["rt"]

## Enable `stm32-metapac`'s `rt` feature
rt = ["stm32-metapac/rt"]

## Use [`defmt`](https://docs.rs/defmt/latest/defmt/) for logging
defmt = [
    "dep:defmt",
    "embassy-sync/defmt",
    "embassy-embedded-hal/defmt",
    "embassy-hal-internal/defmt",
    "embedded-io-async/defmt-03",
    "embassy-usb-driver/defmt",
    "embassy-net-driver/defmt",
    "embassy-time?/defmt",
    "embassy-usb-synopsys-otg/defmt",
    "stm32-metapac/defmt"
]

exti = []
low-power = [ "dep:embassy-executor", "embassy-executor?/arch-cortex-m", "time" ]
low-power-debug-with-sleep = []

## Automatically generate `memory.x` file based on the memory map from [`stm32-metapac`](https://docs.rs/stm32-metapac/)
memory-x = []

## Use secure registers when TrustZone is enabled
trustzone-secure = []

## Re-export stm32-metapac at `embassy_stm32::pac`.
## This is unstable because semver-minor (non-breaking) releases of embassy-stm32 may major-bump (breaking) the stm32-metapac version.
## If this is an issue for you, you're encouraged to directly depend on a fixed version of the PAC.
## There are no plans to make this stable.
unstable-pac = []

## Enable this feature to disable the overclocking check.
## DO NOT ENABLE THIS FEATURE UNLESS YOU KNOW WHAT YOU'RE DOING.
unchecked-overclocking = []

#! ## Time

## Enables additional driver features that depend on embassy-time
time = ["dep:embassy-time", "embassy-embedded-hal/time"]

# Features starting with `_` are for internal use only. They're not intended
# to be enabled by other crates, and are not covered by semver guarantees.
_time-driver = ["dep:embassy-time-driver", "time", "dep:embassy-time-queue-utils"]

## Use any time driver
time-driver-any = ["_time-driver"]
## Use TIM1 as time driver
time-driver-tim1 = ["_time-driver"]
## Use TIM2 as time driver
time-driver-tim2 = ["_time-driver"]
## Use TIM3 as time driver
time-driver-tim3 = ["_time-driver"]
## Use TIM4 as time driver
time-driver-tim4 = ["_time-driver"]
## Use TIM5 as time driver
time-driver-tim5 = ["_time-driver"]
## Use TIM8 as time driver
time-driver-tim8 = ["_time-driver"]
## Use TIM9 as time driver
time-driver-tim9 = ["_time-driver"]
## Use TIM12 as time driver
time-driver-tim12 = ["_time-driver"]
## Use TIM15 as time driver
time-driver-tim15 = ["_time-driver"]
## Use TIM20 as time driver
time-driver-tim20 = ["_time-driver"]
## Use TIM21 as time driver
time-driver-tim21 = ["_time-driver"]
## Use TIM22 as time driver
time-driver-tim22 = ["_time-driver"]
## Use TIM23 as time driver
time-driver-tim23 = ["_time-driver"]
## Use TIM24 as time driver
time-driver-tim24 = ["_time-driver"]


#! ## Analog Switch Pins (Pxy_C) on STM32H7 series
#! Get `PXY` and `PXY_C` singletons. Digital impls are on `PXY`, Analog impls are on `PXY_C`
#! If disabled, you get only the `PXY` singleton. It has both digital and analog impls.

## Split PA0
split-pa0 = ["_split-pins-enabled"]
## Split PA1
split-pa1 = ["_split-pins-enabled"]
## Split PC2
split-pc2 = ["_split-pins-enabled"]
## Split PC3
split-pc3 = ["_split-pins-enabled"]

dual-bank = []
single-bank = []

## internal use only
_split-pins-enabled = []

## internal use only
_dual-core = []
_core-cm0p = []
_core-cm4 = []
_core-cm7 = []

#! ## Chip-selection features
#! Select your chip by specifying the model as a feature, e.g. `stm32c011d6`.
#! Check the `Cargo.toml` for the latest list of supported chips.
#!
#! **Important:** Do not forget to adapt the target chip in your toolchain,
#! e.g. in `.cargo/config.toml`.

stm32c011d6 = [ "stm32-metapac/stm32c011d6" ]
stm32c011f4 = [ "stm32-metapac/stm32c011f4" ]
stm32c011f6 = [ "stm32-metapac/stm32c011f6" ]
stm32c011j4 = [ "stm32-metapac/stm32c011j4" ]
stm32c011j6 = [ "stm32-metapac/stm32c011j6" ]
stm32c031c4 = [ "stm32-metapac/stm32c031c4" ]
stm32c031c6 = [ "stm32-metapac/stm32c031c6" ]
stm32c031f4 = [ "stm32-metapac/stm32c031f4" ]
stm32c031f6 = [ "stm32-metapac/stm32c031f6" ]
stm32c031g4 = [ "stm32-metapac/stm32c031g4" ]
stm32c031g6 = [ "stm32-metapac/stm32c031g6" ]
stm32c031k4 = [ "stm32-metapac/stm32c031k4" ]
stm32c031k6 = [ "stm32-metapac/stm32c031k6" ]
stm32c051c6 = [ "stm32-metapac/stm32c051c6" ]
stm32c051c8 = [ "stm32-metapac/stm32c051c8" ]
stm32c051d8 = [ "stm32-metapac/stm32c051d8" ]
stm32c051f6 = [ "stm32-metapac/stm32c051f6" ]
stm32c051f8 = [ "stm32-metapac/stm32c051f8" ]
stm32c051g6 = [ "stm32-metapac/stm32c051g6" ]
stm32c051g8 = [ "stm32-metapac/stm32c051g8" ]
stm32c051k6 = [ "stm32-metapac/stm32c051k6" ]
stm32c051k8 = [ "stm32-metapac/stm32c051k8" ]
stm32c071c8 = [ "stm32-metapac/stm32c071c8" ]
stm32c071cb = [ "stm32-metapac/stm32c071cb" ]
stm32c071f8 = [ "stm32-metapac/stm32c071f8" ]
stm32c071fb = [ "stm32-metapac/stm32c071fb" ]
stm32c071g8 = [ "stm32-metapac/stm32c071g8" ]
stm32c071gb = [ "stm32-metapac/stm32c071gb" ]
stm32c071k8 = [ "stm32-metapac/stm32c071k8" ]
stm32c071kb = [ "stm32-metapac/stm32c071kb" ]
stm32c071r8 = [ "stm32-metapac/stm32c071r8" ]
stm32c071rb = [ "stm32-metapac/stm32c071rb" ]
stm32c091cb = [ "stm32-metapac/stm32c091cb" ]
stm32c091cc = [ "stm32-metapac/stm32c091cc" ]
stm32c091ec = [ "stm32-metapac/stm32c091ec" ]
stm32c091fb = [ "stm32-metapac/stm32c091fb" ]
stm32c091fc = [ "stm32-metapac/stm32c091fc" ]
stm32c091gb = [ "stm32-metapac/stm32c091gb" ]
stm32c091gc = [ "stm32-metapac/stm32c091gc" ]
stm32c091kb = [ "stm32-metapac/stm32c091kb" ]
stm32c091kc = [ "stm32-metapac/stm32c091kc" ]
stm32c091rb = [ "stm32-metapac/stm32c091rb" ]
stm32c091rc = [ "stm32-metapac/stm32c091rc" ]
stm32c092cb = [ "stm32-metapac/stm32c092cb" ]
stm32c092cc = [ "stm32-metapac/stm32c092cc" ]
stm32c092ec = [ "stm32-metapac/stm32c092ec" ]
stm32c092fb = [ "stm32-metapac/stm32c092fb" ]
stm32c092fc = [ "stm32-metapac/stm32c092fc" ]
stm32c092gb = [ "stm32-metapac/stm32c092gb" ]
stm32c092gc = [ "stm32-metapac/stm32c092gc" ]
stm32c092kb = [ "stm32-metapac/stm32c092kb" ]
stm32c092kc = [ "stm32-metapac/stm32c092kc" ]
stm32c092rb = [ "stm32-metapac/stm32c092rb" ]
stm32c092rc = [ "stm32-metapac/stm32c092rc" ]
stm32f030c6 = [ "stm32-metapac/stm32f030c6" ]
stm32f030c8 = [ "stm32-metapac/stm32f030c8" ]
stm32f030cc = [ "stm32-metapac/stm32f030cc" ]
stm32f030f4 = [ "stm32-metapac/stm32f030f4" ]
stm32f030k6 = [ "stm32-metapac/stm32f030k6" ]
stm32f030r8 = [ "stm32-metapac/stm32f030r8" ]
stm32f030rc = [ "stm32-metapac/stm32f030rc" ]
stm32f031c4 = [ "stm32-metapac/stm32f031c4" ]
stm32f031c6 = [ "stm32-metapac/stm32f031c6" ]
stm32f031e6 = [ "stm32-metapac/stm32f031e6" ]
stm32f031f4 = [ "stm32-metapac/stm32f031f4" ]
stm32f031f6 = [ "stm32-metapac/stm32f031f6" ]
stm32f031g4 = [ "stm32-metapac/stm32f031g4" ]
stm32f031g6 = [ "stm32-metapac/stm32f031g6" ]
stm32f031k4 = [ "stm32-metapac/stm32f031k4" ]
stm32f031k6 = [ "stm32-metapac/stm32f031k6" ]
stm32f038c6 = [ "stm32-metapac/stm32f038c6" ]
stm32f038e6 = [ "stm32-metapac/stm32f038e6" ]
stm32f038f6 = [ "stm32-metapac/stm32f038f6" ]
stm32f038g6 = [ "stm32-metapac/stm32f038g6" ]
stm32f038k6 = [ "stm32-metapac/stm32f038k6" ]
stm32f042c4 = [ "stm32-metapac/stm32f042c4" ]
stm32f042c6 = [ "stm32-metapac/stm32f042c6" ]
stm32f042f4 = [ "stm32-metapac/stm32f042f4" ]
stm32f042f6 = [ "stm32-metapac/stm32f042f6" ]
stm32f042g4 = [ "stm32-metapac/stm32f042g4" ]
stm32f042g6 = [ "stm32-metapac/stm32f042g6" ]
stm32f042k4 = [ "stm32-metapac/stm32f042k4" ]
stm32f042k6 = [ "stm32-metapac/stm32f042k6" ]
stm32f042t6 = [ "stm32-metapac/stm32f042t6" ]
stm32f048c6 = [ "stm32-metapac/stm32f048c6" ]
stm32f048g6 = [ "stm32-metapac/stm32f048g6" ]
stm32f048t6 = [ "stm32-metapac/stm32f048t6" ]
stm32f051c4 = [ "stm32-metapac/stm32f051c4" ]
stm32f051c6 = [ "stm32-metapac/stm32f051c6" ]
stm32f051c8 = [ "stm32-metapac/stm32f051c8" ]
stm32f051k4 = [ "stm32-metapac/stm32f051k4" ]
stm32f051k6 = [ "stm32-metapac/stm32f051k6" ]
stm32f051k8 = [ "stm32-metapac/stm32f051k8" ]
stm32f051r4 = [ "stm32-metapac/stm32f051r4" ]
stm32f051r6 = [ "stm32-metapac/stm32f051r6" ]
stm32f051r8 = [ "stm32-metapac/stm32f051r8" ]
stm32f051t8 = [ "stm32-metapac/stm32f051t8" ]
stm32f058c8 = [ "stm32-metapac/stm32f058c8" ]
stm32f058r8 = [ "stm32-metapac/stm32f058r8" ]
stm32f058t8 = [ "stm32-metapac/stm32f058t8" ]
stm32f070c6 = [ "stm32-metapac/stm32f070c6" ]
stm32f070cb = [ "stm32-metapac/stm32f070cb" ]
stm32f070f6 = [ "stm32-metapac/stm32f070f6" ]
stm32f070rb = [ "stm32-metapac/stm32f070rb" ]
stm32f071c8 = [ "stm32-metapac/stm32f071c8" ]
stm32f071cb = [ "stm32-metapac/stm32f071cb" ]
stm32f071rb = [ "stm32-metapac/stm32f071rb" ]
stm32f071v8 = [ "stm32-metapac/stm32f071v8" ]
stm32f071vb = [ "stm32-metapac/stm32f071vb" ]
stm32f072c8 = [ "stm32-metapac/stm32f072c8" ]
stm32f072cb = [ "stm32-metapac/stm32f072cb" ]
stm32f072r8 = [ "stm32-metapac/stm32f072r8" ]
stm32f072rb = [ "stm32-metapac/stm32f072rb" ]
stm32f072v8 = [ "stm32-metapac/stm32f072v8" ]
stm32f072vb = [ "stm32-metapac/stm32f072vb" ]
stm32f078cb = [ "stm32-metapac/stm32f078cb" ]
stm32f078rb = [ "stm32-metapac/stm32f078rb" ]
stm32f078vb = [ "stm32-metapac/stm32f078vb" ]
stm32f091cb = [ "stm32-metapac/stm32f091cb" ]
stm32f091cc = [ "stm32-metapac/stm32f091cc" ]
stm32f091rb = [ "stm32-metapac/stm32f091rb" ]
stm32f091rc = [ "stm32-metapac/stm32f091rc" ]
stm32f091vb = [ "stm32-metapac/stm32f091vb" ]
stm32f091vc = [ "stm32-metapac/stm32f091vc" ]
stm32f098cc = [ "stm32-metapac/stm32f098cc" ]
stm32f098rc = [ "stm32-metapac/stm32f098rc" ]
stm32f098vc = [ "stm32-metapac/stm32f098vc" ]
stm32f100c4 = [ "stm32-metapac/stm32f100c4" ]
stm32f100c6 = [ "stm32-metapac/stm32f100c6" ]
stm32f100c8 = [ "stm32-metapac/stm32f100c8" ]
stm32f100cb = [ "stm32-metapac/stm32f100cb" ]
stm32f100r4 = [ "stm32-metapac/stm32f100r4" ]
stm32f100r6 = [ "stm32-metapac/stm32f100r6" ]
stm32f100r8 = [ "stm32-metapac/stm32f100r8" ]
stm32f100rb = [ "stm32-metapac/stm32f100rb" ]
stm32f100rc = [ "stm32-metapac/stm32f100rc" ]
stm32f100rd = [ "stm32-metapac/stm32f100rd" ]
stm32f100re = [ "stm32-metapac/stm32f100re" ]
stm32f100v8 = [ "stm32-metapac/stm32f100v8" ]
stm32f100vb = [ "stm32-metapac/stm32f100vb" ]
stm32f100vc = [ "stm32-metapac/stm32f100vc" ]
stm32f100vd = [ "stm32-metapac/stm32f100vd" ]
stm32f100ve = [ "stm32-metapac/stm32f100ve" ]
stm32f100zc = [ "stm32-metapac/stm32f100zc" ]
stm32f100zd = [ "stm32-metapac/stm32f100zd" ]
stm32f100ze = [ "stm32-metapac/stm32f100ze" ]
stm32f101c4 = [ "stm32-metapac/stm32f101c4" ]
stm32f101c6 = [ "stm32-metapac/stm32f101c6" ]
stm32f101c8 = [ "stm32-metapac/stm32f101c8" ]
stm32f101cb = [ "stm32-metapac/stm32f101cb" ]
stm32f101r4 = [ "stm32-metapac/stm32f101r4" ]
stm32f101r6 = [ "stm32-metapac/stm32f101r6" ]
stm32f101r8 = [ "stm32-metapac/stm32f101r8" ]
stm32f101rb = [ "stm32-metapac/stm32f101rb" ]
stm32f101rc = [ "stm32-metapac/stm32f101rc" ]
stm32f101rd = [ "stm32-metapac/stm32f101rd" ]
stm32f101re = [ "stm32-metapac/stm32f101re" ]
stm32f101rf = [ "stm32-metapac/stm32f101rf" ]
stm32f101rg = [ "stm32-metapac/stm32f101rg" ]
stm32f101t4 = [ "stm32-metapac/stm32f101t4" ]
stm32f101t6 = [ "stm32-metapac/stm32f101t6" ]
stm32f101t8 = [ "stm32-metapac/stm32f101t8" ]
stm32f101tb = [ "stm32-metapac/stm32f101tb" ]
stm32f101v8 = [ "stm32-metapac/stm32f101v8" ]
stm32f101vb = [ "stm32-metapac/stm32f101vb" ]
stm32f101vc = [ "stm32-metapac/stm32f101vc" ]
stm32f101vd = [ "stm32-metapac/stm32f101vd" ]
stm32f101ve = [ "stm32-metapac/stm32f101ve" ]
stm32f101vf = [ "stm32-metapac/stm32f101vf" ]
stm32f101vg = [ "stm32-metapac/stm32f101vg" ]
stm32f101zc = [ "stm32-metapac/stm32f101zc" ]
stm32f101zd = [ "stm32-metapac/stm32f101zd" ]
stm32f101ze = [ "stm32-metapac/stm32f101ze" ]
stm32f101zf = [ "stm32-metapac/stm32f101zf" ]
stm32f101zg = [ "stm32-metapac/stm32f101zg" ]
stm32f102c4 = [ "stm32-metapac/stm32f102c4" ]
stm32f102c6 = [ "stm32-metapac/stm32f102c6" ]
stm32f102c8 = [ "stm32-metapac/stm32f102c8" ]
stm32f102cb = [ "stm32-metapac/stm32f102cb" ]
stm32f102r4 = [ "stm32-metapac/stm32f102r4" ]
stm32f102r6 = [ "stm32-metapac/stm32f102r6" ]
stm32f102r8 = [ "stm32-metapac/stm32f102r8" ]
stm32f102rb = [ "stm32-metapac/stm32f102rb" ]
stm32f103c4 = [ "stm32-metapac/stm32f103c4" ]
stm32f103c6 = [ "stm32-metapac/stm32f103c6" ]
stm32f103c8 = [ "stm32-metapac/stm32f103c8" ]
stm32f103cb = [ "stm32-metapac/stm32f103cb" ]
stm32f103r4 = [ "stm32-metapac/stm32f103r4" ]
stm32f103r6 = [ "stm32-metapac/stm32f103r6" ]
stm32f103r8 = [ "stm32-metapac/stm32f103r8" ]
stm32f103rb = [ "stm32-metapac/stm32f103rb" ]
stm32f103rc = [ "stm32-metapac/stm32f103rc" ]
stm32f103rd = [ "stm32-metapac/stm32f103rd" ]
stm32f103re = [ "stm32-metapac/stm32f103re" ]
stm32f103rf = [ "stm32-metapac/stm32f103rf" ]
stm32f103rg = [ "stm32-metapac/stm32f103rg" ]
stm32f103t4 = [ "stm32-metapac/stm32f103t4" ]
stm32f103t6 = [ "stm32-metapac/stm32f103t6" ]
stm32f103t8 = [ "stm32-metapac/stm32f103t8" ]
stm32f103tb = [ "stm32-metapac/stm32f103tb" ]
stm32f103v8 = [ "stm32-metapac/stm32f103v8" ]
stm32f103vb = [ "stm32-metapac/stm32f103vb" ]
stm32f103vc = [ "stm32-metapac/stm32f103vc" ]
stm32f103vd = [ "stm32-metapac/stm32f103vd" ]
stm32f103ve = [ "stm32-metapac/stm32f103ve" ]
stm32f103vf = [ "stm32-metapac/stm32f103vf" ]
stm32f103vg = [ "stm32-metapac/stm32f103vg" ]
stm32f103zc = [ "stm32-metapac/stm32f103zc" ]
stm32f103zd = [ "stm32-metapac/stm32f103zd" ]
stm32f103ze = [ "stm32-metapac/stm32f103ze" ]
stm32f103zf = [ "stm32-metapac/stm32f103zf" ]
stm32f103zg = [ "stm32-metapac/stm32f103zg" ]
stm32f105r8 = [ "stm32-metapac/stm32f105r8" ]
stm32f105rb = [ "stm32-metapac/stm32f105rb" ]
stm32f105rc = [ "stm32-metapac/stm32f105rc" ]
stm32f105v8 = [ "stm32-metapac/stm32f105v8" ]
stm32f105vb = [ "stm32-metapac/stm32f105vb" ]
stm32f105vc = [ "stm32-metapac/stm32f105vc" ]
stm32f107rb = [ "stm32-metapac/stm32f107rb" ]
stm32f107rc = [ "stm32-metapac/stm32f107rc" ]
stm32f107vb = [ "stm32-metapac/stm32f107vb" ]
stm32f107vc = [ "stm32-metapac/stm32f107vc" ]
stm32f205rb = [ "stm32-metapac/stm32f205rb" ]
stm32f205rc = [ "stm32-metapac/stm32f205rc" ]
stm32f205re = [ "stm32-metapac/stm32f205re" ]
stm32f205rf = [ "stm32-metapac/stm32f205rf" ]
stm32f205rg = [ "stm32-metapac/stm32f205rg" ]
stm32f205vb = [ "stm32-metapac/stm32f205vb" ]
stm32f205vc = [ "stm32-metapac/stm32f205vc" ]
stm32f205ve = [ "stm32-metapac/stm32f205ve" ]
stm32f205vf = [ "stm32-metapac/stm32f205vf" ]
stm32f205vg = [ "stm32-metapac/stm32f205vg" ]
stm32f205zc = [ "stm32-metapac/stm32f205zc" ]
stm32f205ze = [ "stm32-metapac/stm32f205ze" ]
stm32f205zf = [ "stm32-metapac/stm32f205zf" ]
stm32f205zg = [ "stm32-metapac/stm32f205zg" ]
stm32f207ic = [ "stm32-metapac/stm32f207ic" ]
stm32f207ie = [ "stm32-metapac/stm32f207ie" ]
stm32f207if = [ "stm32-metapac/stm32f207if" ]
stm32f207ig = [ "stm32-metapac/stm32f207ig" ]
stm32f207vc = [ "stm32-metapac/stm32f207vc" ]
stm32f207ve = [ "stm32-metapac/stm32f207ve" ]
stm32f207vf = [ "stm32-metapac/stm32f207vf" ]
stm32f207vg = [ "stm32-metapac/stm32f207vg" ]
stm32f207zc = [ "stm32-metapac/stm32f207zc" ]
stm32f207ze = [ "stm32-metapac/stm32f207ze" ]
stm32f207zf = [ "stm32-metapac/stm32f207zf" ]
stm32f207zg = [ "stm32-metapac/stm32f207zg" ]
stm32f215re = [ "stm32-metapac/stm32f215re" ]
stm32f215rg = [ "stm32-metapac/stm32f215rg" ]
stm32f215ve = [ "stm32-metapac/stm32f215ve" ]
stm32f215vg = [ "stm32-metapac/stm32f215vg" ]
stm32f215ze = [ "stm32-metapac/stm32f215ze" ]
stm32f215zg = [ "stm32-metapac/stm32f215zg" ]
stm32f217ie = [ "stm32-metapac/stm32f217ie" ]
stm32f217ig = [ "stm32-metapac/stm32f217ig" ]
stm32f217ve = [ "stm32-metapac/stm32f217ve" ]
stm32f217vg = [ "stm32-metapac/stm32f217vg" ]
stm32f217ze = [ "stm32-metapac/stm32f217ze" ]
stm32f217zg = [ "stm32-metapac/stm32f217zg" ]
stm32f301c6 = [ "stm32-metapac/stm32f301c6" ]
stm32f301c8 = [ "stm32-metapac/stm32f301c8" ]
stm32f301k6 = [ "stm32-metapac/stm32f301k6" ]
stm32f301k8 = [ "stm32-metapac/stm32f301k8" ]
stm32f301r6 = [ "stm32-metapac/stm32f301r6" ]
stm32f301r8 = [ "stm32-metapac/stm32f301r8" ]
stm32f302c6 = [ "stm32-metapac/stm32f302c6" ]
stm32f302c8 = [ "stm32-metapac/stm32f302c8" ]
stm32f302cb = [ "stm32-metapac/stm32f302cb" ]
stm32f302cc = [ "stm32-metapac/stm32f302cc" ]
stm32f302k6 = [ "stm32-metapac/stm32f302k6" ]
stm32f302k8 = [ "stm32-metapac/stm32f302k8" ]
stm32f302r6 = [ "stm32-metapac/stm32f302r6" ]
stm32f302r8 = [ "stm32-metapac/stm32f302r8" ]
stm32f302rb = [ "stm32-metapac/stm32f302rb" ]
stm32f302rc = [ "stm32-metapac/stm32f302rc" ]
stm32f302rd = [ "stm32-metapac/stm32f302rd" ]
stm32f302re = [ "stm32-metapac/stm32f302re" ]
stm32f302vb = [ "stm32-metapac/stm32f302vb" ]
stm32f302vc = [ "stm32-metapac/stm32f302vc" ]
stm32f302vd = [ "stm32-metapac/stm32f302vd" ]
stm32f302ve = [ "stm32-metapac/stm32f302ve" ]
stm32f302zd = [ "stm32-metapac/stm32f302zd" ]
stm32f302ze = [ "stm32-metapac/stm32f302ze" ]
stm32f303c6 = [ "stm32-metapac/stm32f303c6" ]
stm32f303c8 = [ "stm32-metapac/stm32f303c8" ]
stm32f303cb = [ "stm32-metapac/stm32f303cb" ]
stm32f303cc = [ "stm32-metapac/stm32f303cc" ]
stm32f303k6 = [ "stm32-metapac/stm32f303k6" ]
stm32f303k8 = [ "stm32-metapac/stm32f303k8" ]
stm32f303r6 = [ "stm32-metapac/stm32f303r6" ]
stm32f303r8 = [ "stm32-metapac/stm32f303r8" ]
stm32f303rb = [ "stm32-metapac/stm32f303rb" ]
stm32f303rc = [ "stm32-metapac/stm32f303rc" ]
stm32f303rd = [ "stm32-metapac/stm32f303rd" ]
stm32f303re = [ "stm32-metapac/stm32f303re" ]
stm32f303vb = [ "stm32-metapac/stm32f303vb" ]
stm32f303vc = [ "stm32-metapac/stm32f303vc" ]
stm32f303vd = [ "stm32-metapac/stm32f303vd" ]
stm32f303ve = [ "stm32-metapac/stm32f303ve" ]
stm32f303zd = [ "stm32-metapac/stm32f303zd" ]
stm32f303ze = [ "stm32-metapac/stm32f303ze" ]
stm32f318c8 = [ "stm32-metapac/stm32f318c8" ]
stm32f318k8 = [ "stm32-metapac/stm32f318k8" ]
stm32f328c8 = [ "stm32-metapac/stm32f328c8" ]
stm32f334c4 = [ "stm32-metapac/stm32f334c4" ]
stm32f334c6 = [ "stm32-metapac/stm32f334c6" ]
stm32f334c8 = [ "stm32-metapac/stm32f334c8" ]
stm32f334k4 = [ "stm32-metapac/stm32f334k4" ]
stm32f334k6 = [ "stm32-metapac/stm32f334k6" ]
stm32f334k8 = [ "stm32-metapac/stm32f334k8" ]
stm32f334r6 = [ "stm32-metapac/stm32f334r6" ]
stm32f334r8 = [ "stm32-metapac/stm32f334r8" ]
stm32f358cc = [ "stm32-metapac/stm32f358cc" ]
stm32f358rc = [ "stm32-metapac/stm32f358rc" ]
stm32f358vc = [ "stm32-metapac/stm32f358vc" ]
stm32f373c8 = [ "stm32-metapac/stm32f373c8" ]
stm32f373cb = [ "stm32-metapac/stm32f373cb" ]
stm32f373cc = [ "stm32-metapac/stm32f373cc" ]
stm32f373r8 = [ "stm32-metapac/stm32f373r8" ]
stm32f373rb = [ "stm32-metapac/stm32f373rb" ]
stm32f373rc = [ "stm32-metapac/stm32f373rc" ]
stm32f373v8 = [ "stm32-metapac/stm32f373v8" ]
stm32f373vb = [ "stm32-metapac/stm32f373vb" ]
stm32f373vc = [ "stm32-metapac/stm32f373vc" ]
stm32f378cc = [ "stm32-metapac/stm32f378cc" ]
stm32f378rc = [ "stm32-metapac/stm32f378rc" ]
stm32f378vc = [ "stm32-metapac/stm32f378vc" ]
stm32f398ve = [ "stm32-metapac/stm32f398ve" ]
stm32f401cb = [ "stm32-metapac/stm32f401cb" ]
stm32f401cc = [ "stm32-metapac/stm32f401cc" ]
stm32f401cd = [ "stm32-metapac/stm32f401cd" ]
stm32f401ce = [ "stm32-metapac/stm32f401ce" ]
stm32f401rb = [ "stm32-metapac/stm32f401rb" ]
stm32f401rc = [ "stm32-metapac/stm32f401rc" ]
stm32f401rd = [ "stm32-metapac/stm32f401rd" ]
stm32f401re = [ "stm32-metapac/stm32f401re" ]
stm32f401vb = [ "stm32-metapac/stm32f401vb" ]
stm32f401vc = [ "stm32-metapac/stm32f401vc" ]
stm32f401vd = [ "stm32-metapac/stm32f401vd" ]
stm32f401ve = [ "stm32-metapac/stm32f401ve" ]
stm32f405oe = [ "stm32-metapac/stm32f405oe" ]
stm32f405og = [ "stm32-metapac/stm32f405og" ]
stm32f405rg = [ "stm32-metapac/stm32f405rg" ]
stm32f405vg = [ "stm32-metapac/stm32f405vg" ]
stm32f405zg = [ "stm32-metapac/stm32f405zg" ]
stm32f407ie = [ "stm32-metapac/stm32f407ie" ]
stm32f407ig = [ "stm32-metapac/stm32f407ig" ]
stm32f407ve = [ "stm32-metapac/stm32f407ve" ]
stm32f407vg = [ "stm32-metapac/stm32f407vg" ]
stm32f407ze = [ "stm32-metapac/stm32f407ze" ]
stm32f407zg = [ "stm32-metapac/stm32f407zg" ]
stm32f410c8 = [ "stm32-metapac/stm32f410c8" ]
stm32f410cb = [ "stm32-metapac/stm32f410cb" ]
stm32f410r8 = [ "stm32-metapac/stm32f410r8" ]
stm32f410rb = [ "stm32-metapac/stm32f410rb" ]
stm32f410t8 = [ "stm32-metapac/stm32f410t8" ]
stm32f410tb = [ "stm32-metapac/stm32f410tb" ]
stm32f411cc = [ "stm32-metapac/stm32f411cc" ]
stm32f411ce = [ "stm32-metapac/stm32f411ce" ]
stm32f411rc = [ "stm32-metapac/stm32f411rc" ]
stm32f411re = [ "stm32-metapac/stm32f411re" ]
stm32f411vc = [ "stm32-metapac/stm32f411vc" ]
stm32f411ve = [ "stm32-metapac/stm32f411ve" ]
stm32f412ce = [ "stm32-metapac/stm32f412ce" ]
stm32f412cg = [ "stm32-metapac/stm32f412cg" ]
stm32f412re = [ "stm32-metapac/stm32f412re" ]
stm32f412rg = [ "stm32-metapac/stm32f412rg" ]
stm32f412ve = [ "stm32-metapac/stm32f412ve" ]
stm32f412vg = [ "stm32-metapac/stm32f412vg" ]
stm32f412ze = [ "stm32-metapac/stm32f412ze" ]
stm32f412zg = [ "stm32-metapac/stm32f412zg" ]
stm32f413cg = [ "stm32-metapac/stm32f413cg" ]
stm32f413ch = [ "stm32-metapac/stm32f413ch" ]
stm32f413mg = [ "stm32-metapac/stm32f413mg" ]
stm32f413mh = [ "stm32-metapac/stm32f413mh" ]
stm32f413rg = [ "stm32-metapac/stm32f413rg" ]
stm32f413rh = [ "stm32-metapac/stm32f413rh" ]
stm32f413vg = [ "stm32-metapac/stm32f413vg" ]
stm32f413vh = [ "stm32-metapac/stm32f413vh" ]
stm32f413zg = [ "stm32-metapac/stm32f413zg" ]
stm32f413zh = [ "stm32-metapac/stm32f413zh" ]
stm32f415og = [ "stm32-metapac/stm32f415og" ]
stm32f415rg = [ "stm32-metapac/stm32f415rg" ]
stm32f415vg = [ "stm32-metapac/stm32f415vg" ]
stm32f415zg = [ "stm32-metapac/stm32f415zg" ]
stm32f417ie = [ "stm32-metapac/stm32f417ie" ]
stm32f417ig = [ "stm32-metapac/stm32f417ig" ]
stm32f417ve = [ "stm32-metapac/stm32f417ve" ]
stm32f417vg = [ "stm32-metapac/stm32f417vg" ]
stm32f417ze = [ "stm32-metapac/stm32f417ze" ]
stm32f417zg = [ "stm32-metapac/stm32f417zg" ]
stm32f423ch = [ "stm32-metapac/stm32f423ch" ]
stm32f423mh = [ "stm32-metapac/stm32f423mh" ]
stm32f423rh = [ "stm32-metapac/stm32f423rh" ]
stm32f423vh = [ "stm32-metapac/stm32f423vh" ]
stm32f423zh = [ "stm32-metapac/stm32f423zh" ]
stm32f427ag = [ "stm32-metapac/stm32f427ag" ]
stm32f427ai = [ "stm32-metapac/stm32f427ai" ]
stm32f427ig = [ "stm32-metapac/stm32f427ig" ]
stm32f427ii = [ "stm32-metapac/stm32f427ii" ]
stm32f427vg = [ "stm32-metapac/stm32f427vg" ]
stm32f427vi = [ "stm32-metapac/stm32f427vi" ]
stm32f427zg = [ "stm32-metapac/stm32f427zg" ]
stm32f427zi = [ "stm32-metapac/stm32f427zi" ]
stm32f429ag = [ "stm32-metapac/stm32f429ag" ]
stm32f429ai = [ "stm32-metapac/stm32f429ai" ]
stm32f429be = [ "stm32-metapac/stm32f429be" ]
stm32f429bg = [ "stm32-metapac/stm32f429bg" ]
stm32f429bi = [ "stm32-metapac/stm32f429bi" ]
stm32f429ie = [ "stm32-metapac/stm32f429ie" ]
stm32f429ig = [ "stm32-metapac/stm32f429ig" ]
stm32f429ii = [ "stm32-metapac/stm32f429ii" ]
stm32f429ne = [ "stm32-metapac/stm32f429ne" ]
stm32f429ng = [ "stm32-metapac/stm32f429ng" ]
stm32f429ni = [ "stm32-metapac/stm32f429ni" ]
stm32f429ve = [ "stm32-metapac/stm32f429ve" ]
stm32f429vg = [ "stm32-metapac/stm32f429vg" ]
stm32f429vi = [ "stm32-metapac/stm32f429vi" ]
stm32f429ze = [ "stm32-metapac/stm32f429ze" ]
stm32f429zg = [ "stm32-metapac/stm32f429zg" ]
stm32f429zi = [ "stm32-metapac/stm32f429zi" ]
stm32f437ai = [ "stm32-metapac/stm32f437ai" ]
stm32f437ig = [ "stm32-metapac/stm32f437ig" ]
stm32f437ii = [ "stm32-metapac/stm32f437ii" ]
stm32f437vg = [ "stm32-metapac/stm32f437vg" ]
stm32f437vi = [ "stm32-metapac/stm32f437vi" ]
stm32f437zg = [ "stm32-metapac/stm32f437zg" ]
stm32f437zi = [ "stm32-metapac/stm32f437zi" ]
stm32f439ai = [ "stm32-metapac/stm32f439ai" ]
stm32f439bg = [ "stm32-metapac/stm32f439bg" ]
stm32f439bi = [ "stm32-metapac/stm32f439bi" ]
stm32f439ig = [ "stm32-metapac/stm32f439ig" ]
stm32f439ii = [ "stm32-metapac/stm32f439ii" ]
stm32f439ng = [ "stm32-metapac/stm32f439ng" ]
stm32f439ni = [ "stm32-metapac/stm32f439ni" ]
stm32f439vg = [ "stm32-metapac/stm32f439vg" ]
stm32f439vi = [ "stm32-metapac/stm32f439vi" ]
stm32f439zg = [ "stm32-metapac/stm32f439zg" ]
stm32f439zi = [ "stm32-metapac/stm32f439zi" ]
stm32f446mc = [ "stm32-metapac/stm32f446mc" ]
stm32f446me = [ "stm32-metapac/stm32f446me" ]
stm32f446rc = [ "stm32-metapac/stm32f446rc" ]
stm32f446re = [ "stm32-metapac/stm32f446re" ]
stm32f446vc = [ "stm32-metapac/stm32f446vc" ]
stm32f446ve = [ "stm32-metapac/stm32f446ve" ]
stm32f446zc = [ "stm32-metapac/stm32f446zc" ]
stm32f446ze = [ "stm32-metapac/stm32f446ze" ]
stm32f469ae = [ "stm32-metapac/stm32f469ae" ]
stm32f469ag = [ "stm32-metapac/stm32f469ag" ]
stm32f469ai = [ "stm32-metapac/stm32f469ai" ]
stm32f469be = [ "stm32-metapac/stm32f469be" ]
stm32f469bg = [ "stm32-metapac/stm32f469bg" ]
stm32f469bi = [ "stm32-metapac/stm32f469bi" ]
stm32f469ie = [ "stm32-metapac/stm32f469ie" ]
stm32f469ig = [ "stm32-metapac/stm32f469ig" ]
stm32f469ii = [ "stm32-metapac/stm32f469ii" ]
stm32f469ne = [ "stm32-metapac/stm32f469ne" ]
stm32f469ng = [ "stm32-metapac/stm32f469ng" ]
stm32f469ni = [ "stm32-metapac/stm32f469ni" ]
stm32f469ve = [ "stm32-metapac/stm32f469ve" ]
stm32f469vg = [ "stm32-metapac/stm32f469vg" ]
stm32f469vi = [ "stm32-metapac/stm32f469vi" ]
stm32f469ze = [ "stm32-metapac/stm32f469ze" ]
stm32f469zg = [ "stm32-metapac/stm32f469zg" ]
stm32f469zi = [ "stm32-metapac/stm32f469zi" ]
stm32f479ag = [ "stm32-metapac/stm32f479ag" ]
stm32f479ai = [ "stm32-metapac/stm32f479ai" ]
stm32f479bg = [ "stm32-metapac/stm32f479bg" ]
stm32f479bi = [ "stm32-metapac/stm32f479bi" ]
stm32f479ig = [ "stm32-metapac/stm32f479ig" ]
stm32f479ii = [ "stm32-metapac/stm32f479ii" ]
stm32f479ng = [ "stm32-metapac/stm32f479ng" ]
stm32f479ni = [ "stm32-metapac/stm32f479ni" ]
stm32f479vg = [ "stm32-metapac/stm32f479vg" ]
stm32f479vi = [ "stm32-metapac/stm32f479vi" ]
stm32f479zg = [ "stm32-metapac/stm32f479zg" ]
stm32f479zi = [ "stm32-metapac/stm32f479zi" ]
stm32f722ic = [ "stm32-metapac/stm32f722ic" ]
stm32f722ie = [ "stm32-metapac/stm32f722ie" ]
stm32f722rc = [ "stm32-metapac/stm32f722rc" ]
stm32f722re = [ "stm32-metapac/stm32f722re" ]
stm32f722vc = [ "stm32-metapac/stm32f722vc" ]
stm32f722ve = [ "stm32-metapac/stm32f722ve" ]
stm32f722zc = [ "stm32-metapac/stm32f722zc" ]
stm32f722ze = [ "stm32-metapac/stm32f722ze" ]
stm32f723ic = [ "stm32-metapac/stm32f723ic" ]
stm32f723ie = [ "stm32-metapac/stm32f723ie" ]
stm32f723vc = [ "stm32-metapac/stm32f723vc" ]
stm32f723ve = [ "stm32-metapac/stm32f723ve" ]
stm32f723zc = [ "stm32-metapac/stm32f723zc" ]
stm32f723ze = [ "stm32-metapac/stm32f723ze" ]
stm32f730i8 = [ "stm32-metapac/stm32f730i8" ]
stm32f730r8 = [ "stm32-metapac/stm32f730r8" ]
stm32f730v8 = [ "stm32-metapac/stm32f730v8" ]
stm32f730z8 = [ "stm32-metapac/stm32f730z8" ]
stm32f732ie = [ "stm32-metapac/stm32f732ie" ]
stm32f732re = [ "stm32-metapac/stm32f732re" ]
stm32f732ve = [ "stm32-metapac/stm32f732ve" ]
stm32f732ze = [ "stm32-metapac/stm32f732ze" ]
stm32f733ie = [ "stm32-metapac/stm32f733ie" ]
stm32f733ve = [ "stm32-metapac/stm32f733ve" ]
stm32f733ze = [ "stm32-metapac/stm32f733ze" ]
stm32f745ie = [ "stm32-metapac/stm32f745ie" ]
stm32f745ig = [ "stm32-metapac/stm32f745ig" ]
stm32f745ve = [ "stm32-metapac/stm32f745ve" ]
stm32f745vg = [ "stm32-metapac/stm32f745vg" ]
stm32f745ze = [ "stm32-metapac/stm32f745ze" ]
stm32f745zg = [ "stm32-metapac/stm32f745zg" ]
stm32f746be = [ "stm32-metapac/stm32f746be" ]
stm32f746bg = [ "stm32-metapac/stm32f746bg" ]
stm32f746ie = [ "stm32-metapac/stm32f746ie" ]
stm32f746ig = [ "stm32-metapac/stm32f746ig" ]
stm32f746ne = [ "stm32-metapac/stm32f746ne" ]
stm32f746ng = [ "stm32-metapac/stm32f746ng" ]
stm32f746ve = [ "stm32-metapac/stm32f746ve" ]
stm32f746vg = [ "stm32-metapac/stm32f746vg" ]
stm32f746ze = [ "stm32-metapac/stm32f746ze" ]
stm32f746zg = [ "stm32-metapac/stm32f746zg" ]
stm32f750n8 = [ "stm32-metapac/stm32f750n8" ]
stm32f750v8 = [ "stm32-metapac/stm32f750v8" ]
stm32f750z8 = [ "stm32-metapac/stm32f750z8" ]
stm32f756bg = [ "stm32-metapac/stm32f756bg" ]
stm32f756ig = [ "stm32-metapac/stm32f756ig" ]
stm32f756ng = [ "stm32-metapac/stm32f756ng" ]
stm32f756vg = [ "stm32-metapac/stm32f756vg" ]
stm32f756zg = [ "stm32-metapac/stm32f756zg" ]
stm32f765bg = [ "stm32-metapac/stm32f765bg" ]
stm32f765bi = [ "stm32-metapac/stm32f765bi" ]
stm32f765ig = [ "stm32-metapac/stm32f765ig" ]
stm32f765ii = [ "stm32-metapac/stm32f765ii" ]
stm32f765ng = [ "stm32-metapac/stm32f765ng" ]
stm32f765ni = [ "stm32-metapac/stm32f765ni" ]
stm32f765vg = [ "stm32-metapac/stm32f765vg" ]
stm32f765vi = [ "stm32-metapac/stm32f765vi" ]
stm32f765zg = [ "stm32-metapac/stm32f765zg" ]
stm32f765zi = [ "stm32-metapac/stm32f765zi" ]
stm32f767bg = [ "stm32-metapac/stm32f767bg" ]
stm32f767bi = [ "stm32-metapac/stm32f767bi" ]
stm32f767ig = [ "stm32-metapac/stm32f767ig" ]
stm32f767ii = [ "stm32-metapac/stm32f767ii" ]
stm32f767ng = [ "stm32-metapac/stm32f767ng" ]
stm32f767ni = [ "stm32-metapac/stm32f767ni" ]
stm32f767vg = [ "stm32-metapac/stm32f767vg" ]
stm32f767vi = [ "stm32-metapac/stm32f767vi" ]
stm32f767zg = [ "stm32-metapac/stm32f767zg" ]
stm32f767zi = [ "stm32-metapac/stm32f767zi" ]
stm32f768ai = [ "stm32-metapac/stm32f768ai" ]
stm32f769ag = [ "stm32-metapac/stm32f769ag" ]
stm32f769ai = [ "stm32-metapac/stm32f769ai" ]
stm32f769bg = [ "stm32-metapac/stm32f769bg" ]
stm32f769bi = [ "stm32-metapac/stm32f769bi" ]
stm32f769ig = [ "stm32-metapac/stm32f769ig" ]
stm32f769ii = [ "stm32-metapac/stm32f769ii" ]
stm32f769ng = [ "stm32-metapac/stm32f769ng" ]
stm32f769ni = [ "stm32-metapac/stm32f769ni" ]
stm32f777bi = [ "stm32-metapac/stm32f777bi" ]
stm32f777ii = [ "stm32-metapac/stm32f777ii" ]
stm32f777ni = [ "stm32-metapac/stm32f777ni" ]
stm32f777vi = [ "stm32-metapac/stm32f777vi" ]
stm32f777zi = [ "stm32-metapac/stm32f777zi" ]
stm32f778ai = [ "stm32-metapac/stm32f778ai" ]
stm32f779ai = [ "stm32-metapac/stm32f779ai" ]
stm32f779bi = [ "stm32-metapac/stm32f779bi" ]
stm32f779ii = [ "stm32-metapac/stm32f779ii" ]
stm32f779ni = [ "stm32-metapac/stm32f779ni" ]
stm32g030c6 = [ "stm32-metapac/stm32g030c6" ]
stm32g030c8 = [ "stm32-metapac/stm32g030c8" ]
stm32g030f6 = [ "stm32-metapac/stm32g030f6" ]
stm32g030j6 = [ "stm32-metapac/stm32g030j6" ]
stm32g030k6 = [ "stm32-metapac/stm32g030k6" ]
stm32g030k8 = [ "stm32-metapac/stm32g030k8" ]
stm32g031c4 = [ "stm32-metapac/stm32g031c4" ]
stm32g031c6 = [ "stm32-metapac/stm32g031c6" ]
stm32g031c8 = [ "stm32-metapac/stm32g031c8" ]
stm32g031f4 = [ "stm32-metapac/stm32g031f4" ]
stm32g031f6 = [ "stm32-metapac/stm32g031f6" ]
stm32g031f8 = [ "stm32-metapac/stm32g031f8" ]
stm32g031g4 = [ "stm32-metapac/stm32g031g4" ]
stm32g031g6 = [ "stm32-metapac/stm32g031g6" ]
stm32g031g8 = [ "stm32-metapac/stm32g031g8" ]
stm32g031j4 = [ "stm32-metapac/stm32g031j4" ]
stm32g031j6 = [ "stm32-metapac/stm32g031j6" ]
stm32g031k4 = [ "stm32-metapac/stm32g031k4" ]
stm32g031k6 = [ "stm32-metapac/stm32g031k6" ]
stm32g031k8 = [ "stm32-metapac/stm32g031k8" ]
stm32g031y8 = [ "stm32-metapac/stm32g031y8" ]
stm32g041c6 = [ "stm32-metapac/stm32g041c6" ]
stm32g041c8 = [ "stm32-metapac/stm32g041c8" ]
stm32g041f6 = [ "stm32-metapac/stm32g041f6" ]
stm32g041f8 = [ "stm32-metapac/stm32g041f8" ]
stm32g041g6 = [ "stm32-metapac/stm32g041g6" ]
stm32g041g8 = [ "stm32-metapac/stm32g041g8" ]
stm32g041j6 = [ "stm32-metapac/stm32g041j6" ]
stm32g041k6 = [ "stm32-metapac/stm32g041k6" ]
stm32g041k8 = [ "stm32-metapac/stm32g041k8" ]
stm32g041y8 = [ "stm32-metapac/stm32g041y8" ]
stm32g050c6 = [ "stm32-metapac/stm32g050c6" ]
stm32g050c8 = [ "stm32-metapac/stm32g050c8" ]
stm32g050f6 = [ "stm32-metapac/stm32g050f6" ]
stm32g050k6 = [ "stm32-metapac/stm32g050k6" ]
stm32g050k8 = [ "stm32-metapac/stm32g050k8" ]
stm32g051c6 = [ "stm32-metapac/stm32g051c6" ]
stm32g051c8 = [ "stm32-metapac/stm32g051c8" ]
stm32g051f6 = [ "stm32-metapac/stm32g051f6" ]
stm32g051f8 = [ "stm32-metapac/stm32g051f8" ]
stm32g051g6 = [ "stm32-metapac/stm32g051g6" ]
stm32g051g8 = [ "stm32-metapac/stm32g051g8" ]
stm32g051k6 = [ "stm32-metapac/stm32g051k6" ]
stm32g051k8 = [ "stm32-metapac/stm32g051k8" ]
stm32g061c6 = [ "stm32-metapac/stm32g061c6" ]
stm32g061c8 = [ "stm32-metapac/stm32g061c8" ]
stm32g061f6 = [ "stm32-metapac/stm32g061f6" ]
stm32g061f8 = [ "stm32-metapac/stm32g061f8" ]
stm32g061g6 = [ "stm32-metapac/stm32g061g6" ]
stm32g061g8 = [ "stm32-metapac/stm32g061g8" ]
stm32g061k6 = [ "stm32-metapac/stm32g061k6" ]
stm32g061k8 = [ "stm32-metapac/stm32g061k8" ]
stm32g070cb = [ "stm32-metapac/stm32g070cb" ]
stm32g070kb = [ "stm32-metapac/stm32g070kb" ]
stm32g070rb = [ "stm32-metapac/stm32g070rb" ]
stm32g071c6 = [ "stm32-metapac/stm32g071c6" ]
stm32g071c8 = [ "stm32-metapac/stm32g071c8" ]
stm32g071cb = [ "stm32-metapac/stm32g071cb" ]
stm32g071eb = [ "stm32-metapac/stm32g071eb" ]
stm32g071g6 = [ "stm32-metapac/stm32g071g6" ]
stm32g071g8 = [ "stm32-metapac/stm32g071g8" ]
stm32g071gb = [ "stm32-metapac/stm32g071gb" ]
stm32g071k6 = [ "stm32-metapac/stm32g071k6" ]
stm32g071k8 = [ "stm32-metapac/stm32g071k8" ]
stm32g071kb = [ "stm32-metapac/stm32g071kb" ]
stm32g071r6 = [ "stm32-metapac/stm32g071r6" ]
stm32g071r8 = [ "stm32-metapac/stm32g071r8" ]
stm32g071rb = [ "stm32-metapac/stm32g071rb" ]
stm32g081cb = [ "stm32-metapac/stm32g081cb" ]
stm32g081eb = [ "stm32-metapac/stm32g081eb" ]
stm32g081gb = [ "stm32-metapac/stm32g081gb" ]
stm32g081kb = [ "stm32-metapac/stm32g081kb" ]
stm32g081rb = [ "stm32-metapac/stm32g081rb" ]
stm32g0b0ce = [ "stm32-metapac/stm32g0b0ce" ]
stm32g0b0ke = [ "stm32-metapac/stm32g0b0ke" ]
stm32g0b0re = [ "stm32-metapac/stm32g0b0re" ]
stm32g0b0ve = [ "stm32-metapac/stm32g0b0ve" ]
stm32g0b1cb = [ "stm32-metapac/stm32g0b1cb" ]
stm32g0b1cc = [ "stm32-metapac/stm32g0b1cc" ]
stm32g0b1ce = [ "stm32-metapac/stm32g0b1ce" ]
stm32g0b1kb = [ "stm32-metapac/stm32g0b1kb" ]
stm32g0b1kc = [ "stm32-metapac/stm32g0b1kc" ]
stm32g0b1ke = [ "stm32-metapac/stm32g0b1ke" ]
stm32g0b1mb = [ "stm32-metapac/stm32g0b1mb" ]
stm32g0b1mc = [ "stm32-metapac/stm32g0b1mc" ]
stm32g0b1me = [ "stm32-metapac/stm32g0b1me" ]
stm32g0b1ne = [ "stm32-metapac/stm32g0b1ne" ]
stm32g0b1rb = [ "stm32-metapac/stm32g0b1rb" ]
stm32g0b1rc = [ "stm32-metapac/stm32g0b1rc" ]
stm32g0b1re = [ "stm32-metapac/stm32g0b1re" ]
stm32g0b1vb = [ "stm32-metapac/stm32g0b1vb" ]
stm32g0b1vc = [ "stm32-metapac/stm32g0b1vc" ]
stm32g0b1ve = [ "stm32-metapac/stm32g0b1ve" ]
stm32g0c1cc = [ "stm32-metapac/stm32g0c1cc" ]
stm32g0c1ce = [ "stm32-metapac/stm32g0c1ce" ]
stm32g0c1kc = [ "stm32-metapac/stm32g0c1kc" ]
stm32g0c1ke = [ "stm32-metapac/stm32g0c1ke" ]
stm32g0c1mc = [ "stm32-metapac/stm32g0c1mc" ]
stm32g0c1me = [ "stm32-metapac/stm32g0c1me" ]
stm32g0c1ne = [ "stm32-metapac/stm32g0c1ne" ]
stm32g0c1rc = [ "stm32-metapac/stm32g0c1rc" ]
stm32g0c1re = [ "stm32-metapac/stm32g0c1re" ]
stm32g0c1vc = [ "stm32-metapac/stm32g0c1vc" ]
stm32g0c1ve = [ "stm32-metapac/stm32g0c1ve" ]
stm32g431c6 = [ "stm32-metapac/stm32g431c6" ]
stm32g431c8 = [ "stm32-metapac/stm32g431c8" ]
stm32g431cb = [ "stm32-metapac/stm32g431cb" ]
stm32g431k6 = [ "stm32-metapac/stm32g431k6" ]
stm32g431k8 = [ "stm32-metapac/stm32g431k8" ]
stm32g431kb = [ "stm32-metapac/stm32g431kb" ]
stm32g431m6 = [ "stm32-metapac/stm32g431m6" ]
stm32g431m8 = [ "stm32-metapac/stm32g431m8" ]
stm32g431mb = [ "stm32-metapac/stm32g431mb" ]
stm32g431r6 = [ "stm32-metapac/stm32g431r6" ]
stm32g431r8 = [ "stm32-metapac/stm32g431r8" ]
stm32g431rb = [ "stm32-metapac/stm32g431rb" ]
stm32g431v6 = [ "stm32-metapac/stm32g431v6" ]
stm32g431v8 = [ "stm32-metapac/stm32g431v8" ]
stm32g431vb = [ "stm32-metapac/stm32g431vb" ]
stm32g441cb = [ "stm32-metapac/stm32g441cb" ]
stm32g441kb = [ "stm32-metapac/stm32g441kb" ]
stm32g441mb = [ "stm32-metapac/stm32g441mb" ]
stm32g441rb = [ "stm32-metapac/stm32g441rb" ]
stm32g441vb = [ "stm32-metapac/stm32g441vb" ]
stm32g471cc = [ "stm32-metapac/stm32g471cc" ]
stm32g471ce = [ "stm32-metapac/stm32g471ce" ]
stm32g471mc = [ "stm32-metapac/stm32g471mc" ]
stm32g471me = [ "stm32-metapac/stm32g471me" ]
stm32g471qc = [ "stm32-metapac/stm32g471qc" ]
stm32g471qe = [ "stm32-metapac/stm32g471qe" ]
stm32g471rc = [ "stm32-metapac/stm32g471rc" ]
stm32g471re = [ "stm32-metapac/stm32g471re" ]
stm32g471vc = [ "stm32-metapac/stm32g471vc" ]
stm32g471ve = [ "stm32-metapac/stm32g471ve" ]
stm32g473cb = [ "stm32-metapac/stm32g473cb" ]
stm32g473cc = [ "stm32-metapac/stm32g473cc" ]
stm32g473ce = [ "stm32-metapac/stm32g473ce" ]
stm32g473mb = [ "stm32-metapac/stm32g473mb" ]
stm32g473mc = [ "stm32-metapac/stm32g473mc" ]
stm32g473me = [ "stm32-metapac/stm32g473me" ]
stm32g473pb = [ "stm32-metapac/stm32g473pb" ]
stm32g473pc = [ "stm32-metapac/stm32g473pc" ]
stm32g473pe = [ "stm32-metapac/stm32g473pe" ]
stm32g473qb = [ "stm32-metapac/stm32g473qb" ]
stm32g473qc = [ "stm32-metapac/stm32g473qc" ]
stm32g473qe = [ "stm32-metapac/stm32g473qe" ]
stm32g473rb = [ "stm32-metapac/stm32g473rb" ]
stm32g473rc = [ "stm32-metapac/stm32g473rc" ]
stm32g473re = [ "stm32-metapac/stm32g473re" ]
stm32g473vb = [ "stm32-metapac/stm32g473vb" ]
stm32g473vc = [ "stm32-metapac/stm32g473vc" ]
stm32g473ve = [ "stm32-metapac/stm32g473ve" ]
stm32g474cb = [ "stm32-metapac/stm32g474cb" ]
stm32g474cc = [ "stm32-metapac/stm32g474cc" ]
stm32g474ce = [ "stm32-metapac/stm32g474ce" ]
stm32g474mb = [ "stm32-metapac/stm32g474mb" ]
stm32g474mc = [ "stm32-metapac/stm32g474mc" ]
stm32g474me = [ "stm32-metapac/stm32g474me" ]
stm32g474pb = [ "stm32-metapac/stm32g474pb" ]
stm32g474pc = [ "stm32-metapac/stm32g474pc" ]
stm32g474pe = [ "stm32-metapac/stm32g474pe" ]
stm32g474qb = [ "stm32-metapac/stm32g474qb" ]
stm32g474qc = [ "stm32-metapac/stm32g474qc" ]
stm32g474qe = [ "stm32-metapac/stm32g474qe" ]
stm32g474rb = [ "stm32-metapac/stm32g474rb" ]
stm32g474rc = [ "stm32-metapac/stm32g474rc" ]
stm32g474re = [ "stm32-metapac/stm32g474re" ]
stm32g474vb = [ "stm32-metapac/stm32g474vb" ]
stm32g474vc = [ "stm32-metapac/stm32g474vc" ]
stm32g474ve = [ "stm32-metapac/stm32g474ve" ]
stm32g483ce = [ "stm32-metapac/stm32g483ce" ]
stm32g483me = [ "stm32-metapac/stm32g483me" ]
stm32g483pe = [ "stm32-metapac/stm32g483pe" ]
stm32g483qe = [ "stm32-metapac/stm32g483qe" ]
stm32g483re = [ "stm32-metapac/stm32g483re" ]
stm32g483ve = [ "stm32-metapac/stm32g483ve" ]
stm32g484ce = [ "stm32-metapac/stm32g484ce" ]
stm32g484me = [ "stm32-metapac/stm32g484me" ]
stm32g484pe = [ "stm32-metapac/stm32g484pe" ]
stm32g484qe = [ "stm32-metapac/stm32g484qe" ]
stm32g484re = [ "stm32-metapac/stm32g484re" ]
stm32g484ve = [ "stm32-metapac/stm32g484ve" ]
stm32g491cc = [ "stm32-metapac/stm32g491cc" ]
stm32g491ce = [ "stm32-metapac/stm32g491ce" ]
stm32g491kc = [ "stm32-metapac/stm32g491kc" ]
stm32g491ke = [ "stm32-metapac/stm32g491ke" ]
stm32g491mc = [ "stm32-metapac/stm32g491mc" ]
stm32g491me = [ "stm32-metapac/stm32g491me" ]
stm32g491rc = [ "stm32-metapac/stm32g491rc" ]
stm32g491re = [ "stm32-metapac/stm32g491re" ]
stm32g491vc = [ "stm32-metapac/stm32g491vc" ]
stm32g491ve = [ "stm32-metapac/stm32g491ve" ]
stm32g4a1ce = [ "stm32-metapac/stm32g4a1ce" ]
stm32g4a1ke = [ "stm32-metapac/stm32g4a1ke" ]
stm32g4a1me = [ "stm32-metapac/stm32g4a1me" ]
stm32g4a1re = [ "stm32-metapac/stm32g4a1re" ]
stm32g4a1ve = [ "stm32-metapac/stm32g4a1ve" ]
stm32h503cb = [ "stm32-metapac/stm32h503cb" ]
stm32h503eb = [ "stm32-metapac/stm32h503eb" ]
stm32h503kb = [ "stm32-metapac/stm32h503kb" ]
stm32h503rb = [ "stm32-metapac/stm32h503rb" ]
stm32h523cc = [ "stm32-metapac/stm32h523cc" ]
stm32h523ce = [ "stm32-metapac/stm32h523ce" ]
stm32h523he = [ "stm32-metapac/stm32h523he" ]
stm32h523rc = [ "stm32-metapac/stm32h523rc" ]
stm32h523re = [ "stm32-metapac/stm32h523re" ]
stm32h523vc = [ "stm32-metapac/stm32h523vc" ]
stm32h523ve = [ "stm32-metapac/stm32h523ve" ]
stm32h523zc = [ "stm32-metapac/stm32h523zc" ]
stm32h523ze = [ "stm32-metapac/stm32h523ze" ]
stm32h533ce = [ "stm32-metapac/stm32h533ce" ]
stm32h533he = [ "stm32-metapac/stm32h533he" ]
stm32h533re = [ "stm32-metapac/stm32h533re" ]
stm32h533ve = [ "stm32-metapac/stm32h533ve" ]
stm32h533ze = [ "stm32-metapac/stm32h533ze" ]
stm32h562ag = [ "stm32-metapac/stm32h562ag" ]
stm32h562ai = [ "stm32-metapac/stm32h562ai" ]
stm32h562ig = [ "stm32-metapac/stm32h562ig" ]
stm32h562ii = [ "stm32-metapac/stm32h562ii" ]
stm32h562rg = [ "stm32-metapac/stm32h562rg" ]
stm32h562ri = [ "stm32-metapac/stm32h562ri" ]
stm32h562vg = [ "stm32-metapac/stm32h562vg" ]
stm32h562vi = [ "stm32-metapac/stm32h562vi" ]
stm32h562zg = [ "stm32-metapac/stm32h562zg" ]
stm32h562zi = [ "stm32-metapac/stm32h562zi" ]
stm32h563ag = [ "stm32-metapac/stm32h563ag" ]
stm32h563ai = [ "stm32-metapac/stm32h563ai" ]
stm32h563ig = [ "stm32-metapac/stm32h563ig" ]
stm32h563ii = [ "stm32-metapac/stm32h563ii" ]
stm32h563mi = [ "stm32-metapac/stm32h563mi" ]
stm32h563rg = [ "stm32-metapac/stm32h563rg" ]
stm32h563ri = [ "stm32-metapac/stm32h563ri" ]
stm32h563vg = [ "stm32-metapac/stm32h563vg" ]
stm32h563vi = [ "stm32-metapac/stm32h563vi" ]
stm32h563zg = [ "stm32-metapac/stm32h563zg" ]
stm32h563zi = [ "stm32-metapac/stm32h563zi" ]
stm32h573ai = [ "stm32-metapac/stm32h573ai" ]
stm32h573ii = [ "stm32-metapac/stm32h573ii" ]
stm32h573mi = [ "stm32-metapac/stm32h573mi" ]
stm32h573ri = [ "stm32-metapac/stm32h573ri" ]
stm32h573vi = [ "stm32-metapac/stm32h573vi" ]
stm32h573zi = [ "stm32-metapac/stm32h573zi" ]
stm32h723ve = [ "stm32-metapac/stm32h723ve" ]
stm32h723vg = [ "stm32-metapac/stm32h723vg" ]
stm32h723ze = [ "stm32-metapac/stm32h723ze" ]
stm32h723zg = [ "stm32-metapac/stm32h723zg" ]
stm32h725ae = [ "stm32-metapac/stm32h725ae" ]
stm32h725ag = [ "stm32-metapac/stm32h725ag" ]
stm32h725ie = [ "stm32-metapac/stm32h725ie" ]
stm32h725ig = [ "stm32-metapac/stm32h725ig" ]
stm32h725re = [ "stm32-metapac/stm32h725re" ]
stm32h725rg = [ "stm32-metapac/stm32h725rg" ]
stm32h725ve = [ "stm32-metapac/stm32h725ve" ]
stm32h725vg = [ "stm32-metapac/stm32h725vg" ]
stm32h725ze = [ "stm32-metapac/stm32h725ze" ]
stm32h725zg = [ "stm32-metapac/stm32h725zg" ]
stm32h730ab = [ "stm32-metapac/stm32h730ab" ]
stm32h730ib = [ "stm32-metapac/stm32h730ib" ]
stm32h730vb = [ "stm32-metapac/stm32h730vb" ]
stm32h730zb = [ "stm32-metapac/stm32h730zb" ]
stm32h733vg = [ "stm32-metapac/stm32h733vg" ]
stm32h733zg = [ "stm32-metapac/stm32h733zg" ]
stm32h735ag = [ "stm32-metapac/stm32h735ag" ]
stm32h735ig = [ "stm32-metapac/stm32h735ig" ]
stm32h735rg = [ "stm32-metapac/stm32h735rg" ]
stm32h735vg = [ "stm32-metapac/stm32h735vg" ]
stm32h735zg = [ "stm32-metapac/stm32h735zg" ]
stm32h742ag = [ "stm32-metapac/stm32h742ag" ]
stm32h742ai = [ "stm32-metapac/stm32h742ai" ]
stm32h742bg = [ "stm32-metapac/stm32h742bg" ]
stm32h742bi = [ "stm32-metapac/stm32h742bi" ]
stm32h742ig = [ "stm32-metapac/stm32h742ig" ]
stm32h742ii = [ "stm32-metapac/stm32h742ii" ]
stm32h742vg = [ "stm32-metapac/stm32h742vg" ]
stm32h742vi = [ "stm32-metapac/stm32h742vi" ]
stm32h742xg = [ "stm32-metapac/stm32h742xg" ]
stm32h742xi = [ "stm32-metapac/stm32h742xi" ]
stm32h742zg = [ "stm32-metapac/stm32h742zg" ]
stm32h742zi = [ "stm32-metapac/stm32h742zi" ]
stm32h743ag = [ "stm32-metapac/stm32h743ag" ]
stm32h743ai = [ "stm32-metapac/stm32h743ai" ]
stm32h743bg = [ "stm32-metapac/stm32h743bg" ]
stm32h743bi = [ "stm32-metapac/stm32h743bi" ]
stm32h743ig = [ "stm32-metapac/stm32h743ig" ]
stm32h743ii = [ "stm32-metapac/stm32h743ii" ]
stm32h743vg = [ "stm32-metapac/stm32h743vg" ]
stm32h743vi = [ "stm32-metapac/stm32h743vi" ]
stm32h743xg = [ "stm32-metapac/stm32h743xg" ]
stm32h743xi = [ "stm32-metapac/stm32h743xi" ]
stm32h743zg = [ "stm32-metapac/stm32h743zg" ]
stm32h743zi = [ "stm32-metapac/stm32h743zi" ]
stm32h745bg-cm7 = [ "stm32-metapac/stm32h745bg-cm7", "_dual-core", "_core-cm7" ]
stm32h745bg-cm4 = [ "stm32-metapac/stm32h745bg-cm4", "_dual-core", "_core-cm4" ]
stm32h745bi-cm7 = [ "stm32-metapac/stm32h745bi-cm7", "_dual-core", "_core-cm7" ]
stm32h745bi-cm4 = [ "stm32-metapac/stm32h745bi-cm4", "_dual-core", "_core-cm4" ]
stm32h745ig-cm7 = [ "stm32-metapac/stm32h745ig-cm7", "_dual-core", "_core-cm7" ]
stm32h745ig-cm4 = [ "stm32-metapac/stm32h745ig-cm4", "_dual-core", "_core-cm4" ]
stm32h745ii-cm7 = [ "stm32-metapac/stm32h745ii-cm7", "_dual-core", "_core-cm7" ]
stm32h745ii-cm4 = [ "stm32-metapac/stm32h745ii-cm4", "_dual-core", "_core-cm4" ]
stm32h745xg-cm7 = [ "stm32-metapac/stm32h745xg-cm7", "_dual-core", "_core-cm7" ]
stm32h745xg-cm4 = [ "stm32-metapac/stm32h745xg-cm4", "_dual-core", "_core-cm4" ]
stm32h745xi-cm7 = [ "stm32-metapac/stm32h745xi-cm7", "_dual-core", "_core-cm7" ]
stm32h745xi-cm4 = [ "stm32-metapac/stm32h745xi-cm4", "_dual-core", "_core-cm4" ]
stm32h745zg-cm7 = [ "stm32-metapac/stm32h745zg-cm7", "_dual-core", "_core-cm7" ]
stm32h745zg-cm4 = [ "stm32-metapac/stm32h745zg-cm4", "_dual-core", "_core-cm4" ]
stm32h745zi-cm7 = [ "stm32-metapac/stm32h745zi-cm7", "_dual-core", "_core-cm7" ]
stm32h745zi-cm4 = [ "stm32-metapac/stm32h745zi-cm4", "_dual-core", "_core-cm4" ]
stm32h747ag-cm7 = [ "stm32-metapac/stm32h747ag-cm7", "_dual-core", "_core-cm7" ]
stm32h747ag-cm4 = [ "stm32-metapac/stm32h747ag-cm4", "_dual-core", "_core-cm4" ]
stm32h747ai-cm7 = [ "stm32-metapac/stm32h747ai-cm7", "_dual-core", "_core-cm7" ]
stm32h747ai-cm4 = [ "stm32-metapac/stm32h747ai-cm4", "_dual-core", "_core-cm4" ]
stm32h747bg-cm7 = [ "stm32-metapac/stm32h747bg-cm7", "_dual-core", "_core-cm7" ]
stm32h747bg-cm4 = [ "stm32-metapac/stm32h747bg-cm4", "_dual-core", "_core-cm4" ]
stm32h747bi-cm7 = [ "stm32-metapac/stm32h747bi-cm7", "_dual-core", "_core-cm7" ]
stm32h747bi-cm4 = [ "stm32-metapac/stm32h747bi-cm4", "_dual-core", "_core-cm4" ]
stm32h747ig-cm7 = [ "stm32-metapac/stm32h747ig-cm7", "_dual-core", "_core-cm7" ]
stm32h747ig-cm4 = [ "stm32-metapac/stm32h747ig-cm4", "_dual-core", "_core-cm4" ]
stm32h747ii-cm7 = [ "stm32-metapac/stm32h747ii-cm7", "_dual-core", "_core-cm7" ]
stm32h747ii-cm4 = [ "stm32-metapac/stm32h747ii-cm4", "_dual-core", "_core-cm4" ]
stm32h747xg-cm7 = [ "stm32-metapac/stm32h747xg-cm7", "_dual-core", "_core-cm7" ]
stm32h747xg-cm4 = [ "stm32-metapac/stm32h747xg-cm4", "_dual-core", "_core-cm4" ]
stm32h747xi-cm7 = [ "stm32-metapac/stm32h747xi-cm7", "_dual-core", "_core-cm7" ]
stm32h747xi-cm4 = [ "stm32-metapac/stm32h747xi-cm4", "_dual-core", "_core-cm4" ]
stm32h747zi-cm7 = [ "stm32-metapac/stm32h747zi-cm7", "_dual-core", "_core-cm7" ]
stm32h747zi-cm4 = [ "stm32-metapac/stm32h747zi-cm4", "_dual-core", "_core-cm4" ]
stm32h750ib = [ "stm32-metapac/stm32h750ib" ]
stm32h750vb = [ "stm32-metapac/stm32h750vb" ]
stm32h750xb = [ "stm32-metapac/stm32h750xb" ]
stm32h750zb = [ "stm32-metapac/stm32h750zb" ]
stm32h753ai = [ "stm32-metapac/stm32h753ai" ]
stm32h753bi = [ "stm32-metapac/stm32h753bi" ]
stm32h753ii = [ "stm32-metapac/stm32h753ii" ]
stm32h753vi = [ "stm32-metapac/stm32h753vi" ]
stm32h753xi = [ "stm32-metapac/stm32h753xi" ]
stm32h753zi = [ "stm32-metapac/stm32h753zi" ]
stm32h755bi-cm7 = [ "stm32-metapac/stm32h755bi-cm7", "_dual-core", "_core-cm7" ]
stm32h755bi-cm4 = [ "stm32-metapac/stm32h755bi-cm4", "_dual-core", "_core-cm4" ]
stm32h755ii-cm7 = [ "stm32-metapac/stm32h755ii-cm7", "_dual-core", "_core-cm7" ]
stm32h755ii-cm4 = [ "stm32-metapac/stm32h755ii-cm4", "_dual-core", "_core-cm4" ]
stm32h755xi-cm7 = [ "stm32-metapac/stm32h755xi-cm7", "_dual-core", "_core-cm7" ]
stm32h755xi-cm4 = [ "stm32-metapac/stm32h755xi-cm4", "_dual-core", "_core-cm4" ]
stm32h755zi-cm7 = [ "stm32-metapac/stm32h755zi-cm7", "_dual-core", "_core-cm7" ]
stm32h755zi-cm4 = [ "stm32-metapac/stm32h755zi-cm4", "_dual-core", "_core-cm4" ]
stm32h757ai-cm7 = [ "stm32-metapac/stm32h757ai-cm7", "_dual-core", "_core-cm7" ]
stm32h757ai-cm4 = [ "stm32-metapac/stm32h757ai-cm4", "_dual-core", "_core-cm4" ]
stm32h757bi-cm7 = [ "stm32-metapac/stm32h757bi-cm7", "_dual-core", "_core-cm7" ]
stm32h757bi-cm4 = [ "stm32-metapac/stm32h757bi-cm4", "_dual-core", "_core-cm4" ]
stm32h757ii-cm7 = [ "stm32-metapac/stm32h757ii-cm7", "_dual-core", "_core-cm7" ]
stm32h757ii-cm4 = [ "stm32-metapac/stm32h757ii-cm4", "_dual-core", "_core-cm4" ]
stm32h757xi-cm7 = [ "stm32-metapac/stm32h757xi-cm7", "_dual-core", "_core-cm7" ]
stm32h757xi-cm4 = [ "stm32-metapac/stm32h757xi-cm4", "_dual-core", "_core-cm4" ]
stm32h757zi-cm7 = [ "stm32-metapac/stm32h757zi-cm7", "_dual-core", "_core-cm7" ]
stm32h757zi-cm4 = [ "stm32-metapac/stm32h757zi-cm4", "_dual-core", "_core-cm4" ]
stm32h7a3ag = [ "stm32-metapac/stm32h7a3ag" ]
stm32h7a3ai = [ "stm32-metapac/stm32h7a3ai" ]
stm32h7a3ig = [ "stm32-metapac/stm32h7a3ig" ]
stm32h7a3ii = [ "stm32-metapac/stm32h7a3ii" ]
stm32h7a3lg = [ "stm32-metapac/stm32h7a3lg" ]
stm32h7a3li = [ "stm32-metapac/stm32h7a3li" ]
stm32h7a3ng = [ "stm32-metapac/stm32h7a3ng" ]
stm32h7a3ni = [ "stm32-metapac/stm32h7a3ni" ]
stm32h7a3qi = [ "stm32-metapac/stm32h7a3qi" ]
stm32h7a3rg = [ "stm32-metapac/stm32h7a3rg" ]
stm32h7a3ri = [ "stm32-metapac/stm32h7a3ri" ]
stm32h7a3vg = [ "stm32-metapac/stm32h7a3vg" ]
stm32h7a3vi = [ "stm32-metapac/stm32h7a3vi" ]
stm32h7a3zg = [ "stm32-metapac/stm32h7a3zg" ]
stm32h7a3zi = [ "stm32-metapac/stm32h7a3zi" ]
stm32h7b0ab = [ "stm32-metapac/stm32h7b0ab" ]
stm32h7b0ib = [ "stm32-metapac/stm32h7b0ib" ]
stm32h7b0rb = [ "stm32-metapac/stm32h7b0rb" ]
stm32h7b0vb = [ "stm32-metapac/stm32h7b0vb" ]
stm32h7b0zb = [ "stm32-metapac/stm32h7b0zb" ]
stm32h7b3ai = [ "stm32-metapac/stm32h7b3ai" ]
stm32h7b3ii = [ "stm32-metapac/stm32h7b3ii" ]
stm32h7b3li = [ "stm32-metapac/stm32h7b3li" ]
stm32h7b3ni = [ "stm32-metapac/stm32h7b3ni" ]
stm32h7b3qi = [ "stm32-metapac/stm32h7b3qi" ]
stm32h7b3ri = [ "stm32-metapac/stm32h7b3ri" ]
stm32h7b3vi = [ "stm32-metapac/stm32h7b3vi" ]
stm32h7b3zi = [ "stm32-metapac/stm32h7b3zi" ]
stm32h7r3a8 = [ "stm32-metapac/stm32h7r3a8" ]
stm32h7r3i8 = [ "stm32-metapac/stm32h7r3i8" ]
stm32h7r3l8 = [ "stm32-metapac/stm32h7r3l8" ]
stm32h7r3r8 = [ "stm32-metapac/stm32h7r3r8" ]
stm32h7r3v8 = [ "stm32-metapac/stm32h7r3v8" ]
stm32h7r3z8 = [ "stm32-metapac/stm32h7r3z8" ]
stm32h7r7a8 = [ "stm32-metapac/stm32h7r7a8" ]
stm32h7r7i8 = [ "stm32-metapac/stm32h7r7i8" ]
stm32h7r7l8 = [ "stm32-metapac/stm32h7r7l8" ]
stm32h7r7z8 = [ "stm32-metapac/stm32h7r7z8" ]
stm32h7s3a8 = [ "stm32-metapac/stm32h7s3a8" ]
stm32h7s3i8 = [ "stm32-metapac/stm32h7s3i8" ]
stm32h7s3l8 = [ "stm32-metapac/stm32h7s3l8" ]
stm32h7s3r8 = [ "stm32-metapac/stm32h7s3r8" ]
stm32h7s3v8 = [ "stm32-metapac/stm32h7s3v8" ]
stm32h7s3z8 = [ "stm32-metapac/stm32h7s3z8" ]
stm32h7s7a8 = [ "stm32-metapac/stm32h7s7a8" ]
stm32h7s7i8 = [ "stm32-metapac/stm32h7s7i8" ]
stm32h7s7l8 = [ "stm32-metapac/stm32h7s7l8" ]
stm32h7s7z8 = [ "stm32-metapac/stm32h7s7z8" ]
stm32l010c6 = [ "stm32-metapac/stm32l010c6" ]
stm32l010f4 = [ "stm32-metapac/stm32l010f4" ]
stm32l010k4 = [ "stm32-metapac/stm32l010k4" ]
stm32l010k8 = [ "stm32-metapac/stm32l010k8" ]
stm32l010r8 = [ "stm32-metapac/stm32l010r8" ]
stm32l010rb = [ "stm32-metapac/stm32l010rb" ]
stm32l011d3 = [ "stm32-metapac/stm32l011d3" ]
stm32l011d4 = [ "stm32-metapac/stm32l011d4" ]
stm32l011e3 = [ "stm32-metapac/stm32l011e3" ]
stm32l011e4 = [ "stm32-metapac/stm32l011e4" ]
stm32l011f3 = [ "stm32-metapac/stm32l011f3" ]
stm32l011f4 = [ "stm32-metapac/stm32l011f4" ]
stm32l011g3 = [ "stm32-metapac/stm32l011g3" ]
stm32l011g4 = [ "stm32-metapac/stm32l011g4" ]
stm32l011k3 = [ "stm32-metapac/stm32l011k3" ]
stm32l011k4 = [ "stm32-metapac/stm32l011k4" ]
stm32l021d4 = [ "stm32-metapac/stm32l021d4" ]
stm32l021f4 = [ "stm32-metapac/stm32l021f4" ]
stm32l021g4 = [ "stm32-metapac/stm32l021g4" ]
stm32l021k4 = [ "stm32-metapac/stm32l021k4" ]
stm32l031c4 = [ "stm32-metapac/stm32l031c4" ]
stm32l031c6 = [ "stm32-metapac/stm32l031c6" ]
stm32l031e4 = [ "stm32-metapac/stm32l031e4" ]
stm32l031e6 = [ "stm32-metapac/stm32l031e6" ]
stm32l031f4 = [ "stm32-metapac/stm32l031f4" ]
stm32l031f6 = [ "stm32-metapac/stm32l031f6" ]
stm32l031g4 = [ "stm32-metapac/stm32l031g4" ]
stm32l031g6 = [ "stm32-metapac/stm32l031g6" ]
stm32l031k4 = [ "stm32-metapac/stm32l031k4" ]
stm32l031k6 = [ "stm32-metapac/stm32l031k6" ]
stm32l041c4 = [ "stm32-metapac/stm32l041c4" ]
stm32l041c6 = [ "stm32-metapac/stm32l041c6" ]
stm32l041e6 = [ "stm32-metapac/stm32l041e6" ]
stm32l041f6 = [ "stm32-metapac/stm32l041f6" ]
stm32l041g6 = [ "stm32-metapac/stm32l041g6" ]
stm32l041k6 = [ "stm32-metapac/stm32l041k6" ]
stm32l051c6 = [ "stm32-metapac/stm32l051c6" ]
stm32l051c8 = [ "stm32-metapac/stm32l051c8" ]
stm32l051k6 = [ "stm32-metapac/stm32l051k6" ]
stm32l051k8 = [ "stm32-metapac/stm32l051k8" ]
stm32l051r6 = [ "stm32-metapac/stm32l051r6" ]
stm32l051r8 = [ "stm32-metapac/stm32l051r8" ]
stm32l051t6 = [ "stm32-metapac/stm32l051t6" ]
stm32l051t8 = [ "stm32-metapac/stm32l051t8" ]
stm32l052c6 = [ "stm32-metapac/stm32l052c6" ]
stm32l052c8 = [ "stm32-metapac/stm32l052c8" ]
stm32l052k6 = [ "stm32-metapac/stm32l052k6" ]
stm32l052k8 = [ "stm32-metapac/stm32l052k8" ]
stm32l052r6 = [ "stm32-metapac/stm32l052r6" ]
stm32l052r8 = [ "stm32-metapac/stm32l052r8" ]
stm32l052t6 = [ "stm32-metapac/stm32l052t6" ]
stm32l052t8 = [ "stm32-metapac/stm32l052t8" ]
stm32l053c6 = [ "stm32-metapac/stm32l053c6" ]
stm32l053c8 = [ "stm32-metapac/stm32l053c8" ]
stm32l053r6 = [ "stm32-metapac/stm32l053r6" ]
stm32l053r8 = [ "stm32-metapac/stm32l053r8" ]
stm32l062c8 = [ "stm32-metapac/stm32l062c8" ]
stm32l062k8 = [ "stm32-metapac/stm32l062k8" ]
stm32l063c8 = [ "stm32-metapac/stm32l063c8" ]
stm32l063r8 = [ "stm32-metapac/stm32l063r8" ]
stm32l071c8 = [ "stm32-metapac/stm32l071c8" ]
stm32l071cb = [ "stm32-metapac/stm32l071cb" ]
stm32l071cz = [ "stm32-metapac/stm32l071cz" ]
stm32l071k8 = [ "stm32-metapac/stm32l071k8" ]
stm32l071kb = [ "stm32-metapac/stm32l071kb" ]
stm32l071kz = [ "stm32-metapac/stm32l071kz" ]
stm32l071rb = [ "stm32-metapac/stm32l071rb" ]
stm32l071rz = [ "stm32-metapac/stm32l071rz" ]
stm32l071v8 = [ "stm32-metapac/stm32l071v8" ]
stm32l071vb = [ "stm32-metapac/stm32l071vb" ]
stm32l071vz = [ "stm32-metapac/stm32l071vz" ]
stm32l072cb = [ "stm32-metapac/stm32l072cb" ]
stm32l072cz = [ "stm32-metapac/stm32l072cz" ]
stm32l072kb = [ "stm32-metapac/stm32l072kb" ]
stm32l072kz = [ "stm32-metapac/stm32l072kz" ]
stm32l072rb = [ "stm32-metapac/stm32l072rb" ]
stm32l072rz = [ "stm32-metapac/stm32l072rz" ]
stm32l072v8 = [ "stm32-metapac/stm32l072v8" ]
stm32l072vb = [ "stm32-metapac/stm32l072vb" ]
stm32l072vz = [ "stm32-metapac/stm32l072vz" ]
stm32l073cb = [ "stm32-metapac/stm32l073cb" ]
stm32l073cz = [ "stm32-metapac/stm32l073cz" ]
stm32l073rb = [ "stm32-metapac/stm32l073rb" ]
stm32l073rz = [ "stm32-metapac/stm32l073rz" ]
stm32l073v8 = [ "stm32-metapac/stm32l073v8" ]
stm32l073vb = [ "stm32-metapac/stm32l073vb" ]
stm32l073vz = [ "stm32-metapac/stm32l073vz" ]
stm32l081cb = [ "stm32-metapac/stm32l081cb" ]
stm32l081cz = [ "stm32-metapac/stm32l081cz" ]
stm32l081kz = [ "stm32-metapac/stm32l081kz" ]
stm32l082cz = [ "stm32-metapac/stm32l082cz" ]
stm32l082kb = [ "stm32-metapac/stm32l082kb" ]
stm32l082kz = [ "stm32-metapac/stm32l082kz" ]
stm32l083cb = [ "stm32-metapac/stm32l083cb" ]
stm32l083cz = [ "stm32-metapac/stm32l083cz" ]
stm32l083rb = [ "stm32-metapac/stm32l083rb" ]
stm32l083rz = [ "stm32-metapac/stm32l083rz" ]
stm32l083v8 = [ "stm32-metapac/stm32l083v8" ]
stm32l083vb = [ "stm32-metapac/stm32l083vb" ]
stm32l083vz = [ "stm32-metapac/stm32l083vz" ]
stm32l100c6 = [ "stm32-metapac/stm32l100c6" ]
stm32l100c6-a = [ "stm32-metapac/stm32l100c6-a" ]
stm32l100r8 = [ "stm32-metapac/stm32l100r8" ]
stm32l100r8-a = [ "stm32-metapac/stm32l100r8-a" ]
stm32l100rb = [ "stm32-metapac/stm32l100rb" ]
stm32l100rb-a = [ "stm32-metapac/stm32l100rb-a" ]
stm32l100rc = [ "stm32-metapac/stm32l100rc" ]
stm32l151c6 = [ "stm32-metapac/stm32l151c6" ]
stm32l151c6-a = [ "stm32-metapac/stm32l151c6-a" ]
stm32l151c8 = [ "stm32-metapac/stm32l151c8" ]
stm32l151c8-a = [ "stm32-metapac/stm32l151c8-a" ]
stm32l151cb = [ "stm32-metapac/stm32l151cb" ]
stm32l151cb-a = [ "stm32-metapac/stm32l151cb-a" ]
stm32l151cc = [ "stm32-metapac/stm32l151cc" ]
stm32l151qc = [ "stm32-metapac/stm32l151qc" ]
stm32l151qd = [ "stm32-metapac/stm32l151qd" ]
stm32l151qe = [ "stm32-metapac/stm32l151qe" ]
stm32l151r6 = [ "stm32-metapac/stm32l151r6" ]
stm32l151r6-a = [ "stm32-metapac/stm32l151r6-a" ]
stm32l151r8 = [ "stm32-metapac/stm32l151r8" ]
stm32l151r8-a = [ "stm32-metapac/stm32l151r8-a" ]
stm32l151rb = [ "stm32-metapac/stm32l151rb" ]
stm32l151rb-a = [ "stm32-metapac/stm32l151rb-a" ]
stm32l151rc = [ "stm32-metapac/stm32l151rc" ]
stm32l151rc-a = [ "stm32-metapac/stm32l151rc-a" ]
stm32l151rd = [ "stm32-metapac/stm32l151rd" ]
stm32l151re = [ "stm32-metapac/stm32l151re" ]
stm32l151uc = [ "stm32-metapac/stm32l151uc" ]
stm32l151v8 = [ "stm32-metapac/stm32l151v8" ]
stm32l151v8-a = [ "stm32-metapac/stm32l151v8-a" ]
stm32l151vb = [ "stm32-metapac/stm32l151vb" ]
stm32l151vb-a = [ "stm32-metapac/stm32l151vb-a" ]
stm32l151vc = [ "stm32-metapac/stm32l151vc" ]
stm32l151vc-a = [ "stm32-metapac/stm32l151vc-a" ]
stm32l151vd = [ "stm32-metapac/stm32l151vd" ]
stm32l151vd-x = [ "stm32-metapac/stm32l151vd-x" ]
stm32l151ve = [ "stm32-metapac/stm32l151ve" ]
stm32l151zc = [ "stm32-metapac/stm32l151zc" ]
stm32l151zd = [ "stm32-metapac/stm32l151zd" ]
stm32l151ze = [ "stm32-metapac/stm32l151ze" ]
stm32l152c6 = [ "stm32-metapac/stm32l152c6" ]
stm32l152c6-a = [ "stm32-metapac/stm32l152c6-a" ]
stm32l152c8 = [ "stm32-metapac/stm32l152c8" ]
stm32l152c8-a = [ "stm32-metapac/stm32l152c8-a" ]
stm32l152cb = [ "stm32-metapac/stm32l152cb" ]
stm32l152cb-a = [ "stm32-metapac/stm32l152cb-a" ]
stm32l152cc = [ "stm32-metapac/stm32l152cc" ]
stm32l152qc = [ "stm32-metapac/stm32l152qc" ]
stm32l152qd = [ "stm32-metapac/stm32l152qd" ]
stm32l152qe = [ "stm32-metapac/stm32l152qe" ]
stm32l152r6 = [ "stm32-metapac/stm32l152r6" ]
stm32l152r6-a = [ "stm32-metapac/stm32l152r6-a" ]
stm32l152r8 = [ "stm32-metapac/stm32l152r8" ]
stm32l152r8-a = [ "stm32-metapac/stm32l152r8-a" ]
stm32l152rb = [ "stm32-metapac/stm32l152rb" ]
stm32l152rb-a = [ "stm32-metapac/stm32l152rb-a" ]
stm32l152rc = [ "stm32-metapac/stm32l152rc" ]
stm32l152rc-a = [ "stm32-metapac/stm32l152rc-a" ]
stm32l152rd = [ "stm32-metapac/stm32l152rd" ]
stm32l152re = [ "stm32-metapac/stm32l152re" ]
stm32l152uc = [ "stm32-metapac/stm32l152uc" ]
stm32l152v8 = [ "stm32-metapac/stm32l152v8" ]
stm32l152v8-a = [ "stm32-metapac/stm32l152v8-a" ]
stm32l152vb = [ "stm32-metapac/stm32l152vb" ]
stm32l152vb-a = [ "stm32-metapac/stm32l152vb-a" ]
stm32l152vc = [ "stm32-metapac/stm32l152vc" ]
stm32l152vc-a = [ "stm32-metapac/stm32l152vc-a" ]
stm32l152vd = [ "stm32-metapac/stm32l152vd" ]
stm32l152vd-x = [ "stm32-metapac/stm32l152vd-x" ]
stm32l152ve = [ "stm32-metapac/stm32l152ve" ]
stm32l152zc = [ "stm32-metapac/stm32l152zc" ]
stm32l152zd = [ "stm32-metapac/stm32l152zd" ]
stm32l152ze = [ "stm32-metapac/stm32l152ze" ]
stm32l162qc = [ "stm32-metapac/stm32l162qc" ]
stm32l162qd = [ "stm32-metapac/stm32l162qd" ]
stm32l162rc = [ "stm32-metapac/stm32l162rc" ]
stm32l162rc-a = [ "stm32-metapac/stm32l162rc-a" ]
stm32l162rd = [ "stm32-metapac/stm32l162rd" ]
stm32l162re = [ "stm32-metapac/stm32l162re" ]
stm32l162vc = [ "stm32-metapac/stm32l162vc" ]
stm32l162vc-a = [ "stm32-metapac/stm32l162vc-a" ]
stm32l162vd = [ "stm32-metapac/stm32l162vd" ]
stm32l162vd-x = [ "stm32-metapac/stm32l162vd-x" ]
stm32l162ve = [ "stm32-metapac/stm32l162ve" ]
stm32l162zc = [ "stm32-metapac/stm32l162zc" ]
stm32l162zd = [ "stm32-metapac/stm32l162zd" ]
stm32l162ze = [ "stm32-metapac/stm32l162ze" ]
stm32l412c8 = [ "stm32-metapac/stm32l412c8" ]
stm32l412cb = [ "stm32-metapac/stm32l412cb" ]
stm32l412k8 = [ "stm32-metapac/stm32l412k8" ]
stm32l412kb = [ "stm32-metapac/stm32l412kb" ]
stm32l412r8 = [ "stm32-metapac/stm32l412r8" ]
stm32l412rb = [ "stm32-metapac/stm32l412rb" ]
stm32l412t8 = [ "stm32-metapac/stm32l412t8" ]
stm32l412tb = [ "stm32-metapac/stm32l412tb" ]
stm32l422cb = [ "stm32-metapac/stm32l422cb" ]
stm32l422kb = [ "stm32-metapac/stm32l422kb" ]
stm32l422rb = [ "stm32-metapac/stm32l422rb" ]
stm32l422tb = [ "stm32-metapac/stm32l422tb" ]
stm32l431cb = [ "stm32-metapac/stm32l431cb" ]
stm32l431cc = [ "stm32-metapac/stm32l431cc" ]
stm32l431kb = [ "stm32-metapac/stm32l431kb" ]
stm32l431kc = [ "stm32-metapac/stm32l431kc" ]
stm32l431rb = [ "stm32-metapac/stm32l431rb" ]
stm32l431rc = [ "stm32-metapac/stm32l431rc" ]
stm32l431vc = [ "stm32-metapac/stm32l431vc" ]
stm32l432kb = [ "stm32-metapac/stm32l432kb" ]
stm32l432kc = [ "stm32-metapac/stm32l432kc" ]
stm32l433cb = [ "stm32-metapac/stm32l433cb" ]
stm32l433cc = [ "stm32-metapac/stm32l433cc" ]
stm32l433rb = [ "stm32-metapac/stm32l433rb" ]
stm32l433rc = [ "stm32-metapac/stm32l433rc" ]
stm32l433vc = [ "stm32-metapac/stm32l433vc" ]
stm32l442kc = [ "stm32-metapac/stm32l442kc" ]
stm32l443cc = [ "stm32-metapac/stm32l443cc" ]
stm32l443rc = [ "stm32-metapac/stm32l443rc" ]
stm32l443vc = [ "stm32-metapac/stm32l443vc" ]
stm32l451cc = [ "stm32-metapac/stm32l451cc" ]
stm32l451ce = [ "stm32-metapac/stm32l451ce" ]
stm32l451rc = [ "stm32-metapac/stm32l451rc" ]
stm32l451re = [ "stm32-metapac/stm32l451re" ]
stm32l451vc = [ "stm32-metapac/stm32l451vc" ]
stm32l451ve = [ "stm32-metapac/stm32l451ve" ]
stm32l452cc = [ "stm32-metapac/stm32l452cc" ]
stm32l452ce = [ "stm32-metapac/stm32l452ce" ]
stm32l452rc = [ "stm32-metapac/stm32l452rc" ]
stm32l452re = [ "stm32-metapac/stm32l452re" ]
stm32l452vc = [ "stm32-metapac/stm32l452vc" ]
stm32l452ve = [ "stm32-metapac/stm32l452ve" ]
stm32l462ce = [ "stm32-metapac/stm32l462ce" ]
stm32l462re = [ "stm32-metapac/stm32l462re" ]
stm32l462ve = [ "stm32-metapac/stm32l462ve" ]
stm32l471qe = [ "stm32-metapac/stm32l471qe" ]
stm32l471qg = [ "stm32-metapac/stm32l471qg" ]
stm32l471re = [ "stm32-metapac/stm32l471re" ]
stm32l471rg = [ "stm32-metapac/stm32l471rg" ]
stm32l471ve = [ "stm32-metapac/stm32l471ve" ]
stm32l471vg = [ "stm32-metapac/stm32l471vg" ]
stm32l471ze = [ "stm32-metapac/stm32l471ze" ]
stm32l471zg = [ "stm32-metapac/stm32l471zg" ]
stm32l475rc = [ "stm32-metapac/stm32l475rc" ]
stm32l475re = [ "stm32-metapac/stm32l475re" ]
stm32l475rg = [ "stm32-metapac/stm32l475rg" ]
stm32l475vc = [ "stm32-metapac/stm32l475vc" ]
stm32l475ve = [ "stm32-metapac/stm32l475ve" ]
stm32l475vg = [ "stm32-metapac/stm32l475vg" ]
stm32l476je = [ "stm32-metapac/stm32l476je" ]
stm32l476jg = [ "stm32-metapac/stm32l476jg" ]
stm32l476me = [ "stm32-metapac/stm32l476me" ]
stm32l476mg = [ "stm32-metapac/stm32l476mg" ]
stm32l476qe = [ "stm32-metapac/stm32l476qe" ]
stm32l476qg = [ "stm32-metapac/stm32l476qg" ]
stm32l476rc = [ "stm32-metapac/stm32l476rc" ]
stm32l476re = [ "stm32-metapac/stm32l476re" ]
stm32l476rg = [ "stm32-metapac/stm32l476rg" ]
stm32l476vc = [ "stm32-metapac/stm32l476vc" ]
stm32l476ve = [ "stm32-metapac/stm32l476ve" ]
stm32l476vg = [ "stm32-metapac/stm32l476vg" ]
stm32l476ze = [ "stm32-metapac/stm32l476ze" ]
stm32l476zg = [ "stm32-metapac/stm32l476zg" ]
stm32l486jg = [ "stm32-metapac/stm32l486jg" ]
stm32l486qg = [ "stm32-metapac/stm32l486qg" ]
stm32l486rg = [ "stm32-metapac/stm32l486rg" ]
stm32l486vg = [ "stm32-metapac/stm32l486vg" ]
stm32l486zg = [ "stm32-metapac/stm32l486zg" ]
stm32l496ae = [ "stm32-metapac/stm32l496ae" ]
stm32l496ag = [ "stm32-metapac/stm32l496ag" ]
stm32l496qe = [ "stm32-metapac/stm32l496qe" ]
stm32l496qg = [ "stm32-metapac/stm32l496qg" ]
stm32l496re = [ "stm32-metapac/stm32l496re" ]
stm32l496rg = [ "stm32-metapac/stm32l496rg" ]
stm32l496ve = [ "stm32-metapac/stm32l496ve" ]
stm32l496vg = [ "stm32-metapac/stm32l496vg" ]
stm32l496wg = [ "stm32-metapac/stm32l496wg" ]
stm32l496ze = [ "stm32-metapac/stm32l496ze" ]
stm32l496zg = [ "stm32-metapac/stm32l496zg" ]
stm32l4a6ag = [ "stm32-metapac/stm32l4a6ag" ]
stm32l4a6qg = [ "stm32-metapac/stm32l4a6qg" ]
stm32l4a6rg = [ "stm32-metapac/stm32l4a6rg" ]
stm32l4a6vg = [ "stm32-metapac/stm32l4a6vg" ]
stm32l4a6zg = [ "stm32-metapac/stm32l4a6zg" ]
stm32l4p5ae = [ "stm32-metapac/stm32l4p5ae" ]
stm32l4p5ag = [ "stm32-metapac/stm32l4p5ag" ]
stm32l4p5ce = [ "stm32-metapac/stm32l4p5ce" ]
stm32l4p5cg = [ "stm32-metapac/stm32l4p5cg" ]
stm32l4p5qe = [ "stm32-metapac/stm32l4p5qe" ]
stm32l4p5qg = [ "stm32-metapac/stm32l4p5qg" ]
stm32l4p5re = [ "stm32-metapac/stm32l4p5re" ]
stm32l4p5rg = [ "stm32-metapac/stm32l4p5rg" ]
stm32l4p5ve = [ "stm32-metapac/stm32l4p5ve" ]
stm32l4p5vg = [ "stm32-metapac/stm32l4p5vg" ]
stm32l4p5ze = [ "stm32-metapac/stm32l4p5ze" ]
stm32l4p5zg = [ "stm32-metapac/stm32l4p5zg" ]
stm32l4q5ag = [ "stm32-metapac/stm32l4q5ag" ]
stm32l4q5cg = [ "stm32-metapac/stm32l4q5cg" ]
stm32l4q5qg = [ "stm32-metapac/stm32l4q5qg" ]
stm32l4q5rg = [ "stm32-metapac/stm32l4q5rg" ]
stm32l4q5vg = [ "stm32-metapac/stm32l4q5vg" ]
stm32l4q5zg = [ "stm32-metapac/stm32l4q5zg" ]
stm32l4r5ag = [ "stm32-metapac/stm32l4r5ag" ]
stm32l4r5ai = [ "stm32-metapac/stm32l4r5ai" ]
stm32l4r5qg = [ "stm32-metapac/stm32l4r5qg" ]
stm32l4r5qi = [ "stm32-metapac/stm32l4r5qi" ]
stm32l4r5vg = [ "stm32-metapac/stm32l4r5vg" ]
stm32l4r5vi = [ "stm32-metapac/stm32l4r5vi" ]
stm32l4r5zg = [ "stm32-metapac/stm32l4r5zg" ]
stm32l4r5zi = [ "stm32-metapac/stm32l4r5zi" ]
stm32l4r7ai = [ "stm32-metapac/stm32l4r7ai" ]
stm32l4r7vi = [ "stm32-metapac/stm32l4r7vi" ]
stm32l4r7zi = [ "stm32-metapac/stm32l4r7zi" ]
stm32l4r9ag = [ "stm32-metapac/stm32l4r9ag" ]
stm32l4r9ai = [ "stm32-metapac/stm32l4r9ai" ]
stm32l4r9vg = [ "stm32-metapac/stm32l4r9vg" ]
stm32l4r9vi = [ "stm32-metapac/stm32l4r9vi" ]
stm32l4r9zg = [ "stm32-metapac/stm32l4r9zg" ]
stm32l4r9zi = [ "stm32-metapac/stm32l4r9zi" ]
stm32l4s5ai = [ "stm32-metapac/stm32l4s5ai" ]
stm32l4s5qi = [ "stm32-metapac/stm32l4s5qi" ]
stm32l4s5vi = [ "stm32-metapac/stm32l4s5vi" ]
stm32l4s5zi = [ "stm32-metapac/stm32l4s5zi" ]
stm32l4s7ai = [ "stm32-metapac/stm32l4s7ai" ]
stm32l4s7vi = [ "stm32-metapac/stm32l4s7vi" ]
stm32l4s7zi = [ "stm32-metapac/stm32l4s7zi" ]
stm32l4s9ai = [ "stm32-metapac/stm32l4s9ai" ]
stm32l4s9vi = [ "stm32-metapac/stm32l4s9vi" ]
stm32l4s9zi = [ "stm32-metapac/stm32l4s9zi" ]
stm32l552cc = [ "stm32-metapac/stm32l552cc" ]
stm32l552ce = [ "stm32-metapac/stm32l552ce" ]
stm32l552me = [ "stm32-metapac/stm32l552me" ]
stm32l552qc = [ "stm32-metapac/stm32l552qc" ]
stm32l552qe = [ "stm32-metapac/stm32l552qe" ]
stm32l552rc = [ "stm32-metapac/stm32l552rc" ]
stm32l552re = [ "stm32-metapac/stm32l552re" ]
stm32l552vc = [ "stm32-metapac/stm32l552vc" ]
stm32l552ve = [ "stm32-metapac/stm32l552ve" ]
stm32l552zc = [ "stm32-metapac/stm32l552zc" ]
stm32l552ze = [ "stm32-metapac/stm32l552ze" ]
stm32l562ce = [ "stm32-metapac/stm32l562ce" ]
stm32l562me = [ "stm32-metapac/stm32l562me" ]
stm32l562qe = [ "stm32-metapac/stm32l562qe" ]
stm32l562re = [ "stm32-metapac/stm32l562re" ]
stm32l562ve = [ "stm32-metapac/stm32l562ve" ]
stm32l562ze = [ "stm32-metapac/stm32l562ze" ]
stm32u031c6 = [ "stm32-metapac/stm32u031c6" ]
stm32u031c8 = [ "stm32-metapac/stm32u031c8" ]
stm32u031f4 = [ "stm32-metapac/stm32u031f4" ]
stm32u031f6 = [ "stm32-metapac/stm32u031f6" ]
stm32u031f8 = [ "stm32-metapac/stm32u031f8" ]
stm32u031g6 = [ "stm32-metapac/stm32u031g6" ]
stm32u031g8 = [ "stm32-metapac/stm32u031g8" ]
stm32u031k4 = [ "stm32-metapac/stm32u031k4" ]
stm32u031k6 = [ "stm32-metapac/stm32u031k6" ]
stm32u031k8 = [ "stm32-metapac/stm32u031k8" ]
stm32u031r6 = [ "stm32-metapac/stm32u031r6" ]
stm32u031r8 = [ "stm32-metapac/stm32u031r8" ]
stm32u073c8 = [ "stm32-metapac/stm32u073c8" ]
stm32u073cb = [ "stm32-metapac/stm32u073cb" ]
stm32u073cc = [ "stm32-metapac/stm32u073cc" ]
stm32u073h8 = [ "stm32-metapac/stm32u073h8" ]
stm32u073hb = [ "stm32-metapac/stm32u073hb" ]
stm32u073hc = [ "stm32-metapac/stm32u073hc" ]
stm32u073k8 = [ "stm32-metapac/stm32u073k8" ]
stm32u073kb = [ "stm32-metapac/stm32u073kb" ]
stm32u073kc = [ "stm32-metapac/stm32u073kc" ]
stm32u073m8 = [ "stm32-metapac/stm32u073m8" ]
stm32u073mb = [ "stm32-metapac/stm32u073mb" ]
stm32u073mc = [ "stm32-metapac/stm32u073mc" ]
stm32u073r8 = [ "stm32-metapac/stm32u073r8" ]
stm32u073rb = [ "stm32-metapac/stm32u073rb" ]
stm32u073rc = [ "stm32-metapac/stm32u073rc" ]
stm32u083cc = [ "stm32-metapac/stm32u083cc" ]
stm32u083hc = [ "stm32-metapac/stm32u083hc" ]
stm32u083kc = [ "stm32-metapac/stm32u083kc" ]
stm32u083mc = [ "stm32-metapac/stm32u083mc" ]
stm32u083rc = [ "stm32-metapac/stm32u083rc" ]
stm32u535cb = [ "stm32-metapac/stm32u535cb" ]
stm32u535cc = [ "stm32-metapac/stm32u535cc" ]
stm32u535ce = [ "stm32-metapac/stm32u535ce" ]
stm32u535je = [ "stm32-metapac/stm32u535je" ]
stm32u535nc = [ "stm32-metapac/stm32u535nc" ]
stm32u535ne = [ "stm32-metapac/stm32u535ne" ]
stm32u535rb = [ "stm32-metapac/stm32u535rb" ]
stm32u535rc = [ "stm32-metapac/stm32u535rc" ]
stm32u535re = [ "stm32-metapac/stm32u535re" ]
stm32u535vc = [ "stm32-metapac/stm32u535vc" ]
stm32u535ve = [ "stm32-metapac/stm32u535ve" ]
stm32u545ce = [ "stm32-metapac/stm32u545ce" ]
stm32u545je = [ "stm32-metapac/stm32u545je" ]
stm32u545ne = [ "stm32-metapac/stm32u545ne" ]
stm32u545re = [ "stm32-metapac/stm32u545re" ]
stm32u545ve = [ "stm32-metapac/stm32u545ve" ]
stm32u575ag = [ "stm32-metapac/stm32u575ag" ]
stm32u575ai = [ "stm32-metapac/stm32u575ai" ]
stm32u575cg = [ "stm32-metapac/stm32u575cg" ]
stm32u575ci = [ "stm32-metapac/stm32u575ci" ]
stm32u575og = [ "stm32-metapac/stm32u575og" ]
stm32u575oi = [ "stm32-metapac/stm32u575oi" ]
stm32u575qg = [ "stm32-metapac/stm32u575qg" ]
stm32u575qi = [ "stm32-metapac/stm32u575qi" ]
stm32u575rg = [ "stm32-metapac/stm32u575rg" ]
stm32u575ri = [ "stm32-metapac/stm32u575ri" ]
stm32u575vg = [ "stm32-metapac/stm32u575vg" ]
stm32u575vi = [ "stm32-metapac/stm32u575vi" ]
stm32u575zg = [ "stm32-metapac/stm32u575zg" ]
stm32u575zi = [ "stm32-metapac/stm32u575zi" ]
stm32u585ai = [ "stm32-metapac/stm32u585ai" ]
stm32u585ci = [ "stm32-metapac/stm32u585ci" ]
stm32u585oi = [ "stm32-metapac/stm32u585oi" ]
stm32u585qi = [ "stm32-metapac/stm32u585qi" ]
stm32u585ri = [ "stm32-metapac/stm32u585ri" ]
stm32u585vi = [ "stm32-metapac/stm32u585vi" ]
stm32u585zi = [ "stm32-metapac/stm32u585zi" ]
stm32u595ai = [ "stm32-metapac/stm32u595ai" ]
stm32u595aj = [ "stm32-metapac/stm32u595aj" ]
stm32u595qi = [ "stm32-metapac/stm32u595qi" ]
stm32u595qj = [ "stm32-metapac/stm32u595qj" ]
stm32u595ri = [ "stm32-metapac/stm32u595ri" ]
stm32u595rj = [ "stm32-metapac/stm32u595rj" ]
stm32u595vi = [ "stm32-metapac/stm32u595vi" ]
stm32u595vj = [ "stm32-metapac/stm32u595vj" ]
stm32u595zi = [ "stm32-metapac/stm32u595zi" ]
stm32u595zj = [ "stm32-metapac/stm32u595zj" ]
stm32u599bj = [ "stm32-metapac/stm32u599bj" ]
stm32u599ni = [ "stm32-metapac/stm32u599ni" ]
stm32u599nj = [ "stm32-metapac/stm32u599nj" ]
stm32u599vi = [ "stm32-metapac/stm32u599vi" ]
stm32u599vj = [ "stm32-metapac/stm32u599vj" ]
stm32u599zi = [ "stm32-metapac/stm32u599zi" ]
stm32u599zj = [ "stm32-metapac/stm32u599zj" ]
stm32u5a5aj = [ "stm32-metapac/stm32u5a5aj" ]
stm32u5a5qi = [ "stm32-metapac/stm32u5a5qi" ]
stm32u5a5qj = [ "stm32-metapac/stm32u5a5qj" ]
stm32u5a5rj = [ "stm32-metapac/stm32u5a5rj" ]
stm32u5a5vj = [ "stm32-metapac/stm32u5a5vj" ]
stm32u5a5zj = [ "stm32-metapac/stm32u5a5zj" ]
stm32u5a9bj = [ "stm32-metapac/stm32u5a9bj" ]
stm32u5a9nj = [ "stm32-metapac/stm32u5a9nj" ]
stm32u5a9vj = [ "stm32-metapac/stm32u5a9vj" ]
stm32u5a9zj = [ "stm32-metapac/stm32u5a9zj" ]
stm32u5f7vi = [ "stm32-metapac/stm32u5f7vi" ]
stm32u5f7vj = [ "stm32-metapac/stm32u5f7vj" ]
stm32u5f9bj = [ "stm32-metapac/stm32u5f9bj" ]
stm32u5f9nj = [ "stm32-metapac/stm32u5f9nj" ]
stm32u5f9vi = [ "stm32-metapac/stm32u5f9vi" ]
stm32u5f9vj = [ "stm32-metapac/stm32u5f9vj" ]
stm32u5f9zi = [ "stm32-metapac/stm32u5f9zi" ]
stm32u5f9zj = [ "stm32-metapac/stm32u5f9zj" ]
stm32u5g7vj = [ "stm32-metapac/stm32u5g7vj" ]
stm32u5g9bj = [ "stm32-metapac/stm32u5g9bj" ]
stm32u5g9nj = [ "stm32-metapac/stm32u5g9nj" ]
stm32u5g9vj = [ "stm32-metapac/stm32u5g9vj" ]
stm32u5g9zj = [ "stm32-metapac/stm32u5g9zj" ]
stm32wb10cc = [ "stm32-metapac/stm32wb10cc" ]
stm32wb15cc = [ "stm32-metapac/stm32wb15cc" ]
stm32wb30ce = [ "stm32-metapac/stm32wb30ce" ]
stm32wb35cc = [ "stm32-metapac/stm32wb35cc" ]
stm32wb35ce = [ "stm32-metapac/stm32wb35ce" ]
stm32wb50cg = [ "stm32-metapac/stm32wb50cg" ]
stm32wb55cc = [ "stm32-metapac/stm32wb55cc" ]
stm32wb55ce = [ "stm32-metapac/stm32wb55ce" ]
stm32wb55cg = [ "stm32-metapac/stm32wb55cg" ]
stm32wb55rc = [ "stm32-metapac/stm32wb55rc" ]
stm32wb55re = [ "stm32-metapac/stm32wb55re" ]
stm32wb55rg = [ "stm32-metapac/stm32wb55rg" ]
stm32wb55vc = [ "stm32-metapac/stm32wb55vc" ]
stm32wb55ve = [ "stm32-metapac/stm32wb55ve" ]
stm32wb55vg = [ "stm32-metapac/stm32wb55vg" ]
stm32wb55vy = [ "stm32-metapac/stm32wb55vy" ]
stm32wba50ke = [ "stm32-metapac/stm32wba50ke" ]
stm32wba50kg = [ "stm32-metapac/stm32wba50kg" ]
stm32wba52ce = [ "stm32-metapac/stm32wba52ce" ]
stm32wba52cg = [ "stm32-metapac/stm32wba52cg" ]
stm32wba52ke = [ "stm32-metapac/stm32wba52ke" ]
stm32wba52kg = [ "stm32-metapac/stm32wba52kg" ]
stm32wba54ce = [ "stm32-metapac/stm32wba54ce" ]
stm32wba54cg = [ "stm32-metapac/stm32wba54cg" ]
stm32wba54ke = [ "stm32-metapac/stm32wba54ke" ]
stm32wba54kg = [ "stm32-metapac/stm32wba54kg" ]
stm32wba55ce = [ "stm32-metapac/stm32wba55ce" ]
stm32wba55cg = [ "stm32-metapac/stm32wba55cg" ]
stm32wba55he = [ "stm32-metapac/stm32wba55he" ]
stm32wba55hg = [ "stm32-metapac/stm32wba55hg" ]
stm32wba55ue = [ "stm32-metapac/stm32wba55ue" ]
stm32wba55ug = [ "stm32-metapac/stm32wba55ug" ]
stm32wba62cg = [ "stm32-metapac/stm32wba62cg" ]
stm32wba62ci = [ "stm32-metapac/stm32wba62ci" ]
stm32wba62mg = [ "stm32-metapac/stm32wba62mg" ]
stm32wba62mi = [ "stm32-metapac/stm32wba62mi" ]
stm32wba62pg = [ "stm32-metapac/stm32wba62pg" ]
stm32wba62pi = [ "stm32-metapac/stm32wba62pi" ]
stm32wba63cg = [ "stm32-metapac/stm32wba63cg" ]
stm32wba63ci = [ "stm32-metapac/stm32wba63ci" ]
stm32wba64cg = [ "stm32-metapac/stm32wba64cg" ]
stm32wba64ci = [ "stm32-metapac/stm32wba64ci" ]
stm32wba65cg = [ "stm32-metapac/stm32wba65cg" ]
stm32wba65ci = [ "stm32-metapac/stm32wba65ci" ]
stm32wba65mg = [ "stm32-metapac/stm32wba65mg" ]
stm32wba65mi = [ "stm32-metapac/stm32wba65mi" ]
stm32wba65pg = [ "stm32-metapac/stm32wba65pg" ]
stm32wba65pi = [ "stm32-metapac/stm32wba65pi" ]
stm32wba65rg = [ "stm32-metapac/stm32wba65rg" ]
stm32wba65ri = [ "stm32-metapac/stm32wba65ri" ]
stm32wl54cc-cm4 = [ "stm32-metapac/stm32wl54cc-cm4", "_dual-core", "_core-cm4" ]
stm32wl54cc-cm0p = [ "stm32-metapac/stm32wl54cc-cm0p", "_dual-core", "_core-cm0p" ]
stm32wl54jc-cm4 = [ "stm32-metapac/stm32wl54jc-cm4", "_dual-core", "_core-cm4" ]
stm32wl54jc-cm0p = [ "stm32-metapac/stm32wl54jc-cm0p", "_dual-core", "_core-cm0p" ]
stm32wl55cc-cm4 = [ "stm32-metapac/stm32wl55cc-cm4", "_dual-core", "_core-cm4" ]
stm32wl55cc-cm0p = [ "stm32-metapac/stm32wl55cc-cm0p", "_dual-core", "_core-cm0p" ]
stm32wl55jc-cm4 = [ "stm32-metapac/stm32wl55jc-cm4", "_dual-core", "_core-cm4" ]
stm32wl55jc-cm0p = [ "stm32-metapac/stm32wl55jc-cm0p", "_dual-core", "_core-cm0p" ]
stm32wle4c8 = [ "stm32-metapac/stm32wle4c8" ]
stm32wle4cb = [ "stm32-metapac/stm32wle4cb" ]
stm32wle4cc = [ "stm32-metapac/stm32wle4cc" ]
stm32wle4j8 = [ "stm32-metapac/stm32wle4j8" ]
stm32wle4jb = [ "stm32-metapac/stm32wle4jb" ]
stm32wle4jc = [ "stm32-metapac/stm32wle4jc" ]
stm32wle5c8 = [ "stm32-metapac/stm32wle5c8" ]
stm32wle5cb = [ "stm32-metapac/stm32wle5cb" ]
stm32wle5cc = [ "stm32-metapac/stm32wle5cc" ]
stm32wle5j8 = [ "stm32-metapac/stm32wle5j8" ]
stm32wle5jb = [ "stm32-metapac/stm32wle5jb" ]
stm32wle5jc = [ "stm32-metapac/stm32wle5jc" ]
