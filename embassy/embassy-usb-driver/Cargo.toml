[package]
name = "embassy-usb-driver"
version = "0.2.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Driver trait for `embassy-usb`, an async USB device stack for embedded devices."
keywords = ["embedded", "async", "usb", "hal", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-usb-driver"

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-usb-driver-v$VERSION/embassy-usb-driver/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-usb-driver/src/"
features = ["defmt"]
target = "thumbv7em-none-eabi"

[package.metadata.docs.rs]
features = ["defmt"]

[dependencies]
embedded-io-async = "0.6.1"
defmt = { version = "1", optional = true }
