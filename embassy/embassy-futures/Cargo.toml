[package]
name = "embassy-futures"
version = "0.1.2"
edition = "2021"
description = "no-std, no-alloc utilities for working with futures"
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-futures"
readme = "README.md"
license = "MIT OR Apache-2.0"
categories = [
    "embedded",
    "no-std",
    "concurrency",
    "asynchronous",
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-futures-v$VERSION/embassy-futures/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-futures/src/"
features = ["defmt"]
target = "thumbv7em-none-eabi"

[package.metadata.docs.rs]
features = ["defmt"]

[dependencies]
defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }
