# Changelog for embassy-futures

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

<!-- next-header -->
## Unreleased - ReleaseDate

## 0.1.2 - 2025-08-26

- Preserve location information for `defmt` in `fmt` calls ([#3085](https://github.com/embassy-rs/embassy/pull/3085))
- Fixed soundness issue in `select_slice` ([#3328](https://github.com/embassy-rs/embassy/pull/3328))
- Added `select5` and `select6` ([#3430](https://github.com/embassy-rs/embassy/pull/3430))
- Added `is_x` methods for all `EitherN` enum variants (#[3650](https://github.com/embassy-rs/embassy/pull/3650))
