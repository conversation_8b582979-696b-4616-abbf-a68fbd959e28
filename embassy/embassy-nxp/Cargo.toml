[package]
name = "embassy-nxp"
version = "0.1.0"
edition = "2021"

publish = false

[package.metadata.embassy]
build = [
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "lpc55-core0"]},
    {target = "thumbv7em-none-eabihf", features = ["defmt", "mimxrt1011", "rt", "time-driver-pit"]},
    {target = "thumbv7em-none-eabihf", features = ["defmt", "mimxrt1062", "rt", "time-driver-pit"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-nxp-v$VERSION/embassy-nxp/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-nxp/src/"
features = ["defmt", "unstable-pac" ] # TODO: Add time-driver-any, as both lpc55 and mimxrt1xxx use different drivers.

flavors = [
    { regex_feature = "lpc55-core0", target = "thumbv8m.main-none-eabihf" },
    { regex_feature = "mimxrt.*", target = "thumbv7em-none-eabihf" },
]

[dependencies]
cortex-m = "0.7.7"
cortex-m-rt = "0.7.0"
critical-section = "1.1.2"
embassy-hal-internal = { version = "0.3.0", path = "../embassy-hal-internal", features = ["cortex-m", "prio-bits-2"] }
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
defmt = { version = "1", optional = true }
log = { version = "0.4.27", optional = true }
embassy-time = { version = "0.5.0", path = "../embassy-time", optional = true }
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver", optional = true }
embassy-time-queue-utils = { version = "0.3.0", path = "../embassy-time-queue-utils", optional = true }
embedded-io = "0.6.1"
embedded-hal-02 = { package = "embedded-hal", version = "0.2.6", features = ["unproven"] }
## Chip dependencies
nxp-pac = { version = "0.1.0", optional = true, git = "https://github.com/i509VCB/nxp-pac", rev = "b736e3038254d593024aaa1a5a7b1f95a5728538"}

imxrt-rt = { version = "0.1.7", optional = true, features = ["device"] }

[build-dependencies]
cfg_aliases = "0.2.1"
nxp-pac = { version = "0.1.0", git = "https://github.com/i509VCB/nxp-pac", rev = "b736e3038254d593024aaa1a5a7b1f95a5728538", features = ["metadata"], optional = true }
proc-macro2 = "1.0.95"
quote = "1.0.15"

[features]
default = ["rt"]
# Enable PACs as optional dependencies, since some chip families will use different pac crates (temporarily).
rt = ["nxp-pac?/rt"]

## Enable [defmt support](https://docs.rs/defmt) and enables `defmt` debug-log messages and formatting in embassy drivers.
defmt = ["dep:defmt", "embassy-hal-internal/defmt", "embassy-sync/defmt"]

log = ["dep:log"]

## Use Periodic Interrupt Timer (PIT) as the time driver for `embassy-time`, with a tick rate of 1 MHz
time-driver-pit = ["_time_driver", "embassy-time?/tick-hz-1_000_000"]

## Use Real Time Clock (RTC) as the time driver for `embassy-time`, with a tick rate of 32768 Hz
time-driver-rtc = ["_time_driver", "embassy-time?/tick-hz-32_768"]

## Reexport the PAC for the currently enabled chip at `embassy_nxp::pac` (unstable)
unstable-pac = []
# This is unstable because semver-minor (non-breaking) releases of embassy-nxp may major-bump (breaking) the PAC version.
# If this is an issue for you, you're encouraged to directly depend on a fixed version of the PAC.
# There are no plans to make this stable.

## internal use only
#
# This feature is unfortunately a hack around the fact that cfg_aliases cannot apply to the buildscript
# that creates the aliases.
_rt1xxx = []

# A timer driver is enabled.
_time_driver = ["dep:embassy-time-driver", "dep:embassy-time-queue-utils"]

#! ### Chip selection features
lpc55-core0 = ["nxp-pac/lpc55s69_cm33_core0"]
mimxrt1011 = ["nxp-pac/mimxrt1011", "_rt1xxx", "dep:imxrt-rt"]
mimxrt1062 = ["nxp-pac/mimxrt1062", "_rt1xxx", "dep:imxrt-rt"]
