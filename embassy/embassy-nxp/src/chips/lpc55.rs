pub use nxp_pac as pac;

embassy_hal_internal::peripherals! {
    // External pins. These are not only GPIOs, they are multi-purpose pins and can be used by other
    // peripheral types (e.g. I2C).
    PIO0_0,
    PIO0_1,
    PIO0_2,
    PIO0_3,
    <PERSON>IO0_4,
    <PERSON>IO0_5,
    <PERSON>IO0_6,
    <PERSON><PERSON>0_7,
    <PERSON><PERSON>0_8,
    <PERSON><PERSON>0_9,
    <PERSON><PERSON>0_10,
    <PERSON><PERSON>0_11,
    <PERSON><PERSON>0_12,
    <PERSON><PERSON>0_13,
    <PERSON>IO0_14,
    <PERSON>IO0_15,
    <PERSON>IO0_16,
    <PERSON>IO0_17,
    PIO0_18,
    PIO0_19,
    <PERSON>IO0_20,
    PIO0_21,
    PIO0_22,
    <PERSON>IO0_23,
    <PERSON>IO0_24,
    <PERSON>IO0_25,
    <PERSON><PERSON>0_26,
    <PERSON><PERSON>0_27,
    <PERSON><PERSON>0_28,
    <PERSON><PERSON>0_29,
    <PERSON><PERSON>0_30,
    <PERSON><PERSON>0_31,
    <PERSON>IO1_0,
    PIO1_1,
    <PERSON>IO1_2,
    <PERSON>IO1_3,
    <PERSON>IO1_4,
    <PERSON>IO1_5,
    <PERSON>IO1_6,
    <PERSON>IO1_7,
    <PERSON>IO1_8,
    <PERSON>IO1_9,
    PIO1_10,
    PIO1_11,
    <PERSON>IO1_12,
    PIO1_13,
    PIO1_14,
    <PERSON><PERSON>1_15,
    <PERSON><PERSON>1_16,
    <PERSON><PERSON>1_17,
    <PERSON><PERSON>1_18,
    <PERSON><PERSON>1_19,
    <PERSON><PERSON>1_20,
    <PERSON><PERSON>1_21,
    PIO1_22,
    P<PERSON>1_23,
    PIO1_24,
    PIO1_25,
    P<PERSON>1_26,
    PIO1_27,
    PIO1_28,
    PIO1_29,
    PIO1_30,
    PIO1_31,

    USART0,
    USART1,
    USART2,
    USART3,
    USART4,
    USART5,
    USART6,
    USART7
}
