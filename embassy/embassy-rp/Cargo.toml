[package]
name = "embassy-rp"
version = "0.8.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Embassy Hardware Abstraction Layer (HAL) for the Raspberry Pi RP2040 or RP235x microcontroller"
keywords = ["embedded", "async", "rp235x", "rp2040", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-rp"

[package.metadata.embassy]
build = [
    {target = "thumbv6m-none-eabi", features = ["defmt", "rp2040", "time-driver"]},
    {target = "thumbv6m-none-eabi", features = ["log", "rp2040", "time-driver"]},
    {target = "thumbv6m-none-eabi", features = ["intrinsics", "rp2040", "time-driver"]},
    {target = "thumbv6m-none-eabi", features = ["qspi-as-gpio", "rp2040", "time-driver"]},
    {target = "thumbv8m.main-none-eabihf", features = ["defmt", "rp235xa", "time-driver"]},
    {target = "thumbv8m.main-none-eabihf", features = ["log", "rp235xa", "time-driver"]},
    {target = "thumbv8m.main-none-eabihf", features = ["binary-info", "rp235xa", "time-driver"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-rp-v$VERSION/embassy-rp/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-rp/src/"
features = ["defmt", "unstable-pac", "time-driver"]
flavors = [
    { name = "rp2040", target = "thumbv6m-none-eabi", features = ["rp2040"] },
    { name = "rp235xa", target = "thumbv8m.main-none-eabihf", features = ["rp235xa"] },
    { name = "rp235xb", target = "thumbv8m.main-none-eabihf", features = ["rp235xb"] },
]

[package.metadata.docs.rs]
# TODO: it's not GREAT to set a specific target, but docs.rs builds will fail otherwise
# for now, default to rp2040
features = ["defmt", "unstable-pac", "time-driver", "rp2040"]

[features]
default = [ "rt" ]

## Enable the `rt` feature of [`rp-pac`](https://docs.rs/rp-pac).
## With `rt` enabled the PAC provides interrupt vectors instead of letting [`cortex-m-rt`](https://docs.rs/cortex-m-rt) do that.
## See <https://docs.rs/cortex-m-rt/latest/cortex_m_rt/#device> for more info.
rt = [ "rp-pac/rt" ]

## Enable [defmt support](https://docs.rs/defmt) and enables `defmt` debug-log messages and formatting in embassy drivers.
defmt = ["dep:defmt", "embassy-usb-driver/defmt", "embassy-hal-internal/defmt"]

## Configure the [`critical-section`](https://docs.rs/critical-section) crate to use an implementation that is safe for multicore use on rp2040.
critical-section-impl = ["critical-section/restore-state-u8"]

## Reexport the PAC for the currently enabled chip at `embassy_rp::pac`.
## This is unstable because semver-minor (non-breaking) releases of `embassy-rp` may major-bump (breaking) the PAC version.
## If this is an issue for you, you're encouraged to directly depend on a fixed version of the PAC.
## There are no plans to make this stable.
unstable-pac = []

## Enable the timer for use with `embassy-time` with a 1MHz tick rate.
time-driver = ["dep:embassy-time-driver", "embassy-time-driver?/tick-hz-1_000_000", "dep:embassy-time-queue-utils"]

## Enable ROM function cache. This will store the address of a ROM function when first used, improving performance of subsequent calls.
rom-func-cache = []
## Enable implementations of some compiler intrinsics using functions in the rp2040 Mask ROM.
## These should be as fast or faster than the implementations in compiler-builtins. They also save code space and reduce memory contention.
## Compiler intrinsics are used automatically, you do not need to change your code to get performance improvements from this feature.
intrinsics = []
## Enable intrinsics based on extra ROM functions added in the v2 version of the rp2040 Mask ROM.
## This version added a lot more floating point operations - many f64 functions and a few f32 functions were added in ROM v2.
rom-v2-intrinsics = []

## Allow using QSPI pins as GPIO pins. This is mostly not what you want (because your flash is attached via QSPI pins)
## and adds code and memory overhead when this feature is enabled.
qspi-as-gpio = []

## Indicate code is running from RAM.
## Set this if all code is in RAM, and the cores never access memory-mapped flash memory through XIP.
## This allows the flash driver to not force pausing execution on both cores when doing flash operations.
run-from-ram = []

#! ### boot2 flash chip support
#! RP2040's internal bootloader is only able to run code from the first 256 bytes of flash.
#! A 2nd stage bootloader (boot2) is required to run larger programs from external flash.
#! Select from existing boot2 implementations via the following features. If none are selected,
#! boot2-w25q080 will be used (w25q080 is the flash chip used on the pico).
#! Each implementation uses flash commands and timings specific to a QSPI flash chip family for better performance.
## Use boot2 with support for Renesas/Dialog AT25SF128a SPI flash.
boot2-at25sf128a = []
## Use boot2 with support for Gigadevice GD25Q64C SPI flash.
boot2-gd25q64cs = []
## Use boot2 that only uses generic flash commands - these are supported by all SPI flash, but are slower.
boot2-generic-03h = []
## Use boot2 with support for ISSI IS25LP080 SPI flash.
boot2-is25lp080 = []
## Use boot2 that copies the entire program to RAM before booting. This uses generic flash commands to perform the copy.
boot2-ram-memcpy = []
## Use boot2 with support for Winbond W25Q080 SPI flash.
boot2-w25q080 = []
## Use boot2 with support for Winbond W25X10CL SPI flash.
boot2-w25x10cl = []
## Have embassy-rp not provide the boot2 so you can use your own.
## Place your own in the ".boot2" section like:
## ```
## #[link_section = ".boot2"]
## #[used]
## static BOOT2: [u8; 256] = [0; 256]; // Provide your own with e.g. include_bytes!
## ```
boot2-none = []

#! ### Image Definition support
#! RP2350's internal bootloader will only execute firmware if it has a valid Image Definition.
#! There are other tags that can be used (for example, you can tag an image as "DATA")
#! but programs will want to have an exe header. "SECURE_EXE" is usually what you want.
#! Use imagedef-none if you want to configure the Image Definition manually
#! If none are selected, imagedef-secure-exe will be used

## Image is executable and starts in Secure mode
imagedef-secure-exe = []
## Image is executable and starts in Non-secure mode
imagedef-nonsecure-exe = []

## Have embassy-rp not provide the Image Definition so you can use your own.
## Place your own in the ".start_block" section like:
## ```ignore
## use embassy_rp::block::ImageDef;
##
## #[link_section = ".start_block"]
## #[used]
## static IMAGE_DEF: ImageDef = ImageDef::secure_exe(); // Update this with your own implementation.
## ```
imagedef-none = []

## Configure the hal for use with the rp2040
rp2040 = ["rp-pac/rp2040"]
_rp235x = ["rp-pac/rp235x"]
## Configure the hal for use with the rp235xA
rp235xa = ["_rp235x"]
## Configure the hal for use with the rp235xB
rp235xb = ["_rp235x"]

# hack around cortex-m peripherals being wrong when running tests.
_test = []

## Add a binary-info header block containing picotool-compatible metadata.
##
## Takes up a little flash space, but picotool can then report the name of your
## program and other details.
binary-info = ["rt", "dep:rp-binary-info", "rp-binary-info?/binary-info"]

[dependencies]
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver", optional = true }
embassy-time-queue-utils = { version = "0.3.0", path = "../embassy-time-queue-utils", optional = true }
embassy-time = { version = "0.5.0", path = "../embassy-time" }
embassy-futures = { version = "0.1.2", path = "../embassy-futures" }
embassy-hal-internal = { version = "0.3.0", path = "../embassy-hal-internal", features = ["cortex-m", "prio-bits-2"] }
embassy-embedded-hal = { version = "0.5.0", path = "../embassy-embedded-hal" }
embassy-usb-driver = { version = "0.2.0", path = "../embassy-usb-driver" }
atomic-polyfill = "1.0.1"
defmt = { version = "1.0.1", optional = true }
log = { version = "0.4.14", optional = true }
nb = "1.1.0"
cfg-if = "1.0.0"
cortex-m-rt = ">=0.6.15,<0.8"
cortex-m = "0.7.6"
critical-section = "1.2.0"
chrono = { version = "0.4", default-features = false, optional = true }
embedded-io = { version = "0.6.1" }
embedded-io-async = { version = "0.6.1" }
embedded-storage = { version = "0.3" }
embedded-storage-async = { version = "0.4.1" }
fixed = "1.28.0"

rp-pac = { version = "7.0.0" }

embedded-hal-02 = { package = "embedded-hal", version = "0.2.6", features = ["unproven"] }
embedded-hal-1 = { package = "embedded-hal", version = "1.0" }
embedded-hal-async = { version = "1.0" }
embedded-hal-nb = { version = "1.0" }

rand-core-06 = { package = "rand_core", version = "0.6" }
rand-core-09 = { package = "rand_core", version = "0.9" }

pio = { version = "0.3" }
rp2040-boot2 = "0.3"
document-features = "0.2.10"
sha2-const-stable = "0.1"
rp-binary-info = { version = "0.1.0", optional = true }
smart-leds = "0.4.0"

[dev-dependencies]
embassy-executor = { version = "0.9.0", path = "../embassy-executor", features = ["arch-std", "executor-thread"] }
static_cell = { version = "2" }
