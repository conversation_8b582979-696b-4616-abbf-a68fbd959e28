[package]
name = "embassy-nrf"
version = "0.7.0"
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Embassy Hardware Abstraction Layer (HAL) for nRF series microcontrollers"
keywords = ["embedded", "async", "nordic", "nrf", "embedded-hal"]
categories = ["embedded", "hardware-support", "no-std", "asynchronous"]
repository = "https://github.com/embassy-rs/embassy"
documentation = "https://docs.embassy.dev/embassy-nrf"

[package.metadata.embassy]
build = [
    {target = "thumbv6m-none-eabi", features = ["gpiote", "nrf51", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52805", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52810", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52811", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52820", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52832", "reset-pin-as-gpio", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nfc-pins-as-gpio", "nrf52833", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf9160-s", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf9160-ns", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf5340-app-s", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf5340-app-ns", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf5340-net", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf54l15-app-s", "time", "time-driver-rtc1"]},
    {target = "thumbv8m.main-none-eabihf", features = ["gpiote", "nrf54l15-app-ns", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52840", "time"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52840", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "nrf52840", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "log", "nrf52840", "time"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "log", "nrf52840", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["gpiote", "log", "nrf52840", "time", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "gpiote", "nrf52840", "time"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "gpiote", "nrf52840", "time-driver-rtc1"]},
    {target = "thumbv7em-none-eabi", features = ["defmt", "gpiote", "nrf52840", "time", "time-driver-rtc1"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "nrf51", "time", "time-driver-rtc1"]},
    {target = "thumbv6m-none-eabi", features = ["defmt", "nrf51", "time"]},
    {target = "thumbv6m-none-eabi", features = ["nrf51", "time"]},
]

[package.metadata.embassy_docs]
src_base = "https://github.com/embassy-rs/embassy/blob/embassy-nrf-v$VERSION/embassy-nrf/src/"
src_base_git = "https://github.com/embassy-rs/embassy/blob/$COMMIT/embassy-nrf/src/"

features = ["time", "defmt", "unstable-pac", "gpiote", "time-driver-rtc1"]
flavors = [
    { regex_feature = "nrf51", target = "thumbv6m-none-eabi" },
    { regex_feature = "nrf52.*", target = "thumbv7em-none-eabihf" },
    { regex_feature = "nrf53.*", target = "thumbv8m.main-none-eabihf" },
    { regex_feature = "nrf54.*", target = "thumbv8m.main-none-eabihf" },
    { regex_feature = "nrf91.*", target = "thumbv8m.main-none-eabihf" },
]

[package.metadata.docs.rs]
features = ["nrf52840", "time", "defmt", "unstable-pac", "gpiote", "time-driver-rtc1"]
rustdoc-args = ["--cfg", "docsrs"]

[features]
default = ["rt"]
## Cortex-M runtime (enabled by default)
rt = ["nrf-pac/rt"]

## Enable features requiring `embassy-time`
time = ["dep:embassy-time", "embassy-embedded-hal/time"]

## Enable defmt
defmt = ["dep:defmt", "embassy-hal-internal/defmt", "embassy-sync/defmt", "embassy-usb-driver/defmt", "embassy-embedded-hal/defmt"]

## Reexport the PAC for the currently enabled chip at `embassy_nrf::pac` (unstable)
unstable-pac = []
# This is unstable because semver-minor (non-breaking) releases of embassy-nrf may major-bump (breaking) the PAC version.
# If this is an issue for you, you're encouraged to directly depend on a fixed version of the PAC.
# There are no plans to make this stable.

## Enable GPIO tasks and events
gpiote = []

## Use RTC1 as the time driver for `embassy-time`, with a tick rate of 32.768khz
time-driver-rtc1 = ["_time-driver"]

## Enable embassy-net 802.15.4 driver
net-driver = ["_net-driver"]

## Allow using the NFC pins as regular GPIO pins (P0_09/P0_10 on nRF52, P0_02/P0_03 on nRF53)
nfc-pins-as-gpio = []

## Allow using the RST pin as a regular GPIO pin.
##  * nRF52805, nRF52810, nRF52811, nRF52832: P0_21
##  * nRF52820, nRF52833, nRF52840: P0_18
reset-pin-as-gpio = []

## Allow using the LFXO pins as regular GPIO pins (P0_00/P0_01 on nRF53)
lfxo-pins-as-gpio = []

## Implements the MultiwriteNorFlash trait for QSPI. Should only be enabled if your external
## flash supports the semantics described [here](https://docs.rs/embedded-storage/0.3.1/embedded_storage/nor_flash/trait.MultiwriteNorFlash.html)
qspi-multiwrite-flash = []

#! ### Chip selection features
## nRF51
nrf51 = ["nrf-pac/nrf51", "_nrf51"]
## nRF52805
nrf52805 = ["nrf-pac/nrf52805", "_nrf52"]
## nRF52810
nrf52810 = ["nrf-pac/nrf52810", "_nrf52"]
## nRF52811
nrf52811 = ["nrf-pac/nrf52811", "_nrf52"]
## nRF52820
nrf52820 = ["nrf-pac/nrf52820", "_nrf52"]
## nRF52832
nrf52832 = ["nrf-pac/nrf52832", "_nrf52", "_nrf52832_anomaly_109"]
## nRF52833
nrf52833 = ["nrf-pac/nrf52833", "_nrf52", "_gpio-p1"]
## nRF52840
nrf52840 = ["nrf-pac/nrf52840", "_nrf52", "_gpio-p1"]
## nRF5340 application core in Secure mode
nrf5340-app-s = ["_nrf5340-app", "_s"]
## nRF5340 application core in Non-Secure mode
nrf5340-app-ns = ["_nrf5340-app", "_ns"]
## nRF5340 network core
nrf5340-net = ["_nrf5340-net"]
## nRF54L15 application core in Secure mode
nrf54l15-app-s = ["_nrf54l15-app", "_s", "_multi_wdt"]
## nRF54L15 application core in Non-Secure mode
nrf54l15-app-ns = ["_nrf54l15-app", "_ns"]

## nRF9160 in Secure mode
nrf9160-s = ["_nrf9160", "_s", "_nrf91"]
## nRF9160 in Non-Secure mode
nrf9160-ns = ["_nrf9160", "_ns", "_nrf91"]
## The nRF9120 is the internal part number for the nRF9161 and nRF9151.
## nRF9120 in Secure mode
nrf9120-s = ["_nrf9120", "_s", "_nrf91"]
nrf9151-s = ["nrf9120-s"]
nrf9161-s = ["nrf9120-s"]
## nRF9120 in Non-Secure mode
nrf9120-ns = ["_nrf9120", "_ns", "_nrf91"]
nrf9151-ns = ["nrf9120-ns"]
nrf9161-ns = ["nrf9120-ns"]

# Features starting with `_` are for internal use only. They're not intended
# to be enabled by other crates, and are not covered by semver guarantees.

_nrf5340-app = ["_nrf5340", "_multi_wdt", "nrf-pac/nrf5340-app"]
_nrf5340-net = ["_nrf5340", "nrf-pac/nrf5340-net"]
_nrf5340 = ["_gpio-p1", "_dppi"]
_nrf54l15-app = ["_nrf54l15", "nrf-pac/nrf54l15-app"]
_nrf54l15 = ["_nrf54l", "_gpio-p1", "_gpio-p2"]
_nrf54l = ["_dppi"]

_nrf9160 = ["nrf-pac/nrf9160", "_dppi"]
_nrf9120 = ["nrf-pac/nrf9120", "_dppi"]
_nrf52 = ["_ppi"]
_nrf51 = ["_ppi"]
_nrf91 = []

_time-driver = ["dep:embassy-time-driver", "embassy-time-driver?/tick-hz-32_768", "dep:embassy-time-queue-utils", "embassy-embedded-hal/time"]

_net-driver = ["dep:embassy-net-driver-channel","dep:embassy-futures"]

# trustzone state.
_s = []
_ns = []

_ppi = []
_dppi = []
_gpio-p1 = []
_gpio-p2 = []

# Errata workarounds
_nrf52832_anomaly_109 = []

# watchdog timer
_multi_wdt = []

[dependencies]
embassy-time-driver = { version = "0.2.1", path = "../embassy-time-driver", optional = true }
embassy-time-queue-utils = { version = "0.3.0", path = "../embassy-time-queue-utils", optional = true }
embassy-time = { version = "0.5.0", path = "../embassy-time", optional = true }
embassy-sync = { version = "0.7.2", path = "../embassy-sync" }
embassy-hal-internal = { version = "0.3.0", path = "../embassy-hal-internal", features = ["cortex-m", "prio-bits-3"] }
embassy-embedded-hal = { version = "0.5.0", path = "../embassy-embedded-hal", default-features = false }
embassy-usb-driver = { version = "0.2.0", path = "../embassy-usb-driver" }
embassy-net-driver-channel = { version = "0.3.2", path = "../embassy-net-driver-channel", optional = true}
embassy-futures = { version = "0.1.2", path = "../embassy-futures", optional = true}

embedded-hal-02 = { package = "embedded-hal", version = "0.2.6", features = ["unproven"] }
embedded-hal-1 = { package = "embedded-hal", version = "1.0" }
embedded-hal-async = { version = "1.0" }
embedded-io = { version = "0.6.0" }
embedded-io-async = { version = "0.6.1" }

rand-core-06 = { package = "rand_core", version = "0.6" }
rand-core-09 = { package = "rand_core", version = "0.9" }

nrf-pac = "0.1.0"

defmt = { version = "1.0.1", optional = true }
bitflags = "2.4.2"
log = { version = "0.4.14", optional = true }
cortex-m-rt = ">=0.6.15,<0.8"
cortex-m = "0.7.6"
critical-section = "1.1"
fixed = "1.10.0"
embedded-storage = "0.3.1"
embedded-storage-async = "0.4.1"
cfg-if = "1.0.0"
document-features = "0.2.7"
