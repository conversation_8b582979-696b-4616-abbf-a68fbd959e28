/// Peripheral Access Crate
#[allow(unused_imports)]
#[rustfmt::skip]
pub mod pac {
    pub use nrf_pac::*;

    #[cfg(feature = "_ns")]
    #[doc(no_inline)]
    pub use nrf_pac::{
        CLOCK_NS as CLOCK,
        DPPIC_NS as DPPIC,
        EGU0_NS as EGU0,
        EGU1_NS as EGU1,
        EGU2_NS as EGU2,
        EGU3_NS as EGU3,
        EGU4_NS as EGU4,
        EGU5_NS as EGU5,
        FPU_NS as FPU,
        GPIOTE1_NS as GPIOTE1,
        I2S_NS as I2S,
        IPC_NS as IPC,
        KMU_NS as KMU,
        NVMC_NS as NVMC,
        P0_NS as P0,
        PDM_NS as PDM,
        POWER_NS as POWER,
        PWM0_NS as PWM0,
        PWM1_NS as PWM1,
        PWM2_NS as PWM2,
        PWM3_NS as PWM3,
        REGULATORS_NS as REGULATORS,
        RTC0_NS as RTC0,
        RTC1_NS as RTC1,
        SAADC_NS as SAADC,
        SPIM0_NS as SPIM0,
        SPIM1_NS as SPIM1,
        SPIM2_NS as SPIM2,
        SPIM3_NS as SPIM3,
        SPIS0_NS as SPIS0,
        SPIS1_NS as SPIS1,
        SPIS2_NS as SPIS2,
        SPIS3_NS as SPIS3,
        TIMER0_NS as TIMER0,
        TIMER1_NS as TIMER1,
        TIMER2_NS as TIMER2,
        TWIM0_NS as TWIM0,
        TWIM1_NS as TWIM1,
        TWIM2_NS as TWIM2,
        TWIM3_NS as TWIM3,
        TWIS0_NS as TWIS0,
        TWIS1_NS as TWIS1,
        TWIS2_NS as TWIS2,
        TWIS3_NS as TWIS3,
        UARTE0_NS as UARTE0,
        UARTE1_NS as UARTE1,
        UARTE2_NS as UARTE2,
        UARTE3_NS as UARTE3,
        VMC_NS as VMC,
        WDT_NS as WDT,
    };

    #[cfg(feature = "_s")]
    #[doc(no_inline)]
    pub use nrf_pac::{
        CC_HOST_RGF_S as CC_HOST_RGF,
        CLOCK_S as CLOCK,
        CRYPTOCELL_S as CRYPTOCELL,
        CTRL_AP_PERI_S as CTRL_AP_PERI,
        DPPIC_S as DPPIC,
        EGU0_S as EGU0,
        EGU1_S as EGU1,
        EGU2_S as EGU2,
        EGU3_S as EGU3,
        EGU4_S as EGU4,
        EGU5_S as EGU5,
        FICR_S as FICR,
        FPU_NS as FPU,
        GPIOTE0_S as GPIOTE0,
        I2S_S as I2S,
        IPC_S as IPC,
        KMU_S as KMU,
        NVMC_S as NVMC,
        P0_S as P0,
        PDM_S as PDM,
        POWER_S as POWER,
        PWM0_S as PWM0,
        PWM1_S as PWM1,
        PWM2_S as PWM2,
        PWM3_S as PWM3,
        REGULATORS_S as REGULATORS,
        RTC0_S as RTC0,
        RTC1_S as RTC1,
        SAADC_S as SAADC,
        SPIM0_S as SPIM0,
        SPIM1_S as SPIM1,
        SPIM2_S as SPIM2,
        SPIM3_S as SPIM3,
        SPIS0_S as SPIS0,
        SPIS1_S as SPIS1,
        SPIS2_S as SPIS2,
        SPIS3_S as SPIS3,
        SPU_S as SPU,
        TAD_S as TAD,
        TIMER0_S as TIMER0,
        TIMER1_S as TIMER1,
        TIMER2_S as TIMER2,
        TWIM0_S as TWIM0,
        TWIM1_S as TWIM1,
        TWIM2_S as TWIM2,
        TWIM3_S as TWIM3,
        TWIS0_S as TWIS0,
        TWIS1_S as TWIS1,
        TWIS2_S as TWIS2,
        TWIS3_S as TWIS3,
        UARTE0_S as UARTE0,
        UARTE1_S as UARTE1,
        UARTE2_S as UARTE2,
        UARTE3_S as UARTE3,
        UICR_S as UICR,
        VMC_S as VMC,
        WDT_S as WDT,
    };
}

/// The maximum buffer size that the EasyDMA can send/recv in one operation.
pub const EASY_DMA_SIZE: usize = (1 << 13) - 1;
pub const FORCE_COPY_BUFFER_SIZE: usize = 1024;

pub const FLASH_SIZE: usize = 1024 * 1024;

embassy_hal_internal::peripherals! {
    // RTC
    RTC0,
    RTC1,

    // WDT
    WDT,

    // NVMC
    NVMC,

    // UARTE, TWI & SPI
    SERIAL0,
    SERIAL1,
    SERIAL2,
    SERIAL3,

    // SAADC
    SAADC,

    // PWM
    PWM0,
    PWM1,
    PWM2,
    PWM3,

    // TIMER
    TIMER0,
    TIMER1,
    TIMER2,

    // GPIOTE
    GPIOTE_CH0,
    GPIOTE_CH1,
    GPIOTE_CH2,
    GPIOTE_CH3,
    GPIOTE_CH4,
    GPIOTE_CH5,
    GPIOTE_CH6,
    GPIOTE_CH7,

    // PPI
    PPI_CH0,
    PPI_CH1,
    PPI_CH2,
    PPI_CH3,
    PPI_CH4,
    PPI_CH5,
    PPI_CH6,
    PPI_CH7,
    PPI_CH8,
    PPI_CH9,
    PPI_CH10,
    PPI_CH11,
    PPI_CH12,
    PPI_CH13,
    PPI_CH14,
    PPI_CH15,

    PPI_GROUP0,
    PPI_GROUP1,
    PPI_GROUP2,
    PPI_GROUP3,
    PPI_GROUP4,
    PPI_GROUP5,

    // GPIO port 0
    P0_00,
    P0_01,
    P0_02,
    P0_03,
    P0_04,
    P0_05,
    P0_06,
    P0_07,
    P0_08,
    P0_09,
    P0_10,
    P0_11,
    P0_12,
    P0_13,
    P0_14,
    P0_15,
    P0_16,
    P0_17,
    P0_18,
    P0_19,
    P0_20,
    P0_21,
    P0_22,
    P0_23,
    P0_24,
    P0_25,
    P0_26,
    P0_27,
    P0_28,
    P0_29,
    P0_30,
    P0_31,

    // PDM
    PDM,

    // EGU
    EGU0,
    EGU1,
    EGU2,
    EGU3,
    EGU4,
    EGU5,
}

impl_uarte!(SERIAL0, UARTE0, SERIAL0);
impl_uarte!(SERIAL1, UARTE1, SERIAL1);
impl_uarte!(SERIAL2, UARTE2, SERIAL2);
impl_uarte!(SERIAL3, UARTE3, SERIAL3);

impl_spim!(SERIAL0, SPIM0, SERIAL0);
impl_spim!(SERIAL1, SPIM1, SERIAL1);
impl_spim!(SERIAL2, SPIM2, SERIAL2);
impl_spim!(SERIAL3, SPIM3, SERIAL3);

impl_spis!(SERIAL0, SPIS0, SERIAL0);
impl_spis!(SERIAL1, SPIS1, SERIAL1);
impl_spis!(SERIAL2, SPIS2, SERIAL2);
impl_spis!(SERIAL3, SPIS3, SERIAL3);

impl_twim!(SERIAL0, TWIM0, SERIAL0);
impl_twim!(SERIAL1, TWIM1, SERIAL1);
impl_twim!(SERIAL2, TWIM2, SERIAL2);
impl_twim!(SERIAL3, TWIM3, SERIAL3);

impl_twis!(SERIAL0, TWIS0, SERIAL0);
impl_twis!(SERIAL1, TWIS1, SERIAL1);
impl_twis!(SERIAL2, TWIS2, SERIAL2);
impl_twis!(SERIAL3, TWIS3, SERIAL3);

impl_pwm!(PWM0, PWM0, PWM0);
impl_pwm!(PWM1, PWM1, PWM1);
impl_pwm!(PWM2, PWM2, PWM2);
impl_pwm!(PWM3, PWM3, PWM3);

impl_pdm!(PDM, PDM, PDM);

impl_timer!(TIMER0, TIMER0, TIMER0);
impl_timer!(TIMER1, TIMER1, TIMER1);
impl_timer!(TIMER2, TIMER2, TIMER2);

impl_pin!(P0_00, 0, 0);
impl_pin!(P0_01, 0, 1);
impl_pin!(P0_02, 0, 2);
impl_pin!(P0_03, 0, 3);
impl_pin!(P0_04, 0, 4);
impl_pin!(P0_05, 0, 5);
impl_pin!(P0_06, 0, 6);
impl_pin!(P0_07, 0, 7);
impl_pin!(P0_08, 0, 8);
impl_pin!(P0_09, 0, 9);
impl_pin!(P0_10, 0, 10);
impl_pin!(P0_11, 0, 11);
impl_pin!(P0_12, 0, 12);
impl_pin!(P0_13, 0, 13);
impl_pin!(P0_14, 0, 14);
impl_pin!(P0_15, 0, 15);
impl_pin!(P0_16, 0, 16);
impl_pin!(P0_17, 0, 17);
impl_pin!(P0_18, 0, 18);
impl_pin!(P0_19, 0, 19);
impl_pin!(P0_20, 0, 20);
impl_pin!(P0_21, 0, 21);
impl_pin!(P0_22, 0, 22);
impl_pin!(P0_23, 0, 23);
impl_pin!(P0_24, 0, 24);
impl_pin!(P0_25, 0, 25);
impl_pin!(P0_26, 0, 26);
impl_pin!(P0_27, 0, 27);
impl_pin!(P0_28, 0, 28);
impl_pin!(P0_29, 0, 29);
impl_pin!(P0_30, 0, 30);
impl_pin!(P0_31, 0, 31);

impl_ppi_channel!(PPI_CH0, 0 => configurable);
impl_ppi_channel!(PPI_CH1, 1 => configurable);
impl_ppi_channel!(PPI_CH2, 2 => configurable);
impl_ppi_channel!(PPI_CH3, 3 => configurable);
impl_ppi_channel!(PPI_CH4, 4 => configurable);
impl_ppi_channel!(PPI_CH5, 5 => configurable);
impl_ppi_channel!(PPI_CH6, 6 => configurable);
impl_ppi_channel!(PPI_CH7, 7 => configurable);
impl_ppi_channel!(PPI_CH8, 8 => configurable);
impl_ppi_channel!(PPI_CH9, 9 => configurable);
impl_ppi_channel!(PPI_CH10, 10 => configurable);
impl_ppi_channel!(PPI_CH11, 11 => configurable);
impl_ppi_channel!(PPI_CH12, 12 => configurable);
impl_ppi_channel!(PPI_CH13, 13 => configurable);
impl_ppi_channel!(PPI_CH14, 14 => configurable);
impl_ppi_channel!(PPI_CH15, 15 => configurable);

impl_saadc_input!(P0_13, ANALOG_INPUT0);
impl_saadc_input!(P0_14, ANALOG_INPUT1);
impl_saadc_input!(P0_15, ANALOG_INPUT2);
impl_saadc_input!(P0_16, ANALOG_INPUT3);
impl_saadc_input!(P0_17, ANALOG_INPUT4);
impl_saadc_input!(P0_18, ANALOG_INPUT5);
impl_saadc_input!(P0_19, ANALOG_INPUT6);
impl_saadc_input!(P0_20, ANALOG_INPUT7);

impl_egu!(EGU0, EGU0, EGU0);
impl_egu!(EGU1, EGU1, EGU1);
impl_egu!(EGU2, EGU2, EGU2);
impl_egu!(EGU3, EGU3, EGU3);
impl_egu!(EGU4, EGU4, EGU4);
impl_egu!(EGU5, EGU5, EGU5);

impl_wdt!(WDT, WDT, WDT, 0);

embassy_hal_internal::interrupt_mod!(
    SPU,
    CLOCK_POWER,
    SERIAL0,
    SERIAL1,
    SERIAL2,
    SERIAL3,
    GPIOTE0,
    SAADC,
    TIMER0,
    TIMER1,
    TIMER2,
    RTC0,
    RTC1,
    WDT,
    EGU0,
    EGU1,
    EGU2,
    EGU3,
    EGU4,
    EGU5,
    PWM0,
    PWM1,
    PWM2,
    PWM3,
    PDM,
    I2S,
    IPC,
    FPU,
    GPIOTE1,
    KMU,
    CRYPTOCELL,
);
