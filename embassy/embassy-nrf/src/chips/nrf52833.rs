pub use nrf_pac as pac;

/// The maximum buffer size that the EasyDMA can send/recv in one operation.
pub const EASY_DMA_SIZE: usize = (1 << 16) - 1;
pub const FORCE_COPY_BUFFER_SIZE: usize = 512;

pub const FLASH_SIZE: usize = 512 * 1024;

pub const RESET_PIN: u32 = 18;
pub const APPROTECT_MIN_BUILD_CODE: u8 = b'B';

embassy_hal_internal::peripherals! {
    // USB
    USBD,

    // RTC
    RTC0,
    RTC1,
    RTC2,

    // WDT
    WDT,

    // NVMC
    NVMC,

    // RNG
    RNG,

    // UARTE
    UARTE0,
    UARTE1,

    // SPI/TWI
    TWISPI0,
    TWISPI1,
    SPI2,
    SPI3,

    // SAADC
    SAADC,

    // PWM
    PWM0,
    PWM1,
    PWM2,
    PWM3,

    // TIMER
    TIMER0,
    TIMER1,
    TIMER2,
    TIMER3,
    TIMER4,

    // GPIOTE
    GPIOTE_CH0,
    GPIOTE_CH1,
    GPIOTE_CH2,
    GPIOTE_CH3,
    GPIOTE_CH4,
    GPIOTE_CH5,
    GPIOTE_CH6,
    GPIOTE_CH7,

    // PPI
    PPI_CH0,
    PPI_CH1,
    PPI_CH2,
    PPI_CH3,
    PPI_CH4,
    PPI_CH5,
    PPI_CH6,
    PPI_CH7,
    PPI_CH8,
    PPI_CH9,
    PPI_CH10,
    PPI_CH11,
    PPI_CH12,
    PPI_CH13,
    PPI_CH14,
    PPI_CH15,
    PPI_CH16,
    PPI_CH17,
    PPI_CH18,
    PPI_CH19,
    PPI_CH20,
    PPI_CH21,
    PPI_CH22,
    PPI_CH23,
    PPI_CH24,
    PPI_CH25,
    PPI_CH26,
    PPI_CH27,
    PPI_CH28,
    PPI_CH29,
    PPI_CH30,
    PPI_CH31,

    PPI_GROUP0,
    PPI_GROUP1,
    PPI_GROUP2,
    PPI_GROUP3,
    PPI_GROUP4,
    PPI_GROUP5,

    // GPIO port 0
    P0_00,
    P0_01,
    P0_02,
    P0_03,
    P0_04,
    P0_05,
    P0_06,
    P0_07,
    P0_08,
    #[cfg(feature = "nfc-pins-as-gpio")]
    P0_09,
    #[cfg(feature = "nfc-pins-as-gpio")]
    P0_10,
    P0_11,
    P0_12,
    P0_13,
    P0_14,
    P0_15,
    P0_16,
    P0_17,
    #[cfg(feature="reset-pin-as-gpio")]
    P0_18,
    P0_19,
    P0_20,
    P0_21,
    P0_22,
    P0_23,
    P0_24,
    P0_25,
    P0_26,
    P0_27,
    P0_28,
    P0_29,
    P0_30,
    P0_31,

    // GPIO port 1
    P1_00,
    P1_01,
    P1_02,
    P1_03,
    P1_04,
    P1_05,
    P1_06,
    P1_07,
    P1_08,
    P1_09,
    P1_10,
    P1_11,
    P1_12,
    P1_13,
    P1_14,
    P1_15,

    // TEMP
    TEMP,

    // QDEC
    QDEC,

    // PDM
    PDM,

    // I2S
    I2S,

    // Radio
    RADIO,

    // EGU
    EGU0,
    EGU1,
    EGU2,
    EGU3,
    EGU4,
    EGU5,

    // NFC
    NFCT,
}

impl_usb!(USBD, USBD, USBD);

impl_uarte!(UARTE0, UARTE0, UARTE0);
impl_uarte!(UARTE1, UARTE1, UARTE1);

impl_spim!(TWISPI0, SPIM0, TWISPI0);
impl_spim!(TWISPI1, SPIM1, TWISPI1);
impl_spim!(SPI2, SPIM2, SPI2);
impl_spim!(SPI3, SPIM3, SPIM3);

impl_spis!(TWISPI0, SPIS0, TWISPI0);
impl_spis!(TWISPI1, SPIS1, TWISPI1);
impl_spis!(SPI2, SPIS2, SPI2);

impl_twim!(TWISPI0, TWIM0, TWISPI0);
impl_twim!(TWISPI1, TWIM1, TWISPI1);

impl_twis!(TWISPI0, TWIS0, TWISPI0);
impl_twis!(TWISPI1, TWIS1, TWISPI1);

impl_pwm!(PWM0, PWM0, PWM0);
impl_pwm!(PWM1, PWM1, PWM1);
impl_pwm!(PWM2, PWM2, PWM2);
impl_pwm!(PWM3, PWM3, PWM3);

impl_pdm!(PDM, PDM, PDM);

impl_qdec!(QDEC, QDEC, QDEC);

impl_rng!(RNG, RNG, RNG);

impl_timer!(TIMER0, TIMER0, TIMER0);
impl_timer!(TIMER1, TIMER1, TIMER1);
impl_timer!(TIMER2, TIMER2, TIMER2);
impl_timer!(TIMER3, TIMER3, TIMER3, extended);
impl_timer!(TIMER4, TIMER4, TIMER4, extended);

impl_pin!(P0_00, 0, 0);
impl_pin!(P0_01, 0, 1);
impl_pin!(P0_02, 0, 2);
impl_pin!(P0_03, 0, 3);
impl_pin!(P0_04, 0, 4);
impl_pin!(P0_05, 0, 5);
impl_pin!(P0_06, 0, 6);
impl_pin!(P0_07, 0, 7);
impl_pin!(P0_08, 0, 8);
#[cfg(feature = "nfc-pins-as-gpio")]
impl_pin!(P0_09, 0, 9);
#[cfg(feature = "nfc-pins-as-gpio")]
impl_pin!(P0_10, 0, 10);
impl_pin!(P0_11, 0, 11);
impl_pin!(P0_12, 0, 12);
impl_pin!(P0_13, 0, 13);
impl_pin!(P0_14, 0, 14);
impl_pin!(P0_15, 0, 15);
impl_pin!(P0_16, 0, 16);
impl_pin!(P0_17, 0, 17);
#[cfg(feature = "reset-pin-as-gpio")]
impl_pin!(P0_18, 0, 18);
impl_pin!(P0_19, 0, 19);
impl_pin!(P0_20, 0, 20);
impl_pin!(P0_21, 0, 21);
impl_pin!(P0_22, 0, 22);
impl_pin!(P0_23, 0, 23);
impl_pin!(P0_24, 0, 24);
impl_pin!(P0_25, 0, 25);
impl_pin!(P0_26, 0, 26);
impl_pin!(P0_27, 0, 27);
impl_pin!(P0_28, 0, 28);
impl_pin!(P0_29, 0, 29);
impl_pin!(P0_30, 0, 30);
impl_pin!(P0_31, 0, 31);

impl_pin!(P1_00, 1, 0);
impl_pin!(P1_01, 1, 1);
impl_pin!(P1_02, 1, 2);
impl_pin!(P1_03, 1, 3);
impl_pin!(P1_04, 1, 4);
impl_pin!(P1_05, 1, 5);
impl_pin!(P1_06, 1, 6);
impl_pin!(P1_07, 1, 7);
impl_pin!(P1_08, 1, 8);
impl_pin!(P1_09, 1, 9);
impl_pin!(P1_10, 1, 10);
impl_pin!(P1_11, 1, 11);
impl_pin!(P1_12, 1, 12);
impl_pin!(P1_13, 1, 13);
impl_pin!(P1_14, 1, 14);
impl_pin!(P1_15, 1, 15);

impl_ppi_channel!(PPI_CH0, 0 => configurable);
impl_ppi_channel!(PPI_CH1, 1 => configurable);
impl_ppi_channel!(PPI_CH2, 2 => configurable);
impl_ppi_channel!(PPI_CH3, 3 => configurable);
impl_ppi_channel!(PPI_CH4, 4 => configurable);
impl_ppi_channel!(PPI_CH5, 5 => configurable);
impl_ppi_channel!(PPI_CH6, 6 => configurable);
impl_ppi_channel!(PPI_CH7, 7 => configurable);
impl_ppi_channel!(PPI_CH8, 8 => configurable);
impl_ppi_channel!(PPI_CH9, 9 => configurable);
impl_ppi_channel!(PPI_CH10, 10 => configurable);
impl_ppi_channel!(PPI_CH11, 11 => configurable);
impl_ppi_channel!(PPI_CH12, 12 => configurable);
impl_ppi_channel!(PPI_CH13, 13 => configurable);
impl_ppi_channel!(PPI_CH14, 14 => configurable);
impl_ppi_channel!(PPI_CH15, 15 => configurable);
impl_ppi_channel!(PPI_CH16, 16 => configurable);
impl_ppi_channel!(PPI_CH17, 17 => configurable);
impl_ppi_channel!(PPI_CH18, 18 => configurable);
impl_ppi_channel!(PPI_CH19, 19 => configurable);
impl_ppi_channel!(PPI_CH20, 20 => static);
impl_ppi_channel!(PPI_CH21, 21 => static);
impl_ppi_channel!(PPI_CH22, 22 => static);
impl_ppi_channel!(PPI_CH23, 23 => static);
impl_ppi_channel!(PPI_CH24, 24 => static);
impl_ppi_channel!(PPI_CH25, 25 => static);
impl_ppi_channel!(PPI_CH26, 26 => static);
impl_ppi_channel!(PPI_CH27, 27 => static);
impl_ppi_channel!(PPI_CH28, 28 => static);
impl_ppi_channel!(PPI_CH29, 29 => static);
impl_ppi_channel!(PPI_CH30, 30 => static);
impl_ppi_channel!(PPI_CH31, 31 => static);

impl_saadc_input!(P0_02, ANALOG_INPUT0);
impl_saadc_input!(P0_03, ANALOG_INPUT1);
impl_saadc_input!(P0_04, ANALOG_INPUT2);
impl_saadc_input!(P0_05, ANALOG_INPUT3);
impl_saadc_input!(P0_28, ANALOG_INPUT4);
impl_saadc_input!(P0_29, ANALOG_INPUT5);
impl_saadc_input!(P0_30, ANALOG_INPUT6);
impl_saadc_input!(P0_31, ANALOG_INPUT7);

impl_i2s!(I2S, I2S, I2S);

impl_radio!(RADIO, RADIO, RADIO);

impl_egu!(EGU0, EGU0, EGU0_SWI0);
impl_egu!(EGU1, EGU1, EGU1_SWI1);
impl_egu!(EGU2, EGU2, EGU2_SWI2);
impl_egu!(EGU3, EGU3, EGU3_SWI3);
impl_egu!(EGU4, EGU4, EGU4_SWI4);
impl_egu!(EGU5, EGU5, EGU5_SWI5);

impl_wdt!(WDT, WDT, WDT, 0);

embassy_hal_internal::interrupt_mod!(
    CLOCK_POWER,
    RADIO,
    UARTE0,
    TWISPI0,
    TWISPI1,
    NFCT,
    GPIOTE,
    SAADC,
    TIMER0,
    TIMER1,
    TIMER2,
    RTC0,
    TEMP,
    RNG,
    ECB,
    AAR_CCM,
    WDT,
    RTC1,
    QDEC,
    COMP_LPCOMP,
    EGU0_SWI0,
    EGU1_SWI1,
    EGU2_SWI2,
    EGU3_SWI3,
    EGU4_SWI4,
    EGU5_SWI5,
    TIMER3,
    TIMER4,
    PWM0,
    PDM,
    MWU,
    PWM1,
    PWM2,
    SPI2,
    RTC2,
    I2S,
    FPU,
    USBD,
    UARTE1,
    PWM3,
    SPIM3,
);
