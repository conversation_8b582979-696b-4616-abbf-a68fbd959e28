```mermaid
---
config:
  theme: redux-dark
  layout: elk
---
stateDiagram
  direction TB
  [*] --> On_Ground
  On_Ground --> On_Launcher:Hall effect sensor closed
  On_Launcher --> On_Ground:Hall effect sensor open
  On_Launcher --> Ready_To_Fly:[UART] Prepare for flight request | succeeds ONLY when the safety pin is pulled out
  Ready_To_Fly --> In_Flight:Hall effect sensor open
  In_Flight --> Armed:[UART] Arm request
  Armed --> In_Flight:[UART] Disarm request
  Armed --> [*]:[UART] Detonate request
  Ready_To_Fly --> On_Ground:[UART] Reset request
  In_Flight --> On_Ground:[UART] Reset request
  note right of On_Launcher: The Hall effect sensor is continuously monitored.<br/>This means that if the plane is reset while the sensor is closed,<br/>it will return to this state automatically.<br/>No opening or closing of the sensor is required.
```