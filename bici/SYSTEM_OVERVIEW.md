# BICI - Detonator State Machine System

## Overview

BICI is an embedded Rust application designed for a **detonator control system** that manages the lifecycle of an explosive device from ground preparation through flight to detonation. The system runs on an STM32H503CB microcontroller and implements a type-safe state machine with UART communication capabilities.

## System Purpose

This is a **safety-critical embedded system** for controlling explosive devices. The system ensures that detonation can only occur under very specific, controlled conditions through a series of safety checks and state transitions.

## Architecture Overview

The system is built using **Embassy** (async Rust embedded framework) and consists of several key components:

### Core Components

1. **State Machine** (`state_machine.rs`) - Type-safe state management using <PERSON>ust's type system
2. **Protocol Handler** (`protocol.rs`) - UART communication with CRC validation and COBS encoding
3. **Main Application** (`main.rs`) - Task orchestration and hardware initialization
4. **Hardware Interfaces** - Hall effect sensor, safety pin, UART communication

### Key Design Principles

- **Type Safety**: Uses Rust's type system to make invalid state transitions impossible at compile time
- **Sensor-Driven**: Sensors are treated as continuous properties, not events, to prevent desynchronization
- **Fail-Safe**: Multiple safety mechanisms prevent accidental detonation
- **Real-time**: Embassy async framework provides deterministic timing

## State Machine Design

### States and Transitions

The system implements 6 distinct states with specific transition rules:

```
[*] → OnGround → OnLauncher → ReadyToFly → InFlight → Armed → [Detonated]
```

#### State Descriptions

1. **OnGround** (0x00)
   - Initial state when device is not on launcher
   - No commands accepted
   - Transitions to OnLauncher when hall effect sensor closes

2. **OnLauncher** (0x01) 
   - Device is physically mounted on launcher (hall effect sensor closed)
   - Accepts: `PrepareForFlight` command (requires safety pin pulled)
   - Auto-transitions back to OnGround if hall effect opens

3. **ReadyToFly** (0x02)
   - Device is armed and ready for launch
   - Safety pin must be pulled to reach this state
   - Accepts: `ResetRequest` command
   - Auto-transitions to InFlight when hall effect opens (launch detected)

4. **InFlight** (0x03)
   - Device has been launched (hall effect sensor open)
   - Accepts: `ArmRequest`, `ResetRequest` commands
   - No automatic transitions

5. **Armed** (0x04)
   - Device is armed and ready for detonation
   - Accepts: `DisarmRequest`, `DetonateRequest` commands
   - No automatic transitions

6. **Detonated** (0xFF)
   - Terminal state - no further transitions possible
   - System has executed detonation sequence

### Safety Mechanisms

1. **Physical Safety Pin**: Must be pulled before flight preparation
2. **Hall Effect Sensor**: Detects launcher mounting and launch events
3. **Command Validation**: Only specific commands accepted in each state
4. **Type Safety**: Rust compiler prevents invalid state transitions
5. **Multi-step Process**: Multiple deliberate actions required for detonation

## Hardware Interface

### Sensors
- **Hall Effect Sensor** (PA3): Detects when device is mounted on launcher
- **Safety Pin** (PA4): Physical safety mechanism that must be pulled
- Both sensors use pull-up resistors, active-low logic

### Communication
- **UART** (USART1, PA1/PA2): Bidirectional communication with host system
- **Protocol**: Custom binary protocol with CRC8 validation and COBS encoding

## Communication Protocol

### Message Types

#### Outgoing (Device → Host)
- **Heartbeat** (0x01): Periodic status updates (100ms interval)
  - Contains: current state, sensor status, sequence number

#### Incoming (Host → Device)  
- **PrepareForFlight** (0x10): Transition from OnLauncher to ReadyToFly
- **ArmRequest** (0x11): Transition from InFlight to Armed
- **DisarmRequest** (0x12): Transition from Armed to InFlight
- **DetonateRequest** (0x13): Transition from Armed to Detonated
- **ResetRequest** (0x14): Return to OnGround from certain states

### Protocol Format
```
[MSG_TYPE][PAYLOAD][CRC8] → COBS_ENCODE → [DATA][0x00]
```

- **CRC8**: Data integrity validation using polynomial 0xD5
- **COBS**: Consistent Overhead Byte Stuffing for reliable framing
- **Zero Termination**: Frames end with 0x00 byte

## Task Architecture

The system runs 5 concurrent async tasks:

1. **UART Reader**: Receives and decodes incoming commands
2. **UART Writer**: Sends encoded messages to host
3. **Sensor Monitor**: Continuously monitors hardware sensors (50ms polling)
4. **State Machine**: Processes commands and sensor updates
5. **Heartbeat**: Sends periodic status updates (100ms interval)

### Inter-Task Communication
- **Channels**: Embassy channels for message passing between tasks
- **Command Channel**: Commands from UART to state machine
- **Sensor Channel**: Sensor updates to state machine  
- **TX Channel**: Outgoing messages to UART writer

## Current Implementation Issues

Based on the code analysis, there are several compilation and logic issues:

1. **Missing Imports**: `STATE_MACHINE` and `CURRENT_STATE_SIGNAL` referenced but not defined
2. **Type Mismatches**: Return type inconsistencies in state machine methods
3. **Incomplete Protocol**: `MAX_ENCODED_SIZE` not properly exported
4. **Logic Errors**: Some state transition logic appears incorrect

## Technology Stack

- **Language**: Rust (no_std embedded)
- **Framework**: Embassy async embedded framework
- **Target**: STM32H503CB microcontroller (Cortex-M33)
- **Key Crates**:
  - `embassy-stm32`: Hardware abstraction
  - `embassy-sync`: Async synchronization primitives
  - `defmt`: Efficient logging for embedded
  - `heapless`: Collections without heap allocation
  - `cobs`: Consistent Overhead Byte Stuffing
  - `crc`: CRC calculation

## Safety Considerations

This is a **safety-critical system** with the following design considerations:

- **Multiple Safety Barriers**: Physical pin + sensor + command sequence
- **Fail-Safe Design**: System defaults to safe states
- **Type Safety**: Compile-time prevention of invalid operations
- **Continuous Monitoring**: Real-time sensor feedback
- **Deterministic Behavior**: Predictable state transitions
- **Communication Integrity**: CRC validation prevents corruption

The system is designed to prevent accidental detonation while ensuring reliable operation when intentionally triggered through the proper sequence of safety checks and commands.
