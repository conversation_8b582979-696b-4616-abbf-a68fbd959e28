//! UART Communication Protocol
//!
//! Implements a robust binary protocol for communication between the detonator
//! and host system using CRC8 validation and COBS encoding for reliability.
//!
//! Protocol Format: [MSG_TYPE][PAYLOAD][CRC8] -> COBS_ENCODE -> [DATA][0x00]
//!
//! Message Types:
//! - Heartbeat (0x01): Device status updates sent every 100ms
//! - Commands (0x10-0x14): Control commands from host
//!
//! Safety Features:
//! - CRC8 validation prevents corruption
//! - COBS encoding ensures reliable framing
//! - Sequence numbers detect dropped messages

use crate::state_machine::{Command, StateId};
use cobs;
use crc::{Algorithm, Crc};
use defmt::Format;
use heapless::Vec;

// ============================================================================
// Protocol Constants
// ============================================================================

/// Maximum size of unencoded frame
const MAX_FRAME_SIZE: usize = 8;

/// Maximum size after COBS encoding (worst case: 2x + 1)
pub const MAX_ENCODED_SIZE: usize = MAX_FRAME_SIZE * 2 + 1;

/// CRC8 algorithm configuration (polynomial 0xD5)
const CRC_ALGORITHM: Algorithm<u8> = Algorithm {
    width: 8,
    poly: 0xD5,
    init: 0x00,
    refin: false,
    refout: false,
    xorout: 0x00,
    check: 0x00,
    residue: 0x00,
};

// ============================================================================
// Message Types
// ============================================================================

/// Protocol message type identifiers
#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum MessageType {
    // Outgoing messages (device -> host)
    Heartbeat = 0x01,

    // Incoming commands (host -> device)
    PrepareForFlight = 0x10,
    ArmRequest = 0x11,
    DisarmRequest = 0x12,
    DetonateRequest = 0x13,
    ResetRequest = 0x14,
}

impl TryFrom<u8> for MessageType {
    type Error = ProtocolError;

    fn try_from(byte: u8) -> Result<Self, Self::Error> {
        match byte {
            0x01 => Ok(MessageType::Heartbeat),
            0x10 => Ok(MessageType::PrepareForFlight),
            0x11 => Ok(MessageType::ArmRequest),
            0x12 => Ok(MessageType::DisarmRequest),
            0x13 => Ok(MessageType::DetonateRequest),
            0x14 => Ok(MessageType::ResetRequest),
            _ => Err(ProtocolError::UnknownMessageType),
        }
    }
}

impl From<Command> for MessageType {
    fn from(command: Command) -> Self {
        match command {
            Command::PrepareForFlight => MessageType::PrepareForFlight,
            Command::ArmRequest => MessageType::ArmRequest,
            Command::DisarmRequest => MessageType::DisarmRequest,
            Command::DetonateRequest => MessageType::DetonateRequest,
            Command::ResetRequest => MessageType::ResetRequest,
        }
    }
}

impl TryFrom<MessageType> for Command {
    type Error = ProtocolError;

    fn try_from(msg_type: MessageType) -> Result<Self, Self::Error> {
        match msg_type {
            MessageType::PrepareForFlight => Ok(Command::PrepareForFlight),
            MessageType::ArmRequest => Ok(Command::ArmRequest),
            MessageType::DisarmRequest => Ok(Command::DisarmRequest),
            MessageType::DetonateRequest => Ok(Command::DetonateRequest),
            MessageType::ResetRequest => Ok(Command::ResetRequest),
            MessageType::Heartbeat => Err(ProtocolError::InvalidCommandMessage),
        }
    }
}

// ============================================================================
// Error Types
// ============================================================================

/// Protocol error types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum ProtocolError {
    /// CRC validation failed
    CrcMismatch,
    /// Frame format is invalid
    InvalidFrame,
    /// Unknown message type received
    UnknownMessageType,
    /// Heartbeat message used as command
    InvalidCommandMessage,
    /// Buffer too small for operation
    BufferOverflow,
    /// COBS decoding failed
    DecodingError,
}

// ============================================================================
// Message Payloads
// ============================================================================

/// Heartbeat message payload structure
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct HeartbeatPayload {
    pub state: StateId,
    pub sensor_flags: u8,  // bit 0: hall_effect, bit 1: safety_pin
    pub sequence: u8,
}

impl HeartbeatPayload {
    pub fn new(state: StateId, hall_effect: bool, safety_pin: bool, sequence: u8) -> Self {
        let sensor_flags = (hall_effect as u8) | ((safety_pin as u8) << 1);
        Self {
            state,
            sensor_flags,
            sequence,
        }
    }


}

// ============================================================================
// Protocol Handler
// ============================================================================

/// Protocol encoder/decoder with sequence tracking
pub struct Protocol {
    sequence_counter: u8,
    crc: Crc<u8>,
}

impl Protocol {
    /// Create new protocol handler
    pub fn new() -> Self {
        Self {
            sequence_counter: 0,
            crc: Crc::<u8>::new(&CRC_ALGORITHM),
        }
    }

    /// Encode a heartbeat message
    pub fn encode_heartbeat(
        &mut self,
        state: StateId,
        hall_effect: bool,
        safety_pin: bool,
    ) -> Vec<u8, MAX_ENCODED_SIZE> {
        // Increment sequence counter
        self.sequence_counter = self.sequence_counter.wrapping_add(1);

        // Create payload
        let payload = HeartbeatPayload::new(state, hall_effect, safety_pin, self.sequence_counter);

        // Build frame
        let mut frame = Vec::<u8, MAX_FRAME_SIZE>::new();
        frame.push(MessageType::Heartbeat as u8).ok();
        frame.push(payload.state as u8).ok();
        frame.push(payload.sensor_flags).ok();
        frame.push(payload.sequence).ok();

        self.encode_frame(frame)
    }

    /// Decode an incoming command message
    pub fn decode_command(&self, encoded_data: &[u8]) -> Result<Command, ProtocolError> {
        // Decode COBS frame
        let frame = self.decode_frame(encoded_data)?;

        // Validate minimum frame size
        if frame.len() < 2 {
            return Err(ProtocolError::InvalidFrame);
        }

        // Extract and validate message type
        let msg_type = MessageType::try_from(frame[0])?;
        
        // Convert to command
        Command::try_from(msg_type)
    }



    // ========================================================================
    // Internal Implementation
    // ========================================================================

    /// Encode frame with CRC and COBS encoding
    fn encode_frame(&self, mut frame: Vec<u8, MAX_FRAME_SIZE>) -> Vec<u8, MAX_ENCODED_SIZE> {
        // Calculate and append CRC
        let crc = self.crc.checksum(&frame);
        frame.push(crc).ok();

        // COBS encode
        let mut encoded_buffer = [0u8; MAX_ENCODED_SIZE];
        let encoded_len = cobs::encode(&frame, &mut encoded_buffer);

        // Add frame terminator
        encoded_buffer[encoded_len] = 0x00;

        // Convert to Vec
        let mut result = Vec::<u8, MAX_ENCODED_SIZE>::new();
        result.extend_from_slice(&encoded_buffer[0..=encoded_len]).ok();
        result
    }

    /// Decode COBS frame and validate CRC
    fn decode_frame(&self, encoded_data: &[u8]) -> Result<Vec<u8, MAX_FRAME_SIZE>, ProtocolError> {
        // Validate frame termination
        if encoded_data.is_empty() || encoded_data.last() != Some(&0x00) {
            return Err(ProtocolError::InvalidFrame);
        }

        // COBS decode (include terminator)
        let mut decode_buffer = [0u8; MAX_FRAME_SIZE];
        let decode_result = cobs::decode(encoded_data, &mut decode_buffer)
            .map_err(|_| ProtocolError::DecodingError)?;
        let decoded_len = decode_result.frame_size();

        // Validate minimum size (at least message type + CRC)
        if decoded_len < 2 {
            return Err(ProtocolError::InvalidFrame);
        }

        // Split data and CRC
        let (data, crc_bytes) = decode_buffer[0..decoded_len].split_at(decoded_len - 1);
        let received_crc = crc_bytes[0];

        // Validate CRC
        let calculated_crc = self.crc.checksum(data);
        if calculated_crc != received_crc {
            return Err(ProtocolError::CrcMismatch);
        }

        // Return validated frame (without CRC)
        let mut frame = Vec::<u8, MAX_FRAME_SIZE>::new();
        frame.extend_from_slice(data)
            .map_err(|_| ProtocolError::BufferOverflow)?;
        
        Ok(frame)
    }
}

impl Default for Protocol {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

/// Create a simple command frame for testing
pub fn encode_command(command: Command) -> Vec<u8, MAX_ENCODED_SIZE> {
    let protocol = Protocol::new();
    let mut frame = Vec::<u8, MAX_FRAME_SIZE>::new();
    frame.push(MessageType::from(command) as u8).ok();
    protocol.encode_frame(frame)
}
