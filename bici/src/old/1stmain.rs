#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::mode::Async;
use embassy_stm32::usart::{Config, Uart, UartRx, UartTx};
use embassy_stm32::{bind_interrupts, peripherals, usart};
use embassy_sync::blocking_mutex::raw::ThreadModeRawMutex;
use embassy_sync::channel::Channel;
use embassy_time::{Duration, Instant, Timer};
use heapless::Vec;
use {defmt_rtt as _, panic_probe as _};

bind_interrupts!(struct Irqs {
    USART1 => usart::InterruptHandler<peripherals::USART1>;
});

// ============================================================================
// Simple Protocol Definition
// ============================================================================

// Message types
#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, defmt::Format)]
pub enum MessageType {
    Heartbeat = 0x01,
    StateChange = 0x02,
    // Incoming commands
    PrepareForFlight = 0x10,
    ArmRequest = 0x11,
    DisarmRequest = 0x12,
    DetonateRequest = 0x13,
    ResetRequest = 0x14,
}

impl From<u8> for MessageType {
    fn from(byte: u8) -> Self {
        match byte {
            0x01 => MessageType::Heartbeat,
            0x02 => MessageType::StateChange,
            0x10 => MessageType::PrepareForFlight,
            0x11 => MessageType::ArmRequest,
            0x12 => MessageType::DisarmRequest,
            0x13 => MessageType::DetonateRequest,
            0x14 => MessageType::ResetRequest,
            _ => MessageType::Heartbeat, // Default fallback
        }
    }
}

// State representation
#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, defmt::Format)]
pub enum StateId {
    OnGround = 0x00,
    OnLauncher = 0x01,
    ReadyToFly = 0x02,
    InFlight = 0x03,
    Armed = 0x04,
    Detonated = 0xFF,
}

// Simple protocol frame: [TYPE][PAYLOAD][CRC8] with COBS encoding
const MAX_FRAME_SIZE: usize = 8;
const MAX_ENCODED_SIZE: usize = 16;

#[derive(Debug, Clone, Copy)]
pub struct HeartbeatPayload {
    pub state: StateId,
    pub flags: u8, // bit 0: hall_effect, bit 1: safety_pin
    pub sequence: u8,
}

#[derive(Debug, Clone, Copy)]
pub struct StateChangePayload {
    pub from_state: StateId,
    pub to_state: StateId,
    pub sequence: u8,
}

pub struct Protocol {
    sequence: u8,
}

impl Protocol {
    pub fn new() -> Self {
        Self { sequence: 0 }
    }

    pub fn encode_heartbeat(
        &mut self,
        state: StateId,
        hall_effect: bool,
        safety_pin: bool,
    ) -> Vec<u8, MAX_ENCODED_SIZE> {
        let flags = (hall_effect as u8) | ((safety_pin as u8) << 1);
        self.sequence = self.sequence.wrapping_add(1);

        let mut frame = Vec::<u8, MAX_FRAME_SIZE>::new();
        frame.push(MessageType::Heartbeat as u8).ok();
        frame.push(state as u8).ok();
        frame.push(flags).ok();
        frame.push(self.sequence).ok();

        self.finalize_frame(frame)
    }

    pub fn encode_state_change(&mut self, from: StateId, to: StateId) -> Vec<u8, MAX_ENCODED_SIZE> {
        self.sequence = self.sequence.wrapping_add(1);

        let mut frame = Vec::<u8, MAX_FRAME_SIZE>::new();
        frame.push(MessageType::StateChange as u8).ok();
        frame.push(from as u8).ok();
        frame.push(to as u8).ok();
        frame.push(self.sequence).ok();

        self.finalize_frame(frame)
    }

    pub fn decode_command(&self, encoded_data: &[u8]) -> Option<MessageType> {
        if let Some(frame) = self.cobs_decode(encoded_data) {
            if self.verify_crc(&frame) && !frame.is_empty() {
                return Some(MessageType::from(frame[0]));
            }
        }
        None
    }

    fn finalize_frame(&self, mut frame: Vec<u8, MAX_FRAME_SIZE>) -> Vec<u8, MAX_ENCODED_SIZE> {
        // Add CRC8
        let crc = self.crc8(&frame);
        frame.push(crc).ok();

        // COBS encode and add delimiter
        self.cobs_encode(&frame)
    }

    fn cobs_encode(&self, data: &[u8]) -> Vec<u8, MAX_ENCODED_SIZE> {
        let mut encoded = Vec::<u8, MAX_ENCODED_SIZE>::new();
        let mut code_ptr = 0;
        let mut code = 1u8;

        encoded.push(0).ok(); // Placeholder for first code

        for &byte in data {
            if byte != 0 {
                encoded.push(byte).ok();
                code = code.wrapping_add(1);
            }

            if byte == 0 || code == 0xFF {
                encoded[code_ptr] = code;
                code_ptr = encoded.len();
                encoded.push(0).ok(); // Placeholder
                code = 1;
            }
        }

        encoded[code_ptr] = code;
        encoded.push(0).ok(); // Frame delimiter
        encoded
    }

    fn cobs_decode(&self, data: &[u8]) -> Option<Vec<u8, MAX_FRAME_SIZE>> {
        let mut decoded = Vec::<u8, MAX_FRAME_SIZE>::new();
        let mut i = 0;

        while i < data.len() {
            let code = data[i];
            if code == 0 {
                break; // End delimiter
            }

            i += 1;
            for _ in 1..code {
                if i >= data.len() {
                    return None;
                }
                decoded.push(data[i]).ok()?;
                i += 1;
            }

            if code != 0xFF && i < data.len() && data[i] != 0 {
                decoded.push(0).ok()?;
            }
        }

        Some(decoded)
    }

    fn verify_crc(&self, frame: &[u8]) -> bool {
        if frame.len() < 2 {
            return false;
        }
        let (data, crc_byte) = frame.split_at(frame.len() - 1);
        self.crc8(data) == crc_byte[0]
    }

    fn crc8(&self, data: &[u8]) -> u8 {
        const POLY: u8 = 0xD5;
        let mut crc = 0u8;

        for &byte in data {
            crc ^= byte;
            for _ in 0..8 {
                if crc & 0x80 != 0 {
                    crc = (crc << 1) ^ POLY;
                } else {
                    crc <<= 1;
                }
            }
        }
        crc
    }
}

// ============================================================================
// State Machine (Simplified)
// ============================================================================

#[derive(Debug, Clone, Copy, PartialEq, defmt::Format)]
pub enum UartCommand {
    PrepareForFlight,
    ArmRequest,
    DisarmRequest,
    DetonateRequest,
    ResetRequest,
}

impl From<MessageType> for UartCommand {
    fn from(msg_type: MessageType) -> Self {
        match msg_type {
            MessageType::PrepareForFlight => UartCommand::PrepareForFlight,
            MessageType::ArmRequest => UartCommand::ArmRequest,
            MessageType::DisarmRequest => UartCommand::DisarmRequest,
            MessageType::DetonateRequest => UartCommand::DetonateRequest,
            MessageType::ResetRequest => UartCommand::ResetRequest,
            _ => UartCommand::ResetRequest, // Fallback
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, defmt::Format)]
pub enum SensorEvent {
    HallEffectClosed,
    HallEffectOpen,
    SafetyPinPulled,
    SafetyPinInserted,
}

#[derive(Debug, Clone, Copy, PartialEq, defmt::Format)]
pub enum Event {
    Uart(UartCommand),
    Sensor(SensorEvent),
}

// State types
#[derive(Debug, Clone, Copy)]
pub struct OnGround;
#[derive(Debug, Clone, Copy)]
pub struct OnLauncher;
#[derive(Debug, Clone, Copy)]
pub struct ReadyToFly;
#[derive(Debug, Clone, Copy)]
pub struct InFlight;
#[derive(Debug, Clone, Copy)]
pub struct Armed;
#[derive(Debug, Clone, Copy)]
pub struct Detonated;

#[derive(Debug, Clone, Copy)]
pub enum CurrentState {
    OnGround(OnGround),
    OnLauncher(OnLauncher),
    ReadyToFly(ReadyToFly),
    InFlight(InFlight),
    Armed(Armed),
    Detonated(Detonated),
}

impl CurrentState {
    pub fn to_state_id(&self) -> StateId {
        match self {
            CurrentState::OnGround(_) => StateId::OnGround,
            CurrentState::OnLauncher(_) => StateId::OnLauncher,
            CurrentState::ReadyToFly(_) => StateId::ReadyToFly,
            CurrentState::InFlight(_) => StateId::InFlight,
            CurrentState::Armed(_) => StateId::Armed,
            CurrentState::Detonated(_) => StateId::Detonated,
        }
    }
}

#[derive(Debug, Clone, Copy)]
pub struct PeripheralState {
    pub hall_effect_closed: bool,
    pub safety_pin_pulled: bool,
}

impl Default for PeripheralState {
    fn default() -> Self {
        Self {
            hall_effect_closed: false,
            safety_pin_pulled: false,
        }
    }
}

pub struct StateMachine {
    current_state: CurrentState,
    peripheral_state: PeripheralState,
    protocol: Protocol,
}

impl StateMachine {
    pub fn new() -> Self {
        Self {
            current_state: CurrentState::OnGround(OnGround),
            peripheral_state: PeripheralState::default(),
            protocol: Protocol::new(),
        }
    }

    pub fn handle_event(&mut self, event: Event) -> Option<Vec<u8, MAX_ENCODED_SIZE>> {
        let old_state_id = self.current_state.to_state_id();
        let mut state_changed = false;

        // Handle the event
        match event {
            Event::Sensor(sensor_event) => {
                self.update_peripheral_state(sensor_event);
                state_changed = self.try_sensor_transition(sensor_event);
            }
            Event::Uart(command) => {
                state_changed = self.try_uart_transition(command);
            }
        }

        // Send state change message if state actually changed
        if state_changed {
            let new_state_id = self.current_state.to_state_id();
            info!("State: {:?} -> {:?}", old_state_id, new_state_id);
            Some(self.protocol.encode_state_change(old_state_id, new_state_id))
        } else {
            None
        }
    }

    pub fn create_heartbeat(&mut self) -> Vec<u8, MAX_ENCODED_SIZE> {
        self.protocol.encode_heartbeat(
            self.current_state.to_state_id(),
            self.peripheral_state.hall_effect_closed,
            self.peripheral_state.safety_pin_pulled,
        )
    }

    pub fn decode_command(&self, data: &[u8]) -> Option<UartCommand> {
        self.protocol.decode_command(data).map(UartCommand::from)
    }

    fn update_peripheral_state(&mut self, sensor_event: SensorEvent) {
        match sensor_event {
            SensorEvent::HallEffectClosed => self.peripheral_state.hall_effect_closed = true,
            SensorEvent::HallEffectOpen => self.peripheral_state.hall_effect_closed = false,
            SensorEvent::SafetyPinPulled => self.peripheral_state.safety_pin_pulled = true,
            SensorEvent::SafetyPinInserted => self.peripheral_state.safety_pin_pulled = false,
        }
    }

    fn try_sensor_transition(&mut self, sensor_event: SensorEvent) -> bool {
        let new_state = match (&self.current_state, sensor_event) {
            (CurrentState::OnGround(_), SensorEvent::HallEffectClosed) if self.peripheral_state.hall_effect_closed => {
                Some(CurrentState::OnLauncher(OnLauncher))
            }
            (CurrentState::OnLauncher(_), SensorEvent::HallEffectOpen) if !self.peripheral_state.hall_effect_closed => {
                Some(CurrentState::OnGround(OnGround))
            }
            (CurrentState::ReadyToFly(_), SensorEvent::HallEffectOpen) if !self.peripheral_state.hall_effect_closed => {
                Some(CurrentState::InFlight(InFlight))
            }
            _ => None,
        };

        if let Some(state) = new_state {
            self.current_state = state;
            true
        } else {
            false
        }
    }

    fn try_uart_transition(&mut self, command: UartCommand) -> bool {
        let new_state = match (&self.current_state, command) {
            (CurrentState::OnLauncher(_), UartCommand::PrepareForFlight) if self.peripheral_state.safety_pin_pulled => {
                Some(CurrentState::ReadyToFly(ReadyToFly))
            }
            (CurrentState::ReadyToFly(_), UartCommand::ResetRequest) => Some(CurrentState::OnGround(OnGround)),
            (CurrentState::InFlight(_), UartCommand::ArmRequest) => Some(CurrentState::Armed(Armed)),
            (CurrentState::InFlight(_), UartCommand::ResetRequest) => Some(CurrentState::OnGround(OnGround)),
            (CurrentState::Armed(_), UartCommand::DisarmRequest) => Some(CurrentState::InFlight(InFlight)),
            (CurrentState::Armed(_), UartCommand::DetonateRequest) => Some(CurrentState::Detonated(Detonated)),
            _ => {
                warn!(
                    "Invalid transition: {:?} with command {:?}",
                    self.current_state.to_state_id(),
                    command
                );
                None
            }
        };

        if let Some(state) = new_state {
            self.current_state = state;
            true
        } else {
            false
        }
    }
}

// ============================================================================
// Communication Channels
// ============================================================================

static EVENT_CHANNEL: Channel<ThreadModeRawMutex, Event, 10> = Channel::new();
static TX_CHANNEL: Channel<ThreadModeRawMutex, Vec<u8, MAX_ENCODED_SIZE>, 5> = Channel::new();

// ============================================================================
// Main Application
// ============================================================================

#[embassy_executor::main]
async fn main(spawner: Spawner) -> ! {
    let p = embassy_stm32::init(Default::default());
    info!("Simple State Machine Starting...");

    let config = Config::default();
    let usart = Uart::new(p.USART1, p.PA1, p.PA2, Irqs, p.GPDMA1_CH0, p.GPDMA1_CH1, config).unwrap();

    let (tx, rx) = usart.split();

    // Initialize sensors
    use embassy_stm32::gpio::{Input, Pull};
    let hall_effect_pin = Input::new(p.PA6, Pull::None); // Adjust pin as needed
    let safety_pin = Input::new(p.PA5, Pull::None); // Adjust pin as needed

    // Spawn tasks
    spawner.spawn(uart_reader(rx)).unwrap();
    spawner.spawn(uart_writer(tx)).unwrap();
    spawner.spawn(sensor_monitor(hall_effect_pin, safety_pin)).unwrap();
    spawner.spawn(state_machine_task()).unwrap();
    spawner.spawn(heartbeat_task()).unwrap();

    loop {
        Timer::after(Duration::from_secs(1)).await;
    }
}

#[embassy_executor::task]
async fn uart_reader(mut rx: UartRx<'static, Async>) {
    let mut buffer = [0u8; MAX_ENCODED_SIZE];
    let mut frame_buffer = Vec::<u8, MAX_ENCODED_SIZE>::new();

    loop {
        if let Ok(_) = rx.read(&mut buffer[..1]).await {
            let byte = buffer[0];

            if byte == 0 && !frame_buffer.is_empty() {
                // End of frame
                frame_buffer.push(0).ok();

                let state_machine = StateMachine::new(); // Temporary for decode
                if let Some(command) = state_machine.decode_command(&frame_buffer) {
                    EVENT_CHANNEL.send(Event::Uart(command)).await;
                }

                frame_buffer.clear();
            } else if byte != 0 {
                frame_buffer.push(byte).ok();
            }
        }
    }
}

#[embassy_executor::task]
async fn uart_writer(mut tx: UartTx<'static, Async>) {
    loop {
        let data = TX_CHANNEL.receive().await;
        let _ = tx.write(&data).await;
    }
}

#[embassy_executor::task]
async fn sensor_monitor(
    hall_effect_pin: embassy_stm32::gpio::Input<'static>,
    safety_pin: embassy_stm32::gpio::Input<'static>,
) {
    let mut hall_effect_closed = hall_effect_pin.is_low();
    let mut safety_pin_pulled = safety_pin.is_low(); // Assuming pulled low when pin is out

    loop {
        Timer::after(Duration::from_millis(50)).await;

        // Read actual sensor states
        let new_hall_state = hall_effect_pin.is_low();
        let new_safety_state = safety_pin.is_low();

        // Check for hall effect changes
        if new_hall_state != hall_effect_closed {
            hall_effect_closed = new_hall_state;
            let event = if new_hall_state {
                SensorEvent::HallEffectClosed
            } else {
                SensorEvent::HallEffectOpen
            };
            info!("Hall effect sensor: {:?}", event);
            EVENT_CHANNEL.send(Event::Sensor(event)).await;
        }

        // Check for safety pin changes
        if new_safety_state != safety_pin_pulled {
            safety_pin_pulled = new_safety_state;
            let event = if new_safety_state {
                SensorEvent::SafetyPinPulled
            } else {
                SensorEvent::SafetyPinInserted
            };
            info!("Safety pin: {:?}", event);
            EVENT_CHANNEL.send(Event::Sensor(event)).await;
        }
    }
}

#[embassy_executor::task]
async fn state_machine_task() {
    let mut state_machine = StateMachine::new();

    loop {
        let event = EVENT_CHANNEL.receive().await;

        if let Some(state_change_msg) = state_machine.handle_event(event) {
            TX_CHANNEL.send(state_change_msg).await;
        }
    }
}

#[embassy_executor::task]
async fn heartbeat_task() {
    let mut state_machine = StateMachine::new();

    loop {
        Timer::after(Duration::from_millis(100)).await; // 10Hz heartbeat

        let heartbeat = state_machine.create_heartbeat();
        TX_CHANNEL.send(heartbeat).await;
    }
}
