//! UART Protocol Implementation
//!
//! Defines the communication protocol between the device and host:
//! - <PERSON><PERSON> sends: Heartbeat messages (periodic status updates)
//! - Device receives: Command messages (state change requests)
//!
//! Protocol: [MSG_TYPE][PAYLOAD][CRC8] -> COBS_ENCODE -> [DATA][0x00]

use crate::state_machine::{Command, StateId};
use cobs;
use crc::{Algorithm, Crc};
use defmt::Format;
use heapless::Vec;

const CRC_ALG: Algorithm<u8> = Algorithm {
    width: 8,
    poly: 0xD5,
    init: 0x00,
    refin: false,
    refout: false,
    xorout: 0x00,
    check: 0x00,
    residue: 0x00,
};

/// Protocol message types
#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Format)]
pub enum MessageType {
    // Outgoing (device -> host)
    Heartbeat = 0x01,

    // Incoming (host -> device)
    PrepareForFlight = 0x10,
    ArmRequest = 0x11,
    DisarmRequest = 0x12,
    DetonateRequest = 0x13,
    ResetRequest = 0x14,
}

impl From<u8> for MessageType {
    fn from(byte: u8) -> Self {
        match byte {
            0x01 => MessageType::Heartbeat,
            0x10 => MessageType::PrepareForFlight,
            0x11 => MessageType::ArmRequest,
            0x12 => MessageType::DisarmRequest,
            0x13 => MessageType::DetonateRequest,
            0x14 => MessageType::ResetRequest,
            _ => MessageType::PrepareForFlight, // Default fallback
        }
    }
}

impl From<Command> for MessageType {
    fn from(command: Command) -> Self {
        match command {
            Command::PrepareForFlight => MessageType::PrepareForFlight,
            Command::ArmRequest => MessageType::ArmRequest,
            Command::DisarmRequest => MessageType::DisarmRequest,
            Command::DetonateRequest => MessageType::DetonateRequest,
            Command::ResetRequest => MessageType::ResetRequest,
        }
    }
}

impl From<MessageType> for Command {
    fn from(msg_type: MessageType) -> Self {
        match msg_type {
            MessageType::PrepareForFlight => Command::PrepareForFlight,
            MessageType::ArmRequest => Command::ArmRequest,
            MessageType::DisarmRequest => Command::DisarmRequest,
            MessageType::DetonateRequest => Command::DetonateRequest,
            MessageType::ResetRequest => Command::ResetRequest,
            MessageType::Heartbeat => Command::ResetRequest, // Fallback
        }
    }
}

/// Protocol error types
#[derive(Debug, Clone, Copy, PartialEq, Format)]
pub enum ProtocolError {
    CrcError,
    InvalidFrame,
    UnknownMessage,
    BufferOverflow,
}

/// Maximum frame sizes
const MAX_FRAME_SIZE: usize = 8;
const MAX_ENCODED_SIZE: usize = 16;

/// Heartbeat message payload
#[derive(Debug, Clone, Copy)]
pub struct HeartbeatPayload {
    pub state: StateId,
    pub flags: u8, // bit 0: hall_effect, bit 1: safety_pin
    pub sequence: u8,
}

/// Protocol handler for encoding/decoding messages
pub struct Protocol {
    sequence: u8,
    crc: Crc<u8>,
}

impl Protocol {
    pub fn new() -> Self {
        Self {
            sequence: 0,
            crc: Crc::new(&CRC_ALG),
        }
    }

    /// Encode a heartbeat message
    pub fn encode_heartbeat(
        &mut self,
        state: StateId,
        hall_effect: bool,
        safety_pin: bool,
    ) -> Vec<u8, MAX_ENCODED_SIZE> {
        let flags = (hall_effect as u8) | ((safety_pin as u8) << 1);
        self.sequence = self.sequence.wrapping_add(1);

        let mut frame = Vec::<u8, MAX_FRAME_SIZE>::new();
        frame.push(MessageType::Heartbeat as u8).ok();
        frame.push(state as u8).ok();
        frame.push(flags).ok();
        frame.push(self.sequence).ok();

        self.finalize_frame(frame)
    }

    /// Decode an incoming command message
    pub fn decode_command(&self, encoded_data: &[u8]) -> Result<Command, ProtocolError> {
        let frame = self.cobs_decode(encoded_data).ok_or(ProtocolError::InvalidFrame)?;

        if !self.verify_crc(&frame) {
            return Err(ProtocolError::CrcError);
        }

        if frame.is_empty() {
            return Err(ProtocolError::InvalidFrame);
        }

        let msg_type = MessageType::from(frame[0]);

        match msg_type {
            MessageType::PrepareForFlight
            | MessageType::ArmRequest
            | MessageType::DisarmRequest
            | MessageType::DetonateRequest
            | MessageType::ResetRequest => Ok(Command::from(msg_type)),
            _ => Err(ProtocolError::UnknownMessage),
        }
    }

    /// Finalize frame with CRC and COBS encoding
    fn finalize_frame(&self, mut frame: Vec<u8, MAX_FRAME_SIZE>) -> Vec<u8, MAX_ENCODED_SIZE> {
        let crc = self.crc.checksum(&frame);
        frame.push(crc).ok();

        let mut buf = [0u8; MAX_ENCODED_SIZE];
        let encoded_len = cobs::encode(&frame, &mut buf).expect("Encode buffer too small");
        buf[encoded_len] = 0u8;

        let mut result = Vec::<u8, MAX_ENCODED_SIZE>::new();
        result.extend_from_slice(&buf[0..=encoded_len]).ok();
        result
    }

    /// COBS decode data
    fn cobs_decode(&self, data: &[u8]) -> Option<Vec<u8, MAX_FRAME_SIZE>> {
        if data.is_empty() || data.last() != Some(&0u8) {
            return None;
        }

        let source = &data[0..data.len() - 1];
        let mut buf = [0u8; MAX_FRAME_SIZE];
        let len = match cobs::decode(source, &mut buf) {
            Ok(len) => len,
            Err(_) => return None,
        };

        let mut decoded = Vec::<u8, MAX_FRAME_SIZE>::new();
        if decoded.extend_from_slice(&buf[0..len]).is_err() {
            return None;
        }

        Some(decoded)
    }

    /// Verify CRC of received frame
    fn verify_crc(&self, frame: &[u8]) -> bool {
        if frame.len() < 2 {
            return false;
        }
        let (data, crc_byte) = frame.split_at(frame.len() - 1);
        self.crc.checksum(data) == crc_byte[0]
    }
}
