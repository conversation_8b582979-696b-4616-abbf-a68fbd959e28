// state_machine.rs
//! Type-Safe State Machine with Sensor Properties
//!
//! Uses the type state pattern to make illegal transitions impossible at compile time.
//! Sensors are treated as continuous properties rather than events to prevent desync.

use defmt::Format;

/// Commands that can be received via UART
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Format)]
pub enum Command {
    PrepareForFlight,
    ArmRequest,
    DisarmRequest,
    DetonateRequest,
    ResetRequest,
}

/// Current sensor/peripheral readings (not events!)
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct SensorState {
    pub hall_effect_closed: bool,
    pub safety_pin_pulled: bool,
}

/// State machine errors
#[derive(Debug, Clone, Copy, PartialEq, Format)]
pub enum TransitionError {
    InvalidTransition,
    SafetyPinNotPulled,
    HallEffectNotClosed,
    HallEffectNotOpen,
}

/// State IDs for protocol communication
#[repr(u8)]
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Format)]
pub enum StateId {
    OnGround = 0x00,
    OnLauncher = 0x01,
    ReadyToFly = 0x02,
    InFlight = 0x03,
    Armed = 0x04,
    Detonated = 0xFF,
}

// ============================================================================
// Type State Pattern - Each state is a distinct type
// ============================================================================

#[derive(Debug, Clone, Copy)]
pub struct OnGround {
    sensors: SensorState,
}

#[derive(Debug, Clone, Copy)]
pub struct OnLauncher {
    sensors: SensorState,
}

#[derive(Debug, Clone, Copy)]
pub struct ReadyToFly {
    sensors: SensorState,
}

#[derive(Debug, Clone, Copy)]
pub struct InFlight {
    sensors: SensorState,
}

#[derive(Debug, Clone, Copy)]
pub struct Armed {
    sensors: SensorState,
}

#[derive(Debug, Clone, Copy)]
pub struct Detonated;

// ============================================================================
// State Implementations - Only valid transitions are possible
// ============================================================================

impl OnGround {
    pub fn new(sensors: SensorState) -> Self {
        Self { sensors }
    }

    pub fn sensors(&self) -> &SensorState {
        &self.sensors
    }

    /// Update sensor readings and potentially transition to OnLauncher
    pub fn update_sensors(self, sensors: SensorState) -> Result<OnLauncher, OnGround> {
        if sensors.hall_effect_closed {
            Ok(OnLauncher { sensors })
        } else {
            Ok(OnGround { sensors })
        }
    }

    /// Commands are not valid in OnGround state
    pub fn handle_command(self, _command: Command) -> Result<Self, TransitionError> {
        Err(TransitionError::InvalidTransition)
    }
}

impl OnLauncher {
    pub fn sensors(&self) -> &SensorState {
        &self.sensors
    }

    /// Update sensor readings and potentially transition
    pub fn update_sensors(self, sensors: SensorState) -> Result<OnGround, OnLauncher> {
        if !sensors.hall_effect_closed {
            Ok(OnGround::new(sensors))
        } else {
            Ok(OnLauncher { sensors })
        }
    }

    /// Handle commands in OnLauncher state
    pub fn handle_command(self, command: Command) -> Result<CommandResult<OnLauncher>, TransitionError> {
        match command {
            Command::PrepareForFlight => {
                if self.sensors.safety_pin_pulled {
                    Ok(CommandResult::StateChange(ReadyToFly { sensors: self.sensors }))
                } else {
                    Err(TransitionError::SafetyPinNotPulled)
                }
            }
            _ => Err(TransitionError::InvalidTransition),
        }
    }
}

impl ReadyToFly {
    pub fn sensors(&self) -> &SensorState {
        &self.sensors
    }

    /// Update sensor readings and potentially transition
    pub fn update_sensors(self, sensors: SensorState) -> SensorUpdateResult<ReadyToFly> {
        if !sensors.hall_effect_closed {
            // Launch detected!
            SensorUpdateResult::StateChange(InFlight { sensors })
        } else {
            SensorUpdateResult::SameState(ReadyToFly { sensors })
        }
    }

    /// Handle commands in ReadyToFly state
    pub fn handle_command(self, command: Command) -> Result<CommandResult<ReadyToFly>, TransitionError> {
        match command {
            Command::ResetRequest => Ok(CommandResult::StateChange(OnGround::new(self.sensors))),
            _ => Err(TransitionError::InvalidTransition),
        }
    }
}

impl InFlight {
    pub fn sensors(&self) -> &SensorState {
        &self.sensors
    }

    /// Update sensor readings (no automatic transitions in flight)
    pub fn update_sensors(self, sensors: SensorState) -> InFlight {
        InFlight { sensors }
    }

    /// Handle commands in InFlight state
    pub fn handle_command(self, command: Command) -> Result<CommandResult<InFlight>, TransitionError> {
        match command {
            Command::ArmRequest => Ok(CommandResult::StateChange(Armed { sensors: self.sensors })),
            Command::ResetRequest => Ok(CommandResult::StateChange(OnGround::new(self.sensors))),
            _ => Err(TransitionError::InvalidTransition),
        }
    }
}

impl Armed {
    pub fn sensors(&self) -> &SensorState {
        &self.sensors
    }

    /// Update sensor readings (no automatic transitions when armed)
    pub fn update_sensors(self, sensors: SensorState) -> Armed {
        Armed { sensors }
    }

    /// Handle commands in Armed state
    pub fn handle_command(self, command: Command) -> Result<ArmedCommandResult, TransitionError> {
        match command {
            Command::DisarmRequest => Ok(ArmedCommandResult::StateChange(InFlight { sensors: self.sensors })),
            Command::DetonateRequest => Ok(ArmedCommandResult::Detonated(Detonated)),
            _ => Err(TransitionError::InvalidTransition),
        }
    }
}

impl Detonated {
    /// Terminal state - no transitions possible
    pub fn update_sensors(self, _sensors: SensorState) -> Detonated {
        self
    }

    pub fn handle_command(self, _command: Command) -> Result<Detonated, TransitionError> {
        Err(TransitionError::InvalidTransition)
    }
}

// ============================================================================
// Result Types for Type-Safe Transitions
// ============================================================================

pub enum CommandResult<T> {
    SameState(T),
    StateChange(StateMachine),
}

pub enum ArmedCommandResult {
    StateChange(InFlight),
    Detonated(Detonated),
}

pub enum SensorUpdateResult<T> {
    SameState(T),
    StateChange(StateMachine),
}

// ============================================================================
// Runtime State Container
// ============================================================================

#[derive(Debug)]
pub enum StateMachine {
    OnGround(OnGround),
    OnLauncher(OnLauncher),
    ReadyToFly(ReadyToFly),
    InFlight(InFlight),
    Armed(Armed),
    Detonated(Detonated),
}

impl StateMachine {
    /// Create new state machine starting in OnGround
    pub fn new() -> Self {
        StateMachine::OnGround(OnGround::new(SensorState::default()))
    }

    /// Get current state ID for protocol
    pub fn state_id(&self) -> StateId {
        match self {
            StateMachine::OnGround(_) => StateId::OnGround,
            StateMachine::OnLauncher(_) => StateId::OnLauncher,
            StateMachine::ReadyToFly(_) => StateId::ReadyToFly,
            StateMachine::InFlight(_) => StateId::InFlight,
            StateMachine::Armed(_) => StateId::Armed,
            StateMachine::Detonated(_) => StateId::Detonated,
        }
    }

    /// Get current sensor state
    pub fn sensors(&self) -> &SensorState {
        match self {
            StateMachine::OnGround(s) => s.sensors(),
            StateMachine::OnLauncher(s) => s.sensors(),
            StateMachine::ReadyToFly(s) => s.sensors(),
            StateMachine::InFlight(s) => s.sensors(),
            StateMachine::Armed(s) => s.sensors(),
            StateMachine::Detonated(_) => &SensorState::default(),
        }
    }

    /// Update sensor readings and check for automatic transitions
    pub fn update_sensors(&mut self, new_sensors: SensorState) -> bool {
        let old_state_id = self.state_id();

        *self = match core::mem::replace(self, StateMachine::new()) {
            StateMachine::OnGround(state) => match state.update_sensors(new_sensors) {
                Ok(new_state) => StateMachine::OnLauncher(new_state),
                Err(same_state) => StateMachine::OnGround(same_state),
            },
            StateMachine::OnLauncher(state) => match state.update_sensors(new_sensors) {
                Ok(new_state) => StateMachine::OnGround(new_state),
                Err(same_state) => StateMachine::OnLauncher(same_state),
            },
            StateMachine::ReadyToFly(state) => match state.update_sensors(new_sensors) {
                SensorUpdateResult::StateChange(new_state) => new_state,
                SensorUpdateResult::SameState(same_state) => StateMachine::ReadyToFly(same_state),
            },
            StateMachine::InFlight(state) => StateMachine::InFlight(state.update_sensors(new_sensors)),
            StateMachine::Armed(state) => StateMachine::Armed(state.update_sensors(new_sensors)),
            StateMachine::Detonated(state) => StateMachine::Detonated(state.update_sensors(new_sensors)),
        };

        self.state_id() != old_state_id
    }

    /// Handle a command and potentially transition
    pub fn handle_command(&mut self, command: Command) -> Result<bool, TransitionError> {
        let old_state_id = self.state_id();

        *self = match core::mem::replace(self, StateMachine::new()) {
            StateMachine::OnGround(state) => {
                state.handle_command(command)?;
                StateMachine::OnGround(state)
            }
            StateMachine::OnLauncher(state) => match state.handle_command(command)? {
                CommandResult::StateChange(new_state) => new_state,
                CommandResult::SameState(same_state) => StateMachine::OnLauncher(same_state),
            },
            StateMachine::ReadyToFly(state) => match state.handle_command(command)? {
                CommandResult::StateChange(new_state) => new_state,
                CommandResult::SameState(same_state) => StateMachine::ReadyToFly(same_state),
            },
            StateMachine::InFlight(state) => match state.handle_command(command)? {
                CommandResult::StateChange(new_state) => new_state,
                CommandResult::SameState(same_state) => StateMachine::InFlight(same_state),
            },
            StateMachine::Armed(state) => match state.handle_command(command)? {
                ArmedCommandResult::StateChange(new_state) => StateMachine::InFlight(new_state),
                ArmedCommandResult::Detonated(detonated) => StateMachine::Detonated(detonated),
            },
            StateMachine::Detonated(state) => {
                state.handle_command(command)?;
                StateMachine::Detonated(state)
            }
        };

        Ok(self.state_id() != old_state_id)
    }
}
