#![no_std]
#![no_main]

// Main application file
// Uses separate modules for clean organization

mod protocol;
mod state_machine;

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::mode::Async;
use embassy_stm32::usart::{Config, Uart, UartRx, UartTx};
use embassy_stm32::{bind_interrupts, peripherals, usart};
use embassy_sync::blocking_mutex::raw::ThreadModeRawMutex;
use embassy_sync::channel::Channel;
use embassy_time::{Duration, Timer};
use heapless::Vec;
use {defmt_rtt as _, panic_probe as _};

use protocol::{Protocol, MAX_ENCODED_SIZE};
use state_machine::{Command, SensorState, StateMachine};

bind_interrupts!(struct Irqs {
    USART1 => usart::InterruptHandler<peripherals::USART1>;
});

// Communication channels
static COMMAND_CHANNEL: Channel<ThreadModeRawMutex, Command, 10> = Channel::new();
static SENSOR_UPDATE_CHANNEL: Channel<ThreadModeRawMutex, SensorState, 10> = Channel::new();
static TX_CHANNEL: Channel<ThreadModeRawMutex, Vec<u8, MAX_ENCODED_SIZE>, 5> = Channel::new();

// ============================================================================
// Main Application
// ============================================================================

#[embassy_executor::main]
async fn main(spawner: Spawner) -> ! {
    let p = embassy_stm32::init(Default::default());
    info!("Detonator State Machine Starting...");

    // Initialize UART
    let config = Config::default();
    let usart = Uart::new(p.USART1, p.PA1, p.PA2, Irqs, p.GPDMA1_CH0, p.GPDMA1_CH1, config).unwrap();

    let (tx, rx) = usart.split();

    // Initialize sensors
    use embassy_stm32::gpio::{Input, Pull};
    let hall_effect_pin = Input::new(p.PA3, Pull::Up);
    let safety_pin = Input::new(p.PA4, Pull::Up);

    // Spawn tasks
    spawner.spawn(uart_reader(rx)).unwrap();
    spawner.spawn(uart_writer(tx)).unwrap();
    spawner.spawn(sensor_monitor(hall_effect_pin, safety_pin)).unwrap();
    spawner.spawn(state_machine_task()).unwrap();
    spawner.spawn(heartbeat_task()).unwrap();

    info!("All tasks started successfully");

    loop {
        Timer::after(Duration::from_secs(10)).await;
        info!("System alive");
    }
}

// ============================================================================
// Task Implementations
// ============================================================================

#[embassy_executor::task]
async fn uart_reader(mut rx: UartRx<'static, Async>) {
    let mut frame_buffer = Vec::<u8, MAX_ENCODED_SIZE>::new();
    let protocol = Protocol::new();

    info!("UART reader started");

    loop {
        let mut byte_buf = [0u8; 1];

        if rx.read(&mut byte_buf).await.is_ok() {
            let byte = byte_buf[0];

            if byte == 0 && !frame_buffer.is_empty() {
                // End of COBS frame
                frame_buffer.push(0).ok();

                match protocol.decode_command(&frame_buffer) {
                    Ok(command) => {
                        info!("Received command: {:?}", command);
                        COMMAND_CHANNEL.send(command).await;
                    }
                    Err(error) => {
                        warn!("Protocol error: {:?}", error);
                    }
                }

                frame_buffer.clear();
            } else if byte != 0 {
                if frame_buffer.push(byte).is_err() {
                    warn!("Frame buffer overflow, dropping frame");
                    frame_buffer.clear();
                }
            }
        }
    }
}

#[embassy_executor::task]
async fn uart_writer(mut tx: UartTx<'static, Async>) {
    info!("UART writer started");

    loop {
        let data = TX_CHANNEL.receive().await;

        if let Err(_) = tx.write(&data).await {
            warn!("Failed to send UART data");
        }
    }
}

#[embassy_executor::task]
async fn sensor_monitor(
    mut hall_effect_pin: embassy_stm32::gpio::Input<'static>,
    mut safety_pin: embassy_stm32::gpio::Input<'static>,
) {
    let mut current_sensors = SensorState {
        hall_effect_closed: hall_effect_pin.is_low(),
        safety_pin_pulled: safety_pin.is_low(),
    };

    info!("Sensor monitor started");
    info!(
        "Initial sensors - Hall: {}, Safety: {}",
        current_sensors.hall_effect_closed, current_sensors.safety_pin_pulled
    );

    // Send initial sensor state
    SENSOR_UPDATE_CHANNEL.send(current_sensors).await;

    loop {
        Timer::after(Duration::from_millis(50)).await;

        let new_sensors = SensorState {
            hall_effect_closed: hall_effect_pin.is_low(),
            safety_pin_pulled: safety_pin.is_low(),
        };

        // Only send update if sensors changed
        if new_sensors.hall_effect_closed != current_sensors.hall_effect_closed
            || new_sensors.safety_pin_pulled != current_sensors.safety_pin_pulled
        {
            info!(
                "Sensor change - Hall: {} -> {}, Safety: {} -> {}",
                current_sensors.hall_effect_closed,
                new_sensors.hall_effect_closed,
                current_sensors.safety_pin_pulled,
                new_sensors.safety_pin_pulled
            );

            current_sensors = new_sensors;
            SENSOR_UPDATE_CHANNEL.send(current_sensors).await;
        }
    }
}

#[embassy_executor::task]
async fn state_machine_task() {
    let mut state_machine = StateMachine::new();
    let mut current_sensors = SensorState::default();

    info!("State machine started in state: {:?}", state_machine.state_id());

    loop {
        // Use select to handle both sensor updates and commands
        match embassy_futures::select::select(SENSOR_UPDATE_CHANNEL.receive(), COMMAND_CHANNEL.receive()).await {
            embassy_futures::select::Either::First(sensors) => {
                // Sensor update
                current_sensors = sensors;
                let state_changed = state_machine.update_sensors(sensors);

                if state_changed {
                    info!("State changed due to sensors: {:?}", state_machine.state_id());
                }
            }
            embassy_futures::select::Either::Second(command) => {
                // Command received
                match state_machine.handle_command(command) {
                    Ok(state_changed) => {
                        if state_changed {
                            info!(
                                "State changed due to command: {:?} -> {:?}",
                                command,
                                state_machine.state_id()
                            );
                        } else {
                            info!("Command {:?} processed, no state change", command);
                        }
                    }
                    Err(error) => {
                        warn!("Command {:?} failed: {:?}", command, error);
                    }
                }
            }
        }
    }
}

#[embassy_executor::task]
async fn heartbeat_task() {
    let mut protocol = Protocol::new();
    let mut current_state = state_machine::StateId::OnGround;
    let mut current_sensors = SensorState::default();

    info!("Heartbeat task started (100ms interval)");

    // Get initial state
    {
        let state_machine_guard = STATE_MACHINE.lock().await;
        if let Some(ref state_machine) = *state_machine_guard {
            current_state = state_machine.state_id();
            current_sensors = *state_machine.sensors();
            info!("Heartbeat initial state: {:?}", current_state);
        }
    }

    loop {
        // Check for state updates (non-blocking)
        if let Ok((new_state, new_sensors)) = CURRENT_STATE_SIGNAL.try_take() {
            current_state = new_state;
            current_sensors = new_sensors;
            info!("Heartbeat updated to state: {:?}", current_state);
        }

        // Send heartbeat with current state
        let heartbeat = protocol.encode_heartbeat(
            current_state,
            current_sensors.hall_effect_closed,
            current_sensors.safety_pin_pulled,
        );

        TX_CHANNEL.send(heartbeat).await;

        Timer::after(Duration::from_millis(100)).await;
    }
}
