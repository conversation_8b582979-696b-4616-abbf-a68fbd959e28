//! Safety Validation and Monitoring
//!
//! Implements comprehensive safety checks and validation for the detonator
//! control system. This module provides multiple layers of safety validation
//! to prevent accidental detonation and ensure safe operation.
//!
//! Safety Layers:
//! 1. Hardware validation (sensor consistency)
//! 2. State transition validation (logical consistency)
//! 3. Command validation (authorization and timing)
//! 4. System health monitoring (fault detection)
//! 5. Emergency shutdown capabilities

use crate::sensors::SensorHealth;
use crate::state_machine::{Command, SensorState, StateId};
use defmt::Format;
use embassy_time::Instant;

// ============================================================================
// Safety Configuration
// ============================================================================

/// Maximum time allowed between safety checks
const SAFETY_CHECK_INTERVAL_MS: u64 = 500;

/// Maximum time allowed in Armed state without activity
const ARMED_STATE_TIMEOUT_MS: u64 = 30_000; // 30 seconds

/// Minimum time required between critical commands
const COMMAND_COOLDOWN_MS: u64 = 1000; // 1 second

/// Maximum number of failed safety checks before emergency shutdown
const MAX_SAFETY_FAILURES: u8 = 3;

// ============================================================================
// Safety Monitor
// ============================================================================

/// Comprehensive safety monitoring system
pub struct SafetyMonitor {
    last_safety_check: Instant,
    armed_since: Option<Instant>,
    last_critical_command: Option<Instant>,
    safety_failure_count: u8,
    emergency_shutdown: bool,
}

impl SafetyMonitor {
    /// Create new safety monitor
    pub fn new() -> Self {
        Self {
            last_safety_check: Instant::now(),
            armed_since: None,
            last_critical_command: None,
            safety_failure_count: 0,
            emergency_shutdown: false,
        }
    }

    /// Perform comprehensive safety check
    pub fn safety_check(
        &mut self,
        current_state: StateId,
        sensor_state: SensorState,
        sensor_health: SensorHealth,
    ) -> SafetyStatus {
        let now = Instant::now();
        self.last_safety_check = now;

        // Check for emergency shutdown
        if self.emergency_shutdown {
            return SafetyStatus::EmergencyShutdown;
        }

        // Check sensor health
        if !sensor_health.is_healthy() {
            self.record_safety_failure();
            return SafetyStatus::SensorFault(sensor_health);
        }

        // Check state-specific safety conditions
        let state_safety = self.check_state_safety(current_state, sensor_state, now);
        if !state_safety.is_safe() {
            self.record_safety_failure();
            return state_safety;
        }

        // Check for armed state timeout
        if let Some(armed_time) = self.armed_since {
            if current_state == StateId::Armed {
                let armed_duration = now.duration_since(armed_time).as_millis();
                if armed_duration > ARMED_STATE_TIMEOUT_MS {
                    self.record_safety_failure();
                    return SafetyStatus::ArmedTimeout;
                }
            } else {
                self.armed_since = None; // Clear armed timer if not in armed state
            }
        }

        // Reset failure count on successful check
        self.safety_failure_count = 0;
        SafetyStatus::Safe
    }

    /// Validate a command before execution
    pub fn validate_command(
        &mut self,
        command: Command,
        current_state: StateId,
        sensor_state: SensorState,
    ) -> CommandValidation {
        let now = Instant::now();

        // Check for emergency shutdown
        if self.emergency_shutdown {
            return CommandValidation::Rejected(CommandRejectionReason::EmergencyShutdown);
        }

        // Check command cooldown for critical commands
        if self.is_critical_command(command) {
            if let Some(last_critical) = self.last_critical_command {
                let time_since_last = now.duration_since(last_critical).as_millis();
                if time_since_last < COMMAND_COOLDOWN_MS {
                    return CommandValidation::Rejected(CommandRejectionReason::CooldownActive);
                }
            }
        }

        // Validate command against current state and sensors
        let validation = self.validate_command_logic(command, current_state, sensor_state);
        
        // Record critical command timing
        if validation.is_approved() && self.is_critical_command(command) {
            self.last_critical_command = Some(now);
            
            // Track armed state entry
            if command == Command::ArmRequest {
                self.armed_since = Some(now);
            }
        }

        validation
    }

    /// Trigger emergency shutdown
    pub fn emergency_shutdown(&mut self, reason: EmergencyReason) {
        self.emergency_shutdown = true;
        defmt::error!("EMERGENCY SHUTDOWN TRIGGERED: {:?}", reason);
    }

    /// Check if system is in emergency shutdown
    pub fn is_emergency_shutdown(&self) -> bool {
        self.emergency_shutdown
    }

    /// Get current safety failure count
    pub fn safety_failure_count(&self) -> u8 {
        self.safety_failure_count
    }

    // ========================================================================
    // Internal Implementation
    // ========================================================================

    fn record_safety_failure(&mut self) {
        self.safety_failure_count = self.safety_failure_count.saturating_add(1);
        
        if self.safety_failure_count >= MAX_SAFETY_FAILURES {
            self.emergency_shutdown(EmergencyReason::MultipleSafetyFailures);
        }
    }

    fn check_state_safety(
        &self,
        state: StateId,
        sensors: SensorState,
        _now: Instant,
    ) -> SafetyStatus {
        match state {
            StateId::OnGround => {
                // Should not have hall effect closed when on ground
                if sensors.hall_effect_closed {
                    SafetyStatus::LogicalInconsistency
                } else {
                    SafetyStatus::Safe
                }
            }
            StateId::OnLauncher => {
                // Must have hall effect closed when on launcher
                if !sensors.hall_effect_closed {
                    SafetyStatus::LogicalInconsistency
                } else {
                    SafetyStatus::Safe
                }
            }
            StateId::ReadyToFly => {
                // Must have hall effect closed and safety pin pulled
                if !sensors.hall_effect_closed || !sensors.safety_pin_pulled {
                    SafetyStatus::LogicalInconsistency
                } else {
                    SafetyStatus::Safe
                }
            }
            StateId::InFlight => {
                // Hall effect should be open (launched)
                if sensors.hall_effect_closed {
                    SafetyStatus::LogicalInconsistency
                } else {
                    SafetyStatus::Safe
                }
            }
            StateId::Armed => {
                // Critical state - extra validation
                if sensors.hall_effect_closed {
                    // Armed while still on launcher - dangerous
                    SafetyStatus::LogicalInconsistency
                } else {
                    SafetyStatus::Safe
                }
            }
            StateId::Detonated => {
                // Terminal state - always safe (already detonated)
                SafetyStatus::Safe
            }
        }
    }

    fn validate_command_logic(
        &self,
        command: Command,
        state: StateId,
        sensors: SensorState,
    ) -> CommandValidation {
        match (command, state) {
            // PrepareForFlight: only valid from OnLauncher with safety pin pulled
            (Command::PrepareForFlight, StateId::OnLauncher) => {
                if sensors.safety_pin_pulled {
                    CommandValidation::Approved
                } else {
                    CommandValidation::Rejected(CommandRejectionReason::SafetyPinRequired)
                }
            }
            (Command::PrepareForFlight, _) => {
                CommandValidation::Rejected(CommandRejectionReason::InvalidState)
            }

            // ArmRequest: only valid from InFlight
            (Command::ArmRequest, StateId::InFlight) => CommandValidation::Approved,
            (Command::ArmRequest, _) => {
                CommandValidation::Rejected(CommandRejectionReason::InvalidState)
            }

            // DisarmRequest: only valid from Armed
            (Command::DisarmRequest, StateId::Armed) => CommandValidation::Approved,
            (Command::DisarmRequest, _) => {
                CommandValidation::Rejected(CommandRejectionReason::InvalidState)
            }

            // DetonateRequest: only valid from Armed, with additional safety checks
            (Command::DetonateRequest, StateId::Armed) => {
                if sensors.hall_effect_closed {
                    // Still on launcher - absolutely forbidden
                    CommandValidation::Rejected(CommandRejectionReason::StillOnLauncher)
                } else {
                    CommandValidation::Approved
                }
            }
            (Command::DetonateRequest, _) => {
                CommandValidation::Rejected(CommandRejectionReason::InvalidState)
            }

            // ResetRequest: valid from ReadyToFly and InFlight
            (Command::ResetRequest, StateId::ReadyToFly | StateId::InFlight) => {
                CommandValidation::Approved
            }
            (Command::ResetRequest, _) => {
                CommandValidation::Rejected(CommandRejectionReason::InvalidState)
            }
        }
    }

    fn is_critical_command(&self, command: Command) -> bool {
        matches!(
            command,
            Command::ArmRequest | Command::DetonateRequest | Command::PrepareForFlight
        )
    }
}

// ============================================================================
// Safety Status Types
// ============================================================================

/// Overall safety status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum SafetyStatus {
    Safe,
    SensorFault(SensorHealth),
    LogicalInconsistency,
    ArmedTimeout,
    EmergencyShutdown,
}

impl SafetyStatus {
    pub fn is_safe(&self) -> bool {
        matches!(self, SafetyStatus::Safe)
    }

    pub fn is_critical(&self) -> bool {
        matches!(
            self,
            SafetyStatus::EmergencyShutdown | SafetyStatus::ArmedTimeout
        )
    }
}

/// Command validation result
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum CommandValidation {
    Approved,
    Rejected(CommandRejectionReason),
}

impl CommandValidation {
    pub fn is_approved(&self) -> bool {
        matches!(self, CommandValidation::Approved)
    }
}

/// Reasons for command rejection
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum CommandRejectionReason {
    InvalidState,
    SafetyPinRequired,
    StillOnLauncher,
    CooldownActive,
    EmergencyShutdown,
    SensorFault,
}

/// Emergency shutdown reasons
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum EmergencyReason {
    MultipleSafetyFailures,
    CriticalSensorFault,
    SystemCorruption,
    ManualShutdown,
}

// ============================================================================
// Utility Functions
// ============================================================================

/// Create a safety monitor for testing
#[cfg(test)]
pub fn test_safety_monitor() -> SafetyMonitor {
    SafetyMonitor::new()
}

/// Check if a state transition is inherently safe
pub fn is_safe_transition(from: StateId, to: StateId) -> bool {
    match (from, to) {
        // Always safe transitions
        (_, StateId::OnGround) => true, // Reset is always safe
        (StateId::OnGround, StateId::OnLauncher) => true,
        (StateId::OnLauncher, StateId::OnGround) => true,
        (StateId::OnLauncher, StateId::ReadyToFly) => true,
        (StateId::ReadyToFly, StateId::InFlight) => true,
        (StateId::InFlight, StateId::Armed) => true,
        (StateId::Armed, StateId::InFlight) => true,
        (StateId::Armed, StateId::Detonated) => true,
        
        // Unsafe transitions
        _ => false,
    }
}
