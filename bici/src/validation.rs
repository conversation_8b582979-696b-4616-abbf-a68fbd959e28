//! System Validation Module
//!
//! Provides validation functions that can be called during system startup
//! to verify correct operation of all components. These are embedded-friendly
//! and don't require the standard test framework.

use crate::protocol::{Protocol, encode_command};
use crate::safety::SafetyMonitor;
use crate::sensors::SensorHealth;
use crate::state_machine::{Command, SensorState, StateMachine, StateId, TransitionError};
use defmt::*;

// ============================================================================
// Validation Result Type
// ============================================================================

#[derive(Debug, Clone, <PERSON><PERSON>, PartialEq)]
pub enum ValidationResult {
    Pass,
    Fail(&'static str),
}

impl ValidationResult {
    pub fn is_pass(&self) -> bool {
        matches!(self, ValidationResult::Pass)
    }
}

// ============================================================================
// State Machine Validation
// ============================================================================

/// Validate initial state machine state
pub fn validate_initial_state() -> ValidationResult {
    let sm = StateMachine::new();
    if sm.state_id() == StateId::OnGround && sm.sensors() == SensorState::default() {
        ValidationResult::Pass
    } else {
        ValidationResult::Fail("Initial state incorrect")
    }
}

/// Validate basic state transitions
pub fn validate_state_transitions() -> ValidationResult {
    let mut sm = StateMachine::new();
    
    // Test OnGround -> OnLauncher transition
    let sensors = SensorState {
        hall_effect_closed: true,
        safety_pin_pulled: false,
    };
    
    let changed = sm.update_sensors(sensors);
    if !changed || sm.state_id() != StateId::OnLauncher {
        return ValidationResult::Fail("Ground to launcher transition failed");
    }
    
    // Test OnLauncher -> ReadyToFly transition
    let sensors_with_safety = SensorState {
        hall_effect_closed: true,
        safety_pin_pulled: true,
    };
    sm.update_sensors(sensors_with_safety);
    
    match sm.handle_command(Command::PrepareForFlight) {
        Ok(true) if sm.state_id() == StateId::ReadyToFly => ValidationResult::Pass,
        _ => ValidationResult::Fail("Launcher to ready transition failed"),
    }
}

/// Validate safety pin requirement
pub fn validate_safety_pin_requirement() -> ValidationResult {
    let mut sm = StateMachine::new();
    
    // Go to OnLauncher without safety pin
    let sensors = SensorState {
        hall_effect_closed: true,
        safety_pin_pulled: false,
    };
    sm.update_sensors(sensors);
    
    // Try to prepare for flight without safety pin - should fail
    match sm.handle_command(Command::PrepareForFlight) {
        Err(TransitionError::SafetyPinNotPulled) => ValidationResult::Pass,
        _ => ValidationResult::Fail("Safety pin requirement not enforced"),
    }
}

/// Validate launch detection
pub fn validate_launch_detection() -> ValidationResult {
    let mut sm = create_ready_to_fly_state();
    
    // Simulate launch (hall effect opens)
    let sensors_launched = SensorState {
        hall_effect_closed: false,
        safety_pin_pulled: true,
    };
    let changed = sm.update_sensors(sensors_launched);
    
    if changed && sm.state_id() == StateId::InFlight {
        ValidationResult::Pass
    } else {
        ValidationResult::Fail("Launch detection failed")
    }
}

/// Validate complete arm and detonate sequence
pub fn validate_arm_detonate_sequence() -> ValidationResult {
    let mut sm = create_inflight_state();
    
    // Arm the device
    match sm.handle_command(Command::ArmRequest) {
        Ok(true) if sm.state_id() == StateId::Armed => {},
        _ => return ValidationResult::Fail("Arm command failed"),
    }
    
    // Detonate
    match sm.handle_command(Command::DetonateRequest) {
        Ok(true) if sm.state_id() == StateId::Detonated => ValidationResult::Pass,
        _ => ValidationResult::Fail("Detonate command failed"),
    }
}

// ============================================================================
// Protocol Validation
// ============================================================================

/// Validate heartbeat encoding/decoding
pub fn validate_heartbeat_protocol() -> ValidationResult {
    let mut protocol = Protocol::new();
    
    let encoded = protocol.encode_heartbeat(
        StateId::Armed,
        true,  // hall_effect
        false, // safety_pin
    );
    
    // Verify the message is properly terminated
    if encoded.last() != Some(&0x00) {
        return ValidationResult::Fail("Heartbeat not properly terminated");
    }
    
    // Decode and verify
    match protocol.decode_heartbeat(&encoded) {
        Ok(decoded) => {
            if decoded.state == StateId::Armed 
                && decoded.hall_effect_closed() 
                && !decoded.safety_pin_pulled() {
                ValidationResult::Pass
            } else {
                ValidationResult::Fail("Heartbeat decode mismatch")
            }
        }
        Err(_) => ValidationResult::Fail("Heartbeat decode failed"),
    }
}

/// Validate command encoding/decoding
pub fn validate_command_protocol() -> ValidationResult {
    let protocol = Protocol::new();
    
    let encoded = encode_command(Command::ArmRequest);
    match protocol.decode_command(&encoded) {
        Ok(Command::ArmRequest) => ValidationResult::Pass,
        _ => ValidationResult::Fail("Command protocol failed"),
    }
}

/// Validate CRC error detection
pub fn validate_crc_protection() -> ValidationResult {
    let protocol = Protocol::new();
    let mut encoded = encode_command(Command::DetonateRequest);
    
    // Corrupt the message
    if encoded.len() > 2 {
        encoded[1] = encoded[1].wrapping_add(1);
    }
    
    // Should fail CRC check
    match protocol.decode_command(&encoded) {
        Err(_) => ValidationResult::Pass,
        Ok(_) => ValidationResult::Fail("CRC validation failed"),
    }
}

// ============================================================================
// Safety Validation
// ============================================================================

/// Validate safety monitor basic functionality
pub fn validate_safety_monitor() -> ValidationResult {
    let mut safety = SafetyMonitor::new();
    
    let sensors = SensorState {
        hall_effect_closed: false,
        safety_pin_pulled: false,
    };
    let status = safety.safety_check(StateId::OnGround, sensors, SensorHealth::Healthy);
    
    if status.is_safe() {
        ValidationResult::Pass
    } else {
        ValidationResult::Fail("Safety monitor basic check failed")
    }
}

/// Validate dangerous command rejection
pub fn validate_dangerous_command_rejection() -> ValidationResult {
    let mut safety = SafetyMonitor::new();
    
    // Try to detonate while still on launcher (dangerous)
    let sensors_on_launcher = SensorState {
        hall_effect_closed: true,
        safety_pin_pulled: true,
    };
    let validation = safety.validate_command(
        Command::DetonateRequest,
        StateId::Armed,
        sensors_on_launcher,
    );
    
    if !validation.is_approved() {
        ValidationResult::Pass
    } else {
        ValidationResult::Fail("Dangerous command not rejected")
    }
}

// ============================================================================
// Comprehensive System Validation
// ============================================================================

/// Run all validation tests
pub fn run_all_validations() -> ValidationResult {
    let validations = [
        ("Initial State", validate_initial_state()),
        ("State Transitions", validate_state_transitions()),
        ("Safety Pin Requirement", validate_safety_pin_requirement()),
        ("Launch Detection", validate_launch_detection()),
        ("Arm/Detonate Sequence", validate_arm_detonate_sequence()),
        ("Heartbeat Protocol", validate_heartbeat_protocol()),
        ("Command Protocol", validate_command_protocol()),
        ("CRC Protection", validate_crc_protection()),
        ("Safety Monitor", validate_safety_monitor()),
        ("Dangerous Command Rejection", validate_dangerous_command_rejection()),
    ];
    
    for (name, result) in validations.iter() {
        if !result.is_pass() {
            error!("Validation failed: {}", name);
            return *result;
        } else {
            info!("Validation passed: {}", name);
        }
    }
    
    info!("All validations passed successfully");
    ValidationResult::Pass
}

// ============================================================================
// Helper Functions
// ============================================================================

fn create_ready_to_fly_state() -> StateMachine {
    let mut sm = StateMachine::new();
    
    // Go through the sequence to ReadyToFly
    let sensors_on_launcher = SensorState {
        hall_effect_closed: true,
        safety_pin_pulled: true,
    };
    sm.update_sensors(sensors_on_launcher);
    sm.handle_command(Command::PrepareForFlight).unwrap();
    
    sm
}

fn create_inflight_state() -> StateMachine {
    let mut sm = create_ready_to_fly_state();
    
    // Launch
    let sensors_launched = SensorState {
        hall_effect_closed: false,
        safety_pin_pulled: true,
    };
    sm.update_sensors(sensors_launched);
    
    sm
}
