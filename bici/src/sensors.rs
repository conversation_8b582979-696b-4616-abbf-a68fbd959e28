//! Sensor Abstraction Layer
//!
//! Provides simple sensor access with basic debouncing for protocol testing.

use crate::state_machine::SensorState;
use embassy_stm32::gpio::Input;
use embassy_time::Instant;

// ============================================================================
// Configuration Constants
// ============================================================================

/// Debounce time for sensor readings (prevents noise)
const DEBOUNCE_TIME_MS: u64 = 10;

// ============================================================================
// Sensor Types
// ============================================================================

/// Debounced sensor reading with timestamp
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
struct DebouncedReading {
    stable_value: bool,      // The debounced, stable value
    raw_value: bool,         // The current raw input
    change_start: Instant,   // When the raw value last changed
}

impl DebouncedReading {
    fn new(initial_value: bool, now: Instant) -> Self {
        Self {
            stable_value: initial_value,
            raw_value: initial_value,
            change_start: now,
        }
    }

    /// Update reading with debouncing. Returns true if stable value changed.
    fn update(&mut self, new_raw_value: bool, now: Instant) -> bool {
        if new_raw_value != self.raw_value {
            // Raw input changed - start debounce timer
            self.raw_value = new_raw_value;
            self.change_start = now;
            false // Not stable yet
        } else if self.raw_value != self.stable_value {
            // Raw value is different from stable value - check if debounce time elapsed
            if now.duration_since(self.change_start).as_millis() >= DEBOUNCE_TIME_MS {
                // Debounce time elapsed - accept the new value
                self.stable_value = self.raw_value;
                true // Stable value changed
            } else {
                false // Still debouncing
            }
        } else {
            false // Raw and stable values match - no change
        }
    }

    fn value(&self) -> bool {
        self.stable_value
    }
}

// ============================================================================
// Sensor Manager
// ============================================================================

/// Manages all sensors with debouncing and change detection
pub struct SensorManager {
    hall_effect: DebouncedReading,
    safety_pin: DebouncedReading,
    last_state: SensorState,
}

impl SensorManager {
    /// Create new sensor manager with initial readings
    pub fn new(hall_effect_pin: &Input<'_>, safety_pin: &Input<'_>) -> Self {
        let now = Instant::now();
        
        // Read initial values (active low)
        let hall_initial = hall_effect_pin.is_low();
        let safety_initial = safety_pin.is_low();

        Self {
            hall_effect: DebouncedReading::new(hall_initial, now),
            safety_pin: DebouncedReading::new(safety_initial, now),
            last_state: SensorState {
                hall_effect_closed: hall_initial,
                safety_pin_pulled: safety_initial,
            },
        }
    }

    /// Update sensor readings and return current state
    /// Returns Some(state) if sensors changed, None if no change
    pub fn update(
        &mut self,
        hall_effect_pin: &Input<'_>,
        safety_pin_pin: &Input<'_>,
    ) -> Option<SensorState> {
        let now = Instant::now();
        
        // Read current values (active low)
        let hall_raw = hall_effect_pin.is_low();
        let safety_raw = safety_pin_pin.is_low();

        // Update debounced readings
        let hall_changed = self.hall_effect.update(hall_raw, now);
        let safety_changed = self.safety_pin.update(safety_raw, now);

        // Check if any sensor became stable with a new value
        if hall_changed || safety_changed {
            let new_state = SensorState {
                hall_effect_closed: self.hall_effect.value(),
                safety_pin_pulled: self.safety_pin.value(),
            };

            if new_state != self.last_state {
                self.last_state = new_state;
                return Some(new_state);
            }
        }

        None
    }

    /// Get current sensor state (last stable reading)
    pub fn current_state(&self) -> SensorState {
        self.last_state
    }
}
