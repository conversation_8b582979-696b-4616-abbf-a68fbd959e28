//! Sensor Abstraction Layer
//!
//! Provides type-safe, debounced access to hardware sensors with proper
//! state tracking and change detection for safety-critical applications.
//!
//! Features:
//! - Debounced sensor readings
//! - Change detection
//! - Type-safe sensor state
//! - Fail-safe defaults

use crate::state_machine::SensorState;
use defmt::Format;
use embassy_stm32::gpio::Input;
use embassy_time::Instant;

// ============================================================================
// Configuration Constants
// ============================================================================

/// Debounce time for sensor readings (prevents noise)
const DEBOUNCE_TIME_MS: u64 = 10;

/// Maximum time between sensor reads before considering it stale
const SENSOR_TIMEOUT_MS: u64 = 1000;

// ============================================================================
// Sensor Types
// ============================================================================

/// Debounced sensor reading with timestamp
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
struct DebouncedReading {
    value: bool,
    stable_since: Instant,
    last_change: Instant,
}

impl DebouncedReading {
    fn new(initial_value: bool, now: Instant) -> Self {
        Self {
            value: initial_value,
            stable_since: now,
            last_change: now,
        }
    }

    /// Update reading with debouncing
    fn update(&mut self, new_value: bool, now: Instant) -> bool {
        if new_value != self.value {
            // Value changed - start debounce timer
            self.last_change = now;
            false // Not stable yet
        } else if now.duration_since(self.last_change).as_millis() >= DEBOUNCE_TIME_MS {
            // Value has been stable for debounce time
            if now.duration_since(self.stable_since).as_millis() >= DEBOUNCE_TIME_MS {
                false // Already stable
            } else {
                self.stable_since = now;
                true // Just became stable
            }
        } else {
            false // Still debouncing
        }
    }

    fn is_stable(&self, now: Instant) -> bool {
        now.duration_since(self.stable_since).as_millis() >= DEBOUNCE_TIME_MS
    }

    fn is_stale(&self, now: Instant) -> bool {
        now.duration_since(self.last_change).as_millis() > SENSOR_TIMEOUT_MS
    }
}

// ============================================================================
// Sensor Manager
// ============================================================================

/// Manages all sensors with debouncing and change detection
pub struct SensorManager {
    hall_effect: DebouncedReading,
    safety_pin: DebouncedReading,
    last_state: SensorState,
}

impl SensorManager {
    /// Create new sensor manager with initial readings
    pub fn new(hall_effect_pin: &Input<'_>, safety_pin: &Input<'_>) -> Self {
        let now = Instant::now();
        
        // Read initial values (active low)
        let hall_initial = hall_effect_pin.is_low();
        let safety_initial = safety_pin.is_low();

        Self {
            hall_effect: DebouncedReading::new(hall_initial, now),
            safety_pin: DebouncedReading::new(safety_initial, now),
            last_state: SensorState {
                hall_effect_closed: hall_initial,
                safety_pin_pulled: safety_initial,
            },
        }
    }

    /// Update sensor readings and return current state
    /// Returns Some(state) if sensors changed, None if no change
    pub fn update(
        &mut self,
        hall_effect_pin: &Input<'_>,
        safety_pin_pin: &Input<'_>,
    ) -> Option<SensorState> {
        let now = Instant::now();
        
        // Read current values (active low)
        let hall_raw = hall_effect_pin.is_low();
        let safety_raw = safety_pin_pin.is_low();

        // Update debounced readings
        let hall_changed = self.hall_effect.update(hall_raw, now);
        let safety_changed = self.safety_pin.update(safety_raw, now);

        // Check if any sensor became stable with a new value
        if hall_changed || safety_changed {
            let new_state = SensorState {
                hall_effect_closed: self.hall_effect.value,
                safety_pin_pulled: self.safety_pin.value,
            };

            if new_state != self.last_state {
                self.last_state = new_state;
                return Some(new_state);
            }
        }

        None
    }

    /// Get current sensor state (last stable reading)
    pub fn current_state(&self) -> SensorState {
        self.last_state
    }

    /// Check if all sensors are providing valid readings
    pub fn is_healthy(&self) -> SensorHealth {
        let now = Instant::now();
        
        let hall_healthy = self.hall_effect.is_stable(now) && !self.hall_effect.is_stale(now);
        let safety_healthy = self.safety_pin.is_stable(now) && !self.safety_pin.is_stale(now);

        match (hall_healthy, safety_healthy) {
            (true, true) => SensorHealth::Healthy,
            (false, true) => SensorHealth::HallEffectFault,
            (true, false) => SensorHealth::SafetyPinFault,
            (false, false) => SensorHealth::MultipleFaults,
        }
    }

    /// Force update sensor state (for testing or emergency)
    pub fn force_update(&mut self, new_state: SensorState) {
        self.last_state = new_state;
        let now = Instant::now();
        self.hall_effect = DebouncedReading::new(new_state.hall_effect_closed, now);
        self.safety_pin = DebouncedReading::new(new_state.safety_pin_pulled, now);
    }
}

// ============================================================================
// Health Monitoring
// ============================================================================

/// Sensor health status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum SensorHealth {
    Healthy,
    HallEffectFault,
    SafetyPinFault,
    MultipleFaults,
}

impl SensorHealth {
    pub fn is_healthy(&self) -> bool {
        matches!(self, SensorHealth::Healthy)
    }

    pub fn has_critical_fault(&self) -> bool {
        matches!(self, SensorHealth::MultipleFaults)
    }
}

// ============================================================================
// Safety Validation
// ============================================================================

/// Validate sensor state for safety-critical operations
pub fn validate_sensor_state(state: SensorState, health: SensorHealth) -> SensorValidation {
    // Check sensor health first
    if !health.is_healthy() {
        return SensorValidation::SensorFault(health);
    }

    // Validate logical consistency
    // Note: Both sensors being active simultaneously might indicate a fault
    if state.hall_effect_closed && state.safety_pin_pulled {
        // This is actually a valid state - device on launcher with safety pin pulled
        SensorValidation::Valid
    } else {
        SensorValidation::Valid
    }
}

/// Sensor validation result
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum SensorValidation {
    Valid,
    SensorFault(SensorHealth),
    LogicalInconsistency,
}

impl SensorValidation {
    pub fn is_valid(&self) -> bool {
        matches!(self, SensorValidation::Valid)
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

/// Create a sensor state for testing
#[cfg(test)]
pub fn test_sensor_state(hall_effect: bool, safety_pin: bool) -> SensorState {
    SensorState {
        hall_effect_closed: hall_effect,
        safety_pin_pulled: safety_pin,
    }
}

/// Format sensor state for logging
pub fn format_sensor_state(state: SensorState) -> &'static str {
    match (state.hall_effect_closed, state.safety_pin_pulled) {
        (false, false) => "Ground/Safe",
        (true, false) => "OnLauncher/Safe", 
        (false, true) => "Ground/Armed",
        (true, true) => "OnLauncher/Armed",
    }
}
