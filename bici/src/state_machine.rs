//! Type-Safe State Machine for Detonator Control
//!
//! This module implements a compile-time safe state machine using Rust's type system.
//! Invalid state transitions are impossible to express, ensuring safety-critical behavior.
//!
//! States: OnGround -> OnLauncher -> ReadyToFly -> InFlight -> Armed -> Detonated
//!
//! Safety Features:
//! - Compile-time transition validation
//! - Sensor-driven automatic transitions
//! - Command validation per state
//! - Fail-safe defaults

use defmt::Format;

// ============================================================================
// Core Types
// ============================================================================

/// Commands that can be received via UART
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum Command {
    PrepareForFlight,
    ArmRequest,
    DisarmRequest,
    DetonateRequest,
    ResetRequest,
}

/// Current sensor readings (continuous properties, not events)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Default, Format)]
pub struct SensorState {
    pub hall_effect_closed: bool,
    pub safety_pin_pulled: bool,
}

/// State machine errors
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum TransitionError {
    InvalidCommand,
    SafetyPinNotPulled,
    AlreadyDetonated,
}

/// State identifiers for protocol communication
#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, Format)]
pub enum StateId {
    OnGround = 0x00,
    OnLauncher = 0x01,
    ReadyToFly = 0x02,
    InFlight = 0x03,
    Armed = 0x04,
    Detonated = 0xFF,
}

// ============================================================================
// Type-Safe State Types
// ============================================================================

/// Device is on ground, not on launcher
#[derive(Debug, Clone, Copy)]
pub struct OnGround {
    sensors: SensorState,
}

/// Device is mounted on launcher (hall effect closed)
#[derive(Debug, Clone, Copy)]
pub struct OnLauncher {
    sensors: SensorState,
}

/// Device is ready for flight (safety pin pulled)
#[derive(Debug, Clone, Copy)]
pub struct ReadyToFly {
    sensors: SensorState,
}

/// Device is in flight (hall effect open after being closed)
#[derive(Debug, Clone, Copy)]
pub struct InFlight {
    sensors: SensorState,
}

/// Device is armed and ready for detonation
#[derive(Debug, Clone, Copy)]
pub struct Armed {
    sensors: SensorState,
}

/// Terminal state - device has detonated
#[derive(Debug, Clone, Copy)]
pub struct Detonated;

// ============================================================================
// Transition Result Types
// ============================================================================

/// Result of sensor update - either same state or transition
pub enum SensorTransition<T> {
    Stay(T),
    Transition(StateMachine),
}

/// Result of command processing
pub enum CommandResult<T> {
    Stay(T),
    Transition(StateMachine),
    Detonated,
}

// ============================================================================
// State Implementations
// ============================================================================

impl OnGround {
    pub fn new(sensors: SensorState) -> Self {
        Self { sensors }
    }

    pub fn sensors(&self) -> SensorState {
        self.sensors
    }

    /// REQUIREMENT: Can transition to OnLauncher when hall effect sensor detects launcher
    fn can_transition_to_launcher(&self, sensors: SensorState) -> bool {
        sensors.hall_effect_closed
    }

    /// Sensor update: transition to OnLauncher if hall effect closes
    pub fn update_sensors(self, sensors: SensorState) -> SensorTransition<Self> {
        if self.can_transition_to_launcher(sensors) {
            SensorTransition::Transition(StateMachine::OnLauncher(OnLauncher { sensors }))
        } else {
            SensorTransition::Stay(Self { sensors })
        }
    }

    /// Commands not accepted in OnGround state
    pub fn handle_command(self, _command: Command) -> Result<CommandResult<Self>, TransitionError> {
        Err(TransitionError::InvalidCommand)
    }
}

impl OnLauncher {
    pub fn sensors(&self) -> SensorState {
        self.sensors
    }

    /// REQUIREMENT: Can return to OnGround when hall effect sensor no longer detects launcher
    fn can_transition_to_ground(&self, sensors: SensorState) -> bool {
        !sensors.hall_effect_closed
    }

    /// REQUIREMENT: Can prepare for flight only when safety pin is pulled
    fn can_prepare_for_flight(&self) -> bool {
        self.sensors.safety_pin_pulled
    }

    /// Sensor update: return to OnGround if hall effect opens
    pub fn update_sensors(self, sensors: SensorState) -> SensorTransition<Self> {
        if self.can_transition_to_ground(sensors) {
            SensorTransition::Transition(StateMachine::OnGround(OnGround::new(sensors)))
        } else {
            SensorTransition::Stay(Self { sensors })
        }
    }

    /// Handle PrepareForFlight command (requires safety pin pulled)
    pub fn handle_command(self, command: Command) -> Result<CommandResult<Self>, TransitionError> {
        match command {
            Command::PrepareForFlight => {
                if self.can_prepare_for_flight() {
                    Ok(CommandResult::Transition(StateMachine::ReadyToFly(
                        ReadyToFly {
                            sensors: self.sensors,
                        },
                    )))
                } else {
                    Err(TransitionError::SafetyPinNotPulled)
                }
            }
            _ => Err(TransitionError::InvalidCommand),
        }
    }
}

impl ReadyToFly {
    pub fn sensors(&self) -> SensorState {
        self.sensors
    }

    /// REQUIREMENT: Can transition to InFlight when hall effect opens (launch detected)
    fn can_transition_to_inflight(&self, sensors: SensorState) -> bool {
        !sensors.hall_effect_closed
    }

    /// REQUIREMENT: Can always reset to OnGround from ReadyToFly
    fn can_reset_to_ground(&self) -> bool {
        true
    }

    /// Sensor update: transition to InFlight when hall effect opens (launch detected)
    pub fn update_sensors(self, sensors: SensorState) -> SensorTransition<Self> {
        if self.can_transition_to_inflight(sensors) {
            SensorTransition::Transition(StateMachine::InFlight(InFlight { sensors }))
        } else {
            SensorTransition::Stay(Self { sensors })
        }
    }

    /// Handle ResetRequest command
    pub fn handle_command(self, command: Command) -> Result<CommandResult<Self>, TransitionError> {
        match command {
            Command::ResetRequest => {
                if self.can_reset_to_ground() {
                    Ok(CommandResult::Transition(StateMachine::OnGround(
                        OnGround::new(self.sensors),
                    )))
                } else {
                    Err(TransitionError::InvalidCommand)
                }
            }
            _ => Err(TransitionError::InvalidCommand),
        }
    }
}

impl InFlight {
    pub fn sensors(&self) -> SensorState {
        self.sensors
    }

    /// REQUIREMENT: Can always arm when in flight (no additional constraints)
    fn can_arm(&self) -> bool {
        true
    }

    /// REQUIREMENT: Can always reset to ground from InFlight
    fn can_reset_to_ground(&self) -> bool {
        true
    }

    /// Sensor update: no automatic transitions in flight
    pub fn update_sensors(self, sensors: SensorState) -> SensorTransition<Self> {
        SensorTransition::Stay(Self { sensors })
    }

    /// Handle ArmRequest and ResetRequest commands
    pub fn handle_command(self, command: Command) -> Result<CommandResult<Self>, TransitionError> {
        match command {
            Command::ArmRequest => {
                if self.can_arm() {
                    Ok(CommandResult::Transition(StateMachine::Armed(Armed {
                        sensors: self.sensors,
                    })))
                } else {
                    Err(TransitionError::InvalidCommand)
                }
            }
            Command::ResetRequest => {
                if self.can_reset_to_ground() {
                    Ok(CommandResult::Transition(StateMachine::OnGround(
                        OnGround::new(self.sensors),
                    )))
                } else {
                    Err(TransitionError::InvalidCommand)
                }
            }
            _ => Err(TransitionError::InvalidCommand),
        }
    }
}

impl Armed {
    pub fn sensors(&self) -> SensorState {
        self.sensors
    }

    /// REQUIREMENT: Can always disarm back to InFlight
    fn can_disarm(&self) -> bool {
        true
    }

    /// REQUIREMENT: Can always detonate when armed (final safety check)
    fn can_detonate(&self) -> bool {
        true
    }

    /// Sensor update: no automatic transitions when armed
    pub fn update_sensors(self, sensors: SensorState) -> SensorTransition<Self> {
        SensorTransition::Stay(Self { sensors })
    }

    /// Handle DisarmRequest and DetonateRequest commands
    pub fn handle_command(self, command: Command) -> Result<CommandResult<Self>, TransitionError> {
        match command {
            Command::DisarmRequest => {
                if self.can_disarm() {
                    Ok(CommandResult::Transition(StateMachine::InFlight(
                        InFlight {
                            sensors: self.sensors,
                        },
                    )))
                } else {
                    Err(TransitionError::InvalidCommand)
                }
            }
            Command::DetonateRequest => {
                if self.can_detonate() {
                    Ok(CommandResult::Detonated)
                } else {
                    Err(TransitionError::InvalidCommand)
                }
            }
            _ => Err(TransitionError::InvalidCommand),
        }
    }
}

impl Detonated {
    /// Terminal state - no transitions possible
    pub fn update_sensors(self, _sensors: SensorState) -> SensorTransition<Self> {
        SensorTransition::Stay(self)
    }

    pub fn handle_command(self, _command: Command) -> Result<CommandResult<Self>, TransitionError> {
        Err(TransitionError::AlreadyDetonated)
    }
}

// ============================================================================
// Runtime State Container
// ============================================================================

/// Runtime container for the type-safe state machine
#[derive(Debug)]
pub enum StateMachine {
    OnGround(OnGround),
    OnLauncher(OnLauncher),
    ReadyToFly(ReadyToFly),
    InFlight(InFlight),
    Armed(Armed),
    Detonated(Detonated),
}

impl StateMachine {
    /// Create new state machine starting in OnGround
    pub fn new() -> Self {
        StateMachine::OnGround(OnGround::new(SensorState::default()))
    }

    /// Get current state ID for protocol
    pub fn state_id(&self) -> StateId {
        match self {
            StateMachine::OnGround(_) => StateId::OnGround,
            StateMachine::OnLauncher(_) => StateId::OnLauncher,
            StateMachine::ReadyToFly(_) => StateId::ReadyToFly,
            StateMachine::InFlight(_) => StateId::InFlight,
            StateMachine::Armed(_) => StateId::Armed,
            StateMachine::Detonated(_) => StateId::Detonated,
        }
    }

    /// Get current sensor state
    pub fn sensors(&self) -> SensorState {
        match self {
            StateMachine::OnGround(s) => s.sensors(),
            StateMachine::OnLauncher(s) => s.sensors(),
            StateMachine::ReadyToFly(s) => s.sensors(),
            StateMachine::InFlight(s) => s.sensors(),
            StateMachine::Armed(s) => s.sensors(),
            StateMachine::Detonated(_) => SensorState::default(),
        }
    }

    /// Update sensor readings and check for automatic transitions
    /// Returns true if state changed
    pub fn update_sensors(&mut self, new_sensors: SensorState) -> bool {
        let old_state_id = self.state_id();

        *self = match core::mem::take(self) {
            StateMachine::OnGround(state) => match state.update_sensors(new_sensors) {
                SensorTransition::Stay(s) => StateMachine::OnGround(s),
                SensorTransition::Transition(new_state) => new_state,
            },
            StateMachine::OnLauncher(state) => match state.update_sensors(new_sensors) {
                SensorTransition::Stay(s) => StateMachine::OnLauncher(s),
                SensorTransition::Transition(new_state) => new_state,
            },
            StateMachine::ReadyToFly(state) => match state.update_sensors(new_sensors) {
                SensorTransition::Stay(s) => StateMachine::ReadyToFly(s),
                SensorTransition::Transition(new_state) => new_state,
            },
            StateMachine::InFlight(state) => match state.update_sensors(new_sensors) {
                SensorTransition::Stay(s) => StateMachine::InFlight(s),
                SensorTransition::Transition(new_state) => new_state,
            },
            StateMachine::Armed(state) => match state.update_sensors(new_sensors) {
                SensorTransition::Stay(s) => StateMachine::Armed(s),
                SensorTransition::Transition(new_state) => new_state,
            },
            StateMachine::Detonated(state) => match state.update_sensors(new_sensors) {
                SensorTransition::Stay(s) => StateMachine::Detonated(s),
                SensorTransition::Transition(new_state) => new_state,
            },
        };

        self.state_id() != old_state_id
    }

    /// Handle a command and potentially transition
    /// Returns Ok(true) if state changed, Ok(false) if no change, Err for invalid commands
    pub fn handle_command(&mut self, command: Command) -> Result<bool, TransitionError> {
        let old_state_id = self.state_id();

        *self = match core::mem::take(self) {
            StateMachine::OnGround(state) => {
                match state.handle_command(command)? {
                    CommandResult::Stay(s) => StateMachine::OnGround(s),
                    CommandResult::Transition(new_state) => new_state,
                    CommandResult::Detonated => StateMachine::Detonated(Detonated),
                }
            }
            StateMachine::OnLauncher(state) => {
                match state.handle_command(command)? {
                    CommandResult::Stay(s) => StateMachine::OnLauncher(s),
                    CommandResult::Transition(new_state) => new_state,
                    CommandResult::Detonated => StateMachine::Detonated(Detonated),
                }
            }
            StateMachine::ReadyToFly(state) => {
                match state.handle_command(command)? {
                    CommandResult::Stay(s) => StateMachine::ReadyToFly(s),
                    CommandResult::Transition(new_state) => new_state,
                    CommandResult::Detonated => StateMachine::Detonated(Detonated),
                }
            }
            StateMachine::InFlight(state) => {
                match state.handle_command(command)? {
                    CommandResult::Stay(s) => StateMachine::InFlight(s),
                    CommandResult::Transition(new_state) => new_state,
                    CommandResult::Detonated => StateMachine::Detonated(Detonated),
                }
            }
            StateMachine::Armed(state) => {
                match state.handle_command(command)? {
                    CommandResult::Stay(s) => StateMachine::Armed(s),
                    CommandResult::Transition(new_state) => new_state,
                    CommandResult::Detonated => StateMachine::Detonated(Detonated),
                }
            }
            StateMachine::Detonated(state) => {
                match state.handle_command(command)? {
                    CommandResult::Stay(s) => StateMachine::Detonated(s),
                    CommandResult::Transition(new_state) => new_state,
                    CommandResult::Detonated => StateMachine::Detonated(Detonated),
                }
            }
        };

        Ok(self.state_id() != old_state_id)
    }
}

impl Default for StateMachine {
    fn default() -> Self {
        Self::new()
    }
}
