#![no_std]
#![no_main]

//! Detonator State Machine - Main Application
//!
//! Safety-critical embedded system for explosive device control.
//! Implements type-safe state machine with multiple safety barriers.
//!
//! Architecture:
//! - Type-safe state machine with compile-time transition validation
//! - Sensor monitoring with debouncing and health checks
//! - UART protocol with CRC validation and COBS encoding
//! - Async task-based design using Embassy framework
//!
//! Safety Features:
//! - Multiple independent safety checks
//! - Fail-safe defaults
//! - Sensor validation and health monitoring
//! - Protocol integrity validation

mod protocol;
mod sensors;
mod state_machine;

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Input, Pull};
use embassy_stm32::mode::Async;
use embassy_stm32::usart::{Config, Uart, UartRx, UartTx};
use embassy_stm32::{bind_interrupts, peripherals, usart};
use embassy_sync::blocking_mutex::raw::ThreadModeRawMutex;
use embassy_sync::channel::Channel;
use embassy_time::{Duration, Timer};
use heapless::Vec;
use {defmt_rtt as _, panic_probe as _};

use protocol::{Protocol, MAX_ENCODED_SIZE};
use sensors::SensorManager;
use state_machine::{Command, SensorState, StateMachine, StateId};

// ============================================================================
// Hardware Configuration
// ============================================================================

bind_interrupts!(struct Irqs {
    USART1 => usart::InterruptHandler<peripherals::USART1>;
});

// ============================================================================
// Communication Channels
// ============================================================================

/// Commands received from UART
static COMMAND_CHANNEL: Channel<ThreadModeRawMutex, Command, 8> = Channel::new();

/// Sensor state updates
static SENSOR_CHANNEL: Channel<ThreadModeRawMutex, SensorState, 8> = Channel::new();

/// Outgoing UART messages
static TX_CHANNEL: Channel<ThreadModeRawMutex, Vec<u8, MAX_ENCODED_SIZE>, 4> = Channel::new();

/// State machine status updates for heartbeat
static STATUS_CHANNEL: Channel<ThreadModeRawMutex, (StateId, SensorState), 4> = Channel::new();

// ============================================================================
// Main Application Entry Point
// ============================================================================

#[embassy_executor::main]
async fn main(spawner: Spawner) -> ! {
    // Initialize hardware
    let p = embassy_stm32::init(Default::default());
    info!("=== Detonator State Machine Starting ===");

    // Initialize UART for communication
    let uart_config = Config::default();
    let uart = Uart::new(
        p.USART1,
        p.PA1,  // TX
        p.PA2,  // RX
        Irqs,
        p.GPDMA1_CH0,
        p.GPDMA1_CH1,
        uart_config,
    ).unwrap();

    let (uart_tx, uart_rx) = uart.split();

    let hall_effect_pin = Input::new(p.PA3, Pull::None);
    let safety_pin = Input::new(p.PA4, Pull::None);

    info!("Hardware initialized successfully");

    // Spawn all tasks
    spawner.spawn(uart_receiver_task(uart_rx)).unwrap();
    spawner.spawn(uart_transmitter_task(uart_tx)).unwrap();
    spawner.spawn(sensor_monitoring_task(hall_effect_pin, safety_pin)).unwrap();
    spawner.spawn(state_machine_task()).unwrap();
    spawner.spawn(heartbeat_task()).unwrap();
    spawner.spawn(system_monitor_task()).unwrap();

    info!("All tasks spawned successfully");
    info!("=== System Ready ===");

    // Main loop - just keep the system alive
    loop {
        Timer::after(Duration::from_secs(30)).await;
        info!("System heartbeat - all tasks running");
    }
}

// ============================================================================
// UART Communication Tasks
// ============================================================================

/// Receives and decodes UART messages
#[embassy_executor::task]
async fn uart_receiver_task(mut uart_rx: UartRx<'static, Async>) {
    let protocol = Protocol::new();
    let mut frame_buffer = Vec::<u8, MAX_ENCODED_SIZE>::new();

    info!("UART receiver task started");

    loop {
        let mut byte_buffer = [0u8; 1];

        // Read one byte at a time
        if uart_rx.read(&mut byte_buffer).await.is_ok() {
            let byte = byte_buffer[0];

            if byte == 0x00 && !frame_buffer.is_empty() {
                // Frame terminator received - process complete frame
                frame_buffer.push(0x00).ok();

                match protocol.decode_command(&frame_buffer) {
                    Ok(command) => {
                        info!("Received command: {:?}", command);
                        COMMAND_CHANNEL.send(command).await;
                    }
                    Err(error) => {
                        warn!("Protocol decode error: {:?}", error);
                        // Continue processing - don't let bad frames stop us
                    }
                }

                frame_buffer.clear();
            } else if byte != 0x00 {
                // Accumulate frame data
                if frame_buffer.push(byte).is_err() {
                    warn!("Frame buffer overflow - dropping frame");
                    frame_buffer.clear();
                }
            }
            // Ignore multiple zero bytes (frame separators)
        }
    }
}

/// Transmits encoded UART messages
#[embassy_executor::task]
async fn uart_transmitter_task(mut uart_tx: UartTx<'static, Async>) {
    info!("UART transmitter task started");

    loop {
        let message = TX_CHANNEL.receive().await;

        match uart_tx.write(&message).await {
            Ok(_) => {
                // Message sent successfully
            }
            Err(_) => {
                warn!("UART transmission failed");
                // Continue - don't stop on transmission errors
            }
        }
    }
}

// ============================================================================
// Sensor Monitoring Task
// ============================================================================

/// Monitors sensors with debouncing and health checking
#[embassy_executor::task]
async fn sensor_monitoring_task(
    hall_effect_pin: Input<'static>,
    safety_pin: Input<'static>,
) {
    let mut sensor_manager = SensorManager::new(&hall_effect_pin, &safety_pin);
    
    info!("Sensor monitoring task started");
    info!("Initial sensor state: {:?}", sensor_manager.current_state());

    // Send initial sensor state
    SENSOR_CHANNEL.send(sensor_manager.current_state()).await;

    loop {
        Timer::after(Duration::from_millis(20)).await; // 50Hz sensor polling

        // Update sensors and check for changes
        if let Some(new_state) = sensor_manager.update(&hall_effect_pin, &safety_pin) {
            info!("Sensor change: {:?}", new_state);
            SENSOR_CHANNEL.send(new_state).await;
        }
    }
}

// ============================================================================
// State Machine Task
// ============================================================================

/// Main state machine processing task
#[embassy_executor::task]
async fn state_machine_task() {
    let mut state_machine = StateMachine::new();

    info!("State machine task started");
    info!("Initial state: {:?}", state_machine.state_id());

    // Send initial status
    STATUS_CHANNEL.send((state_machine.state_id(), state_machine.sensors())).await;

    loop {
        // Wait for either sensor update or command
        match embassy_futures::select::select(
            SENSOR_CHANNEL.receive(),
            COMMAND_CHANNEL.receive(),
        ).await {
            embassy_futures::select::Either::First(sensor_state) => {
                // Sensor update received
                let state_changed = state_machine.update_sensors(sensor_state);

                if state_changed {
                    let new_state_id = state_machine.state_id();
                    info!("State changed due to sensors: {:?}", new_state_id);

                    // Notify heartbeat task
                    STATUS_CHANNEL.send((new_state_id, state_machine.sensors())).await;
                }
            }
            embassy_futures::select::Either::Second(command) => {
                // Command received
                match state_machine.handle_command(command) {
                    Ok(state_changed) => {
                        if state_changed {
                            let new_state_id = state_machine.state_id();
                            info!("State changed due to command {:?}: {:?}", command, new_state_id);

                            // Notify heartbeat task
                            STATUS_CHANNEL.send((new_state_id, state_machine.sensors())).await;
                        } else {
                            info!("Command {:?} processed - no state change", command);
                        }
                    }
                    Err(error) => {
                        warn!("Command {:?} rejected: {:?}", command, error);
                    }
                }
            }
        }
    }
}

// ============================================================================
// Heartbeat Task
// ============================================================================

/// Sends periodic status updates via UART
#[embassy_executor::task]
async fn heartbeat_task() {
    let mut protocol = Protocol::new();
    let mut current_state = StateId::OnGround;
    let mut current_sensors = SensorState::default();

    info!("Heartbeat task started (100ms interval)");

    loop {
        // Check for status updates (non-blocking)
        if let Ok((state, sensors)) = STATUS_CHANNEL.try_receive() {
            current_state = state;
            current_sensors = sensors;
        }

        // Encode and send heartbeat
        let heartbeat = protocol.encode_heartbeat(
            current_state,
            current_sensors.hall_effect_closed,
            current_sensors.safety_pin_pulled,
        );

        TX_CHANNEL.send(heartbeat).await;

        Timer::after(Duration::from_millis(100)).await;
    }
}

// ============================================================================
// System Monitor Task
// ============================================================================

/// Monitors overall system health and performance
#[embassy_executor::task]
async fn system_monitor_task() {
    info!("System monitor task started");

    loop {
        Timer::after(Duration::from_secs(10)).await;

        // Check channel health (basic monitoring)
        let command_capacity = COMMAND_CHANNEL.capacity() - COMMAND_CHANNEL.len();
        let sensor_capacity = SENSOR_CHANNEL.capacity() - SENSOR_CHANNEL.len();
        let tx_capacity = TX_CHANNEL.capacity() - TX_CHANNEL.len();

        if command_capacity < 2 || sensor_capacity < 2 || tx_capacity < 1 {
            warn!("Channel congestion detected - CMD:{} SENS:{} TX:{}", 
                  command_capacity, sensor_capacity, tx_capacity);
        }

        debug!("System monitor: channels healthy");
    }
}
