# State Transition Requirements

This document clearly defines all state transition requirements for easy auditing.

## State Transition Matrix

| From State   | To State     | Trigger Type | Requirements |
|-------------|-------------|-------------|-------------|
| OnGround    | OnLauncher  | Sensor      | Hall effect sensor detects launcher |
| OnLauncher  | OnGround    | Sensor      | Hall effect sensor no longer detects launcher |
| OnLauncher  | ReadyToFly  | Command     | PrepareForFlight command + Safety pin pulled |
| ReadyToFly  | InFlight    | Sensor      | Hall effect opens (launch detected) |
| ReadyToFly  | OnGround    | Command     | ResetRequest command (always allowed) |
| InFlight    | Armed       | Command     | ArmRequest command (always allowed) |
| InFlight    | OnGround    | Command     | ResetRequest command (always allowed) |
| Armed       | InFlight    | Command     | DisarmRequest command (always allowed) |
| Armed       | Detonated   | Command     | DetonateRequest command (always allowed) |
| Detonated   | (none)      | -           | Terminal state - no transitions |

## Sensor Requirements

### Hall Effect Sensor
- **Purpose**: Detect when device is on launcher
- **OnGround → OnLauncher**: Must be `true` (closed/detected)
- **OnLauncher → OnGround**: Must be `false` (open/not detected)
- **ReadyToFly → InFlight**: Must be `false` (launch detected)

### Safety Pin
- **Purpose**: Physical safety mechanism
- **OnLauncher → ReadyToFly**: Must be `true` (pulled)
- **All other transitions**: No requirement (sensor state tracked but not enforced)

## Command Requirements

### PrepareForFlight
- **Valid from**: OnLauncher only
- **Requirements**: Safety pin must be pulled
- **Result**: Transition to ReadyToFly

### ArmRequest
- **Valid from**: InFlight only
- **Requirements**: None (always allowed in flight)
- **Result**: Transition to Armed

### DisarmRequest
- **Valid from**: Armed only
- **Requirements**: None (always allowed)
- **Result**: Transition to InFlight

### DetonateRequest
- **Valid from**: Armed only
- **Requirements**: None (final safety check passed)
- **Result**: Transition to Detonated (terminal)

### ResetRequest
- **Valid from**: ReadyToFly, InFlight
- **Requirements**: None (always allowed)
- **Result**: Transition to OnGround

## Safety Notes

1. **Physical Safety**: Safety pin provides physical interlock
2. **Sensor Validation**: All sensor readings are debounced (10ms)
3. **Command Validation**: Invalid commands are rejected with specific error codes
4. **Terminal State**: Detonated state has no exit transitions
5. **Type Safety**: Rust's type system prevents invalid state transitions at compile time

## Audit Points

Each state implementation contains explicit `can_*` methods that encode the requirements:
- `can_transition_to_launcher()` - Hall effect requirement
- `can_transition_to_ground()` - Hall effect requirement  
- `can_prepare_for_flight()` - Safety pin requirement
- `can_transition_to_inflight()` - Launch detection requirement
- `can_arm()` - No additional requirements in flight
- `can_disarm()` - Always allowed when armed
- `can_detonate()` - Final safety check (always true in this prototype)
- `can_reset_to_ground()` - Always allowed from safe states

These methods make the transition logic easily auditable and modifiable.
