[package]
edition = "2021"
name = "bici"
version = "0.1.0"
publish = false


[dependencies]
# Change stm32h563zi to your chip name, if necessary.
embassy-stm32 = { version = "0.4.0", features = ["defmt", "stm32h503cb", "memory-x", "time-driver-any", "exti", "unstable-pac", "low-power"] }
embassy-sync = { version = "0.7.2",  features = ["defmt"] }
embassy-executor = { version = "0.9.1", features = ["arch-cortex-m", "executor-thread", "defmt"] }
embassy-time = { version = "0.5.0", features = ["defmt", "defmt-timestamp-uptime", "tick-hz-32_768"] }
embassy-net = { version = "0.7.1",  features = ["defmt", "tcp", "dhcpv4", "medium-ethernet", "proto-ipv6"] }
embassy-usb = { version = "0.5.1",  features = ["defmt"] }
embassy-futures = { version = "0.1.2" }

defmt = "1.0.1"
defmt-rtt = "1.0.0"

cortex-m = { version = "0.7.7", features = ["inline-asm", "critical-section-single-core"] }
cortex-m-rt = "0.7.5"
embedded-hal = "0.2.7"
embedded-hal-1 = { package = "embedded-hal", version = "1.0" }
embedded-hal-async = { version = "1.0" }
embedded-io-async = { version = "0.6.1" }
embedded-nal-async = "0.8.0"
panic-probe = { version = "1.0.0", features = ["print-defmt"] }
heapless = { version = "0.8", default-features = false }
critical-section = "1.2"
micromath = "2.1.0"
stm32-fmc = "0.3.2"
embedded-storage = "0.3.1"
static_cell = "2"
crc = "3.3.0"
cobs = { version = "0.4.0", default-features = false }

# cargo build/run
[profile.dev]
codegen-units = 1
debug = 2
debug-assertions = true # <-
incremental = false
opt-level = 3 # <-
overflow-checks = true # <-

# cargo test
[profile.test]
codegen-units = 1
debug = 2
debug-assertions = true # <-
incremental = false
opt-level = 3 # <-
overflow-checks = true # <-

# cargo build/run --release
[profile.release]
codegen-units = 1
debug = 2
debug-assertions = false # <-
incremental = false
lto = 'fat'
opt-level = 3 # <-
overflow-checks = false # <-

# cargo test --release
[profile.bench]
codegen-units = 1
debug = 2
debug-assertions = false # <-
incremental = false
lto = 'fat'
opt-level = 3 # <-
overflow-checks = false # <-
