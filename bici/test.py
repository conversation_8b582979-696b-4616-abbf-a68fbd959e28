#!/usr/bin/env python3
"""
State Machine UART Controller

Communicates with the embedded state machine over UART using the defined protocol:
- Heartbeat messages (0x01): Shows current state and sensor status
- State change messages (0x02): Notification of state transitions  
- Commands (0x10-0x14): Send transition requests

Protocol: [MSG_TYPE][PAYLOAD][CRC8] -> COBS_ENCODE -> [DATA][0x00]
"""

import serial
import struct
import time
import threading
from enum import IntEnum
from typing import Optional, Tuple, List
import argparse

class MessageType(IntEnum):
    HEARTBEAT = 0x01
    STATE_CHANGE = 0x02
    PREPARE_FOR_FLIGHT = 0x10
    ARM_REQUEST = 0x11
    DISARM_REQUEST = 0x12
    DETONATE_REQUEST = 0x13
    RESET_REQUEST = 0x14

class StateId(IntEnum):
    ON_GROUND = 0x00
    ON_LAUNCHER = 0x01
    READY_TO_FLY = 0x02
    IN_FLIGHT = 0x03
    ARMED = 0x04
    DETONATED = 0xFF

class UartController:
    def __init__(self, port: str = "/dev/ttyUSB0", baudrate: int = 115200):
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        self.last_heartbeat = None
        self.heartbeat_count = 0
        self.state_changes = []
        
        # Statistics
        self.stats = {
            'heartbeats_received': 0,
            'state_changes_received': 0,
            'commands_sent': 0,
            'crc_errors': 0,
            'decode_errors': 0
        }
    
    def connect(self) -> bool:
        """Connect to the UART device"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            print(f"Connected to {self.port} at {self.baudrate} baud")
            return True
        except Exception as e:
            print(f"Failed to connect: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from UART"""
        self.running = False
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("Disconnected")
    
    def crc8(self, data: bytes) -> int:
        """Calculate CRC8 using CRC-8-DVB-S2 polynomial (0xD5)"""
        POLY = 0xD5
        crc = 0
        
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = ((crc << 1) ^ POLY) & 0xFF
                else:
                    crc = (crc << 1) & 0xFF
        
        return crc
    
    def cobs_encode(self, data: bytes) -> bytes:
        """COBS encode the data and add frame delimiter"""
        encoded = bytearray()
        code_ptr = 0
        code = 1
        
        encoded.append(0)  # Placeholder for first code
        
        for byte in data:
            if byte != 0:
                encoded.append(byte)
                code += 1
            
            if byte == 0 or code == 0xFF:
                encoded[code_ptr] = code
                code_ptr = len(encoded)
                encoded.append(0)  # Placeholder
                code = 1
        
        encoded[code_ptr] = code
        encoded.append(0)  # Frame delimiter
        
        return bytes(encoded)
    
    def cobs_decode(self, data: bytes) -> Optional[bytes]:
        """COBS decode the data"""
        if not data or data[-1] != 0:
            return None
            
        decoded = bytearray()
        i = 0
        
        while i < len(data) - 1:  # Skip the final delimiter
            code = data[i]
            if code == 0:
                break
                
            i += 1
            for _ in range(1, code):
                if i >= len(data):
                    return None
                decoded.append(data[i])
                i += 1
            
            if code != 0xFF and i < len(data) - 1:
                decoded.append(0)
        
        return bytes(decoded)
    
    def encode_command(self, msg_type: MessageType) -> bytes:
        """Encode a command message"""
        frame = bytearray([msg_type])
        crc = self.crc8(frame)
        frame.append(crc)
        
        return self.cobs_encode(frame)
    
    def decode_message(self, encoded_data: bytes) -> Optional[dict]:
        """Decode an incoming message"""
        try:
            # COBS decode
            frame = self.cobs_decode(encoded_data)
            if not frame or len(frame) < 2:
                self.stats['decode_errors'] += 1
                return None
            
            # Verify CRC
            message_data = frame[:-1]
            received_crc = frame[-1]
            expected_crc = self.crc8(message_data)
            
            if received_crc != expected_crc:
                self.stats['crc_errors'] += 1
                print(f"CRC error: expected {expected_crc:02x}, got {received_crc:02x}")
                return None
            
            # Parse message
            msg_type = MessageType(message_data[0])
            
            if msg_type == MessageType.HEARTBEAT and len(message_data) >= 4:
                state = StateId(message_data[1])
                flags = message_data[2]
                sequence = message_data[3]
                
                hall_effect = bool(flags & 0x01)
                safety_pin = bool(flags & 0x02)
                
                return {
                    'type': 'heartbeat',
                    'state': state,
                    'hall_effect_closed': hall_effect,
                    'safety_pin_pulled': safety_pin,
                    'sequence': sequence
                }
                
            elif msg_type == MessageType.STATE_CHANGE and len(message_data) >= 4:
                from_state = StateId(message_data[1])
                to_state = StateId(message_data[2])
                sequence = message_data[3]
                
                return {
                    'type': 'state_change',
                    'from_state': from_state,
                    'to_state': to_state,
                    'sequence': sequence
                }
            
        except Exception as e:
            self.stats['decode_errors'] += 1
            print(f"Decode error: {e}")
            return None
        
        return None
    
    def send_command(self, command: MessageType) -> bool:
        """Send a command to the device"""
        if not self.ser or not self.ser.is_open:
            print("Not connected")
            return False
        
        try:
            encoded = self.encode_command(command)
            self.ser.write(encoded)
            self.ser.flush()
            self.stats['commands_sent'] += 1
            print(f"Sent command: {command.name}")
            return True
        except Exception as e:
            print(f"Failed to send command: {e}")
            return False
    
    def read_messages(self):
        """Background thread to read incoming messages"""
        frame_buffer = bytearray()
        
        while self.running and self.ser and self.ser.is_open:
            try:
                if self.ser.in_waiting > 0:
                    byte = self.ser.read(1)
                    if len(byte) == 0:
                        continue
                        
                    b = byte[0]
                    
                    if b == 0 and len(frame_buffer) > 0:
                        # End of frame
                        frame_buffer.append(0)
                        message = self.decode_message(bytes(frame_buffer))
                        
                        if message:
                            self.process_message(message)
                        
                        frame_buffer.clear()
                    elif b != 0:
                        frame_buffer.append(b)
                
                time.sleep(0.001)  # Small delay to prevent busy waiting
                
            except Exception as e:
                print(f"Read error: {e}")
                break
    
    def process_message(self, message: dict):
        """Process a decoded message"""
        if message['type'] == 'heartbeat':
            self.last_heartbeat = message
            self.stats['heartbeats_received'] += 1
            
            if self.stats['heartbeats_received'] % 10 == 0:  # Print every 10th heartbeat
                self.print_status()
                
        elif message['type'] == 'state_change':
            self.state_changes.append(message)
            self.stats['state_changes_received'] += 1
            
            print(f"\n🔄 STATE CHANGE: {message['from_state'].name} -> {message['to_state'].name}")
            print(f"   Sequence: {message['sequence']}")
    
    def print_status(self):
        """Print current status"""
        if not self.last_heartbeat:
            print("No heartbeat received yet...")
            return
        
        hb = self.last_heartbeat
        state_name = hb['state'].name
        hall_status = "CLOSED" if hb['hall_effect_closed'] else "OPEN"
        safety_status = "PULLED" if hb['safety_pin_pulled'] else "INSERTED"
        
        print(f"\n📡 STATUS: {state_name} | Hall: {hall_status} | Safety: {safety_status} | Seq: {hb['sequence']}")
    
    def print_statistics(self):
        """Print communication statistics"""
        print("\n📊 STATISTICS:")
        for key, value in self.stats.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
    
    def start_monitoring(self):
        """Start the message monitoring thread"""
        self.running = True
        self.monitor_thread = threading.Thread(target=self.read_messages, daemon=True)
        self.monitor_thread.start()
    
    def interactive_mode(self):
        """Run interactive command mode"""
        commands = {
            '1': (MessageType.PREPARE_FOR_FLIGHT, "Prepare for Flight"),
            '2': (MessageType.ARM_REQUEST, "Arm Request"),
            '3': (MessageType.DISARM_REQUEST, "Disarm Request"),
            '4': (MessageType.DETONATE_REQUEST, "Detonate Request"),
            '5': (MessageType.RESET_REQUEST, "Reset Request"),
            's': (None, "Show Status"),
            'stats': (None, "Show Statistics"),
            'q': (None, "Quit")
        }
        
        print("\n🎮 INTERACTIVE MODE")
        print("Commands:")
        for key, (_, desc) in commands.items():
            print(f"  {key}: {desc}")
        
        try:
            while self.running:
                cmd = input("\nEnter command: ").strip().lower()
                
                if cmd == 'q':
                    break
                elif cmd == 's':
                    self.print_status()
                elif cmd == 'stats':
                    self.print_statistics()
                elif cmd in commands and commands[cmd][0] is not None:
                    self.send_command(commands[cmd][0])
                else:
                    print("Unknown command")
                    
        except KeyboardInterrupt:
            pass

def main():
    parser = argparse.ArgumentParser(description='State Machine UART Controller')
    parser.add_argument('--port', default='/dev/ttyUSB0', help='Serial port device')
    parser.add_argument('--baud', type=int, default=115200, help='Baud rate')
    parser.add_argument('--monitor', action='store_true', help='Monitor mode only (no interactive)')
    parser.add_argument('--command', choices=['prep', 'arm', 'disarm', 'detonate', 'reset'], 
                       help='Send single command and exit')
    
    args = parser.parse_args()
    
    controller = UartController(args.port, args.baud)
    
    if not controller.connect():
        return 1
    
    try:
        controller.start_monitoring()
        
        if args.command:
            # Send single command
            command_map = {
                'prep': MessageType.PREPARE_FOR_FLIGHT,
                'arm': MessageType.ARM_REQUEST,
                'disarm': MessageType.DISARM_REQUEST,
                'detonate': MessageType.DETONATE_REQUEST,
                'reset': MessageType.RESET_REQUEST
            }
            controller.send_command(command_map[args.command])
            time.sleep(2)  # Wait for response
            
        elif args.monitor:
            # Monitor mode
            print("Monitoring mode - Press Ctrl+C to exit")
            while True:
                time.sleep(0.1)
                
        else:
            # Interactive mode
            controller.interactive_mode()
            
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        controller.disconnect()
    
    return 0

if __name__ == "__main__":
    exit(main())