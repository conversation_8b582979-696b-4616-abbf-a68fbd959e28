#!/usr/bin/env python3
"""
Simple UART test script for BICI detonator protocol testing.
Shows heartbeats and sends commands via keypresses.
"""

import serial
import threading
import time
import sys
import select
import tty
import termios

# Protocol constants
MAX_FRAME_SIZE = 8
CRC_POLYNOMIAL = 0xD5

# Message types
MSG_HEARTBEAT = 0x01
MSG_PREPARE_FOR_FLIGHT = 0x10
MSG_ARM_REQUEST = 0x20
MSG_DISARM_REQUEST = 0x21
MSG_DETONATE_REQUEST = 0x22
MSG_RESET_REQUEST = 0x30

# State IDs
STATE_ON_GROUND = 0x00
STATE_ON_LAUNCHER = 0x01
STATE_READY_TO_FLY = 0x02
STATE_IN_FLIGHT = 0x03
STATE_ARMED = 0x04
STATE_DETONATED = 0xFF

STATE_NAMES = {
    STATE_ON_GROUND: "OnGround",
    STATE_ON_LAUNCHER: "OnLauncher", 
    STATE_READY_TO_FLY: "ReadyToFly",
    STATE_IN_FLIGHT: "InFlight",
    STATE_ARMED: "Armed",
    STATE_DETONATED: "Detonated"
}

def crc8(data):
    """Calculate CRC8 with polynomial 0xD5"""
    crc = 0
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ CRC_POLYNOMIAL
            else:
                crc <<= 1
            crc &= 0xFF
    return crc

def cobs_encode(data):
    """COBS encode data"""
    encoded = []
    code_ptr = 0
    code = 1
    
    encoded.append(0)  # Placeholder for first code
    
    for byte in data:
        if byte == 0:
            encoded[code_ptr] = code
            code_ptr = len(encoded)
            encoded.append(0)
            code = 1
        else:
            encoded.append(byte)
            code += 1
            if code == 0xFF:
                encoded[code_ptr] = code
                code_ptr = len(encoded)
                encoded.append(0)
                code = 1
    
    encoded[code_ptr] = code
    encoded.append(0)  # Frame delimiter
    return bytes(encoded)

def cobs_decode(data):
    """COBS decode data"""
    if not data or data[-1] != 0:
        return None
    
    data = data[:-1]  # Remove delimiter
    decoded = []
    i = 0
    
    while i < len(data):
        code = data[i]
        if code == 0:
            return None
        
        for j in range(1, code):
            if i + j >= len(data):
                return None
            decoded.append(data[i + j])
        
        if code < 0xFF and i + code < len(data):
            decoded.append(0)
        
        i += code
    
    return bytes(decoded)

def create_command_frame(command):
    """Create a command frame"""
    frame = bytes([command])
    frame_with_crc = frame + bytes([crc8(frame)])
    return cobs_encode(frame_with_crc)

def parse_heartbeat(data):
    """Parse heartbeat message"""
    if len(data) < 5:
        return None
    
    # Verify CRC
    if crc8(data[:-1]) != data[-1]:
        return None
    
    msg_type = data[0]
    if msg_type != MSG_HEARTBEAT:
        return None
    
    state = data[1]
    sensor_flags = data[2]
    sequence = data[3]
    
    hall_effect = bool(sensor_flags & 0x01)
    safety_pin = bool(sensor_flags & 0x02)
    
    return {
        'state': state,
        'state_name': STATE_NAMES.get(state, f"Unknown({state:02X})"),
        'hall_effect': hall_effect,
        'safety_pin': safety_pin,
        'sequence': sequence
    }

def uart_reader(ser):
    """Read and parse UART data"""
    buffer = b''
    
    while True:
        try:
            data = ser.read(1)
            if not data:
                continue
            
            buffer += data
            
            # Look for frame delimiter (0x00)
            while b'\x00' in buffer:
                frame_end = buffer.find(b'\x00') + 1
                frame = buffer[:frame_end]
                buffer = buffer[frame_end:]
                
                # Decode COBS
                decoded = cobs_decode(frame)
                if decoded:
                    heartbeat = parse_heartbeat(decoded)
                    if heartbeat:
                        print(f"\rHeartbeat: {heartbeat['state_name']} | "
                              f"Hall: {'ON' if heartbeat['hall_effect'] else 'OFF'} | "
                              f"Safety: {'PULLED' if heartbeat['safety_pin'] else 'SAFE'} | "
                              f"Seq: {heartbeat['sequence']:03d}", end='', flush=True)
        except:
            pass

def get_char():
    """Get single character from stdin"""
    fd = sys.stdin.fileno()
    old_settings = termios.tcgetattr(fd)
    try:
        tty.setraw(sys.stdin.fileno())
        ch = sys.stdin.read(1)
    finally:
        termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
    return ch

def main():
    # Open serial port
    ser = serial.Serial('/dev/ttyUSB0', 115200, timeout=0.1)
    
    # Start UART reader thread
    reader_thread = threading.Thread(target=uart_reader, args=(ser,), daemon=True)
    reader_thread.start()
    
    print("BICI UART Test Tool")
    print("Commands:")
    print("  p - PrepareForFlight")
    print("  a - ArmRequest") 
    print("  d - DisarmRequest")
    print("  x - DetonateRequest")
    print("  r - ResetRequest")
    print("  q - Quit")
    print("\nListening for heartbeats...")
    
    while True:
        try:
            ch = get_char()
            
            if ch == 'q':
                break
            elif ch == 'p':
                frame = create_command_frame(MSG_PREPARE_FOR_FLIGHT)
                ser.write(frame)
                print(f"\nSent: PrepareForFlight")
            elif ch == 'a':
                frame = create_command_frame(MSG_ARM_REQUEST)
                ser.write(frame)
                print(f"\nSent: ArmRequest")
            elif ch == 'd':
                frame = create_command_frame(MSG_DISARM_REQUEST)
                ser.write(frame)
                print(f"\nSent: DisarmRequest")
            elif ch == 'x':
                frame = create_command_frame(MSG_DETONATE_REQUEST)
                ser.write(frame)
                print(f"\nSent: DetonateRequest")
            elif ch == 'r':
                frame = create_command_frame(MSG_RESET_REQUEST)
                ser.write(frame)
                print(f"\nSent: ResetRequest")
                
        except KeyboardInterrupt:
            break
    
    ser.close()
    print("\nExiting...")

if __name__ == "__main__":
    main()
